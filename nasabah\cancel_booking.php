<?php
// Process cancel booking request
require_once '../includes/functions.php';
require_once '../includes/db.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if request is AJAX
if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
    // Get booking ID
    $booking_id = isset($_POST['booking_id']) ? trim($_POST['booking_id']) : '';
    $response = ['success' => false, 'message' => ''];
    
    if (empty($booking_id)) {
        $response['message'] = 'ID booking tidak valid.';
        echo json_encode($response);
        exit;
    }
    
    // Get booking data
    $stmt = $conn->prepare("SELECT * FROM bookings WHERE booking_id = ?");
    $stmt->bind_param("s", $booking_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        $response['message'] = 'Booking tidak ditemukan.';
        echo json_encode($response);
        exit;
    }
    
    $booking = $result->fetch_assoc();
    
    // Check if booking can be cancelled
    if ($booking['status'] == 'completed' || $booking['status'] == 'cancelled') {
        $response['message'] = 'Booking tidak dapat dibatalkan karena status sudah ' . ($booking['status'] == 'completed' ? 'selesai' : 'dibatalkan') . '.';
        echo json_encode($response);
        exit;
    }
    
    // Check if booking date is in the past
    if (strtotime($booking['tanggal']) < strtotime(date('Y-m-d'))) {
        $response['message'] = 'Booking tidak dapat dibatalkan karena tanggal kunjungan sudah lewat.';
        echo json_encode($response);
        exit;
    }
    
    // Update booking status to cancelled
    $stmt = $conn->prepare("UPDATE bookings SET status = 'cancelled', updated_at = NOW() WHERE booking_id = ?");
    $stmt->bind_param("s", $booking_id);
    
    if ($stmt->execute()) {
        $response['success'] = true;
        $response['message'] = 'Booking berhasil dibatalkan.';
    } else {
        $response['message'] = 'Gagal membatalkan booking. Silakan coba lagi.';
    }
    
    echo json_encode($response);
    exit;
} else {
    // If not AJAX request, redirect to check status page
    header("Location: check_status.php");
    exit;
}
?>

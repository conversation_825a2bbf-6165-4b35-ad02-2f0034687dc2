<?php
// Staff management page
$page_title = 'Manajemen Petugas';
$active_menu = 'petugas';
$is_admin = true;

// Include database configuration
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!is_logged_in()) {
    redirect('../login_admin.php');
}

// Check if user is admin
if (!is_admin()) {
    redirect('../index.php');
}

// Process form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['add_staff'])) {
        $username = sanitize($_POST['username']);
        $password = password_hash($_POST['password'], PASSWORD_DEFAULT);
        $nama_lengkap = sanitize($_POST['nama_lengkap']);

        // Check if username already exists
        $check_sql = "SELECT * FROM users WHERE username = '$username'";
        $check_result = query($check_sql);

        if (num_rows($check_result) > 0) {
            $_SESSION['error_message'] = 'Username sudah digunakan. Silakan pilih username lain.';
        } else {
            // Insert new staff
            $sql = "INSERT INTO users (username, password, nama_lengkap, role)
                    VALUES ('$username', '$password', '$nama_lengkap', 'staff')";

            if (query($sql)) {
                $_SESSION['success_message'] = 'Petugas berhasil ditambahkan.';
            } else {
                $_SESSION['error_message'] = 'Gagal menambahkan petugas. Silakan coba lagi.';
            }
        }
        redirect('petugas.php');
    } elseif (isset($_POST['edit_staff'])) {
        $staff_id = (int)$_POST['staff_id'];
        $nama_lengkap = sanitize($_POST['nama_lengkap']);

        if (!empty($_POST['password'])) {
            $password = password_hash($_POST['password'], PASSWORD_DEFAULT);
            $sql = "UPDATE users SET nama_lengkap = '$nama_lengkap', password = '$password' WHERE id = $staff_id";
        } else {
            $sql = "UPDATE users SET nama_lengkap = '$nama_lengkap' WHERE id = $staff_id";
        }

        if (query($sql)) {
            $_SESSION['success_message'] = 'Petugas berhasil diperbarui.';
        } else {
            $_SESSION['error_message'] = 'Gagal memperbarui petugas. Silakan coba lagi.';
        }
        redirect('petugas.php');
    } elseif (isset($_POST['assign_counter'])) {
        $staff_id = (int)$_POST['staff_id'];
        $counter_id = (int)$_POST['counter_id'];

        // First, remove staff from any existing counter
        $sql = "UPDATE loket SET user_id = NULL WHERE user_id = $staff_id";
        query($sql);

        // Then assign to the new counter
        $sql = "UPDATE loket SET user_id = $staff_id WHERE id = $counter_id";

        if (query($sql)) {
            $_SESSION['success_message'] = 'Petugas berhasil ditetapkan ke loket.';
        } else {
            $_SESSION['error_message'] = 'Gagal menetapkan petugas ke loket. Silakan coba lagi.';
        }
        redirect('petugas.php');
    } elseif (isset($_POST['delete_staff'])) {
        $staff_id = (int)$_POST['staff_id'];

        // First, remove staff from any counter
        $sql = "UPDATE loket SET user_id = NULL WHERE user_id = $staff_id";
        query($sql);

        // Then delete the staff
        $sql = "DELETE FROM users WHERE id = $staff_id AND role = 'staff'";

        if (query($sql)) {
            $_SESSION['success_message'] = 'Petugas berhasil dihapus.';
        } else {
            $_SESSION['error_message'] = 'Gagal menghapus petugas. Silakan coba lagi.';
        }
        redirect('petugas.php');
    }
}

// Get staff data from database
$staff_list = [];
$debug_info = [];

// Debug: Check database connection
global $conn;
$debug_info['connection_type'] = get_class($conn);
$debug_info['connection_status'] = ($conn instanceof MockConnection) ? 'Mock' : 'Real';

// Try simple query first
$sql_simple = "SELECT * FROM users WHERE role = 'staff' ORDER BY id ASC";
$debug_info['simple_query'] = $sql_simple;

try {
    $result = query($sql_simple);
    $debug_info['simple_query_result'] = $result ? 'Success' : 'Failed';

    if ($result) {
        $debug_info['simple_query_rows'] = num_rows($result);

        if (num_rows($result) > 0) {
            while ($row = fetch_assoc($result)) {
                $row['loket_id'] = null; // Set default value for simple query
                $staff_list[] = $row;
            }
        }
    }
} catch (Exception $e) {
    $debug_info['simple_query_error'] = $e->getMessage();
}

// If simple query worked, try complex query
if (!empty($staff_list)) {
    $staff_list = []; // Reset for complex query

    $sql_complex = "SELECT u.*, l.id as loket_id
            FROM users u
            LEFT JOIN loket l ON u.id = l.user_id
            WHERE u.role = 'staff'
            ORDER BY u.id ASC";
    $debug_info['complex_query'] = $sql_complex;

    try {
        $result = query($sql_complex);
        $debug_info['complex_query_result'] = $result ? 'Success' : 'Failed';

        if ($result) {
            $debug_info['complex_query_rows'] = num_rows($result);

            if (num_rows($result) > 0) {
                while ($row = fetch_assoc($result)) {
                    $staff_list[] = $row;
                }
            }
        }
    } catch (Exception $e) {
        $debug_info['complex_query_error'] = $e->getMessage();

        // Fall back to simple query results
        $result = query($sql_simple);
        if ($result && num_rows($result) > 0) {
            while ($row = fetch_assoc($result)) {
                $row['loket_id'] = null;
                $staff_list[] = $row;
            }
        }
    }
}

$debug_info['final_staff_count'] = count($staff_list);

// Get counters data
$counters = [];
$sql = "SELECT * FROM loket ORDER BY id ASC";
$result = query($sql);

if (num_rows($result) > 0) {
    while ($row = fetch_assoc($result)) {
        $counters[] = $row;
    }
}

// Include header
include_once 'includes/header.php';
?>

<style>
    /* Staff management page styles */
    .badge {
        font-size: 14px;
        padding: 5px 10px;
        border-radius: 4px;
    }

    .badge-success {
        background-color: #28a745;
        color: white;
    }

    .badge-secondary {
        background-color: #6c757d;
        color: white;
    }

    .btn-group {
        display: flex;
    }

    .btn-group .btn {
        margin-right: 5px;
    }

    .dropdown-menu {
        min-width: 200px;
    }

    .dropdown-toggle::after {
        display: none;
    }

    .dropdown-item {
        padding: 8px 15px;
    }

    .dropdown-item i {
        margin-right: 8px;
        width: 16px;
        text-align: center;
    }

    .table th {
        background-color: #f8f9fa;
        font-weight: 600;
    }

    .table td {
        vertical-align: middle;
    }

    /* Action button styles */
    .btn-aksi {
        position: relative;
    }

    .btn-aksi .dropdown-menu {
        right: 0;
        left: auto;
    }
</style>

<div class="row">
    <div class="col-md-12">
        <h1 class="mb-4">Manajemen Petugas</h1>

        <?php include_once '../includes/alerts.php'; ?>

        <?php if(isset($_GET['debug'])): ?>
        <div class="alert alert-info">
            <strong>Debug Information:</strong><br>
            <?php foreach($debug_info as $key => $value): ?>
            - <?php echo ucfirst(str_replace('_', ' ', $key)); ?>: <?php echo is_array($value) ? json_encode($value) : $value; ?><br>
            <?php endforeach; ?>
            <?php if(!empty($staff_list)): ?>
            <br><strong>Staff Data:</strong><br>
            <?php foreach($staff_list as $index => $staff): ?>
            Staff <?php echo $index + 1; ?>: <?php echo json_encode($staff); ?><br>
            <?php endforeach; ?>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-user-plus"></i> Tambah Petugas Baru
            </div>
            <div class="card-body">
                <form method="post" action="">
                    <div class="form-row">
                        <div class="form-group col-md-4">
                            <label for="username">Username</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                        <div class="form-group col-md-4">
                            <label for="password">Password</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        <div class="form-group col-md-4">
                            <label for="nama_lengkap">Nama Lengkap</label>
                            <input type="text" class="form-control" id="nama_lengkap" name="nama_lengkap" required>
                        </div>
                    </div>
                    <button type="submit" name="add_staff" class="btn btn-primary">Tambah Petugas</button>
                </form>
            </div>
        </div>

        <div class="card">
            <div class="card-header bg-dark text-white">
                <i class="fas fa-users"></i> Daftar Petugas
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Username</th>
                                <th>Nama Lengkap</th>
                                <th>Loket</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if(count($staff_list) > 0): ?>
                            <?php foreach($staff_list as $staff): ?>
                            <tr>
                                <td><?php echo $staff['id']; ?></td>
                                <td><?php echo $staff['username']; ?></td>
                                <td><?php echo $staff['nama_lengkap']; ?></td>
                                <td>
                                    <?php if(isset($staff['loket_id']) && $staff['loket_id']): ?>
                                    <span class="badge badge-success">Loket <?php echo $staff['loket_id']; ?></span>
                                    <?php else: ?>
                                    <span class="badge badge-secondary">Tidak ada</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="dropdown">
                                        <button class="btn btn-info dropdown-toggle" type="button" id="dropdownMenuButton<?php echo $staff['id']; ?>" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            Aksi <i class="fas fa-caret-down ml-1"></i>
                                        </button>
                                        <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownMenuButton<?php echo $staff['id']; ?>">
                                            <a class="dropdown-item" href="#" data-toggle="modal" data-target="#editModal<?php echo $staff['id']; ?>">
                                                <i class="fas fa-edit"></i> Edit
                                            </a>
                                            <a class="dropdown-item" href="#" data-toggle="modal" data-target="#assignModal<?php echo $staff['id']; ?>">
                                                <i class="fas fa-door-open"></i> Tetapkan ke Loket
                                            </a>
                                            <div class="dropdown-divider"></div>
                                            <a class="dropdown-item text-danger" href="#" data-toggle="modal" data-target="#deleteModal<?php echo $staff['id']; ?>">
                                                <i class="fas fa-trash"></i> Hapus
                                            </a>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            <?php else: ?>
                            <tr>
                                <td colspan="5" class="text-center">
                                    Tidak ada data petugas.
                                    <?php if(isset($_GET['debug'])): ?>
                                    <br><small class="text-muted">Debug: Total staff found: <?php echo count($staff_list); ?></small>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Modals -->
<?php foreach($staff_list as $staff): ?>
<div class="modal fade" id="editModal<?php echo $staff['id']; ?>" tabindex="-1" role="dialog" aria-labelledby="editModalLabel<?php echo $staff['id']; ?>" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="editModalLabel<?php echo $staff['id']; ?>">Edit Petugas</h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="post" action="">
                <div class="modal-body">
                    <input type="hidden" name="staff_id" value="<?php echo $staff['id']; ?>">
                    <div class="form-group">
                        <label for="username<?php echo $staff['id']; ?>">Username</label>
                        <input type="text" class="form-control" id="username<?php echo $staff['id']; ?>" value="<?php echo $staff['username']; ?>" readonly>
                        <small class="form-text text-muted">Username tidak dapat diubah.</small>
                    </div>
                    <div class="form-group">
                        <label for="nama_lengkap<?php echo $staff['id']; ?>">Nama Lengkap</label>
                        <input type="text" class="form-control" id="nama_lengkap<?php echo $staff['id']; ?>" name="nama_lengkap" value="<?php echo $staff['nama_lengkap']; ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="password<?php echo $staff['id']; ?>">Password Baru</label>
                        <input type="password" class="form-control" id="password<?php echo $staff['id']; ?>" name="password">
                        <small class="form-text text-muted">Biarkan kosong jika tidak ingin mengubah password.</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" name="edit_staff" class="btn btn-primary">Simpan Perubahan</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endforeach; ?>

<!-- Assign Counter Modals -->
<?php foreach($staff_list as $staff): ?>
<div class="modal fade" id="assignModal<?php echo $staff['id']; ?>" tabindex="-1" role="dialog" aria-labelledby="assignModalLabel<?php echo $staff['id']; ?>" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="assignModalLabel<?php echo $staff['id']; ?>">Tetapkan Petugas ke Loket</h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="post" action="">
                <div class="modal-body">
                    <input type="hidden" name="staff_id" value="<?php echo $staff['id']; ?>">
                    <p>Tetapkan <strong><?php echo $staff['nama_lengkap']; ?></strong> ke loket:</p>
                    <div class="form-group">
                        <select class="form-control" name="counter_id" required>
                            <option value="">-- Pilih Loket --</option>
                            <?php
                            // Sort counters by ID to ensure they're in order
                            usort($counters, function($a, $b) {
                                return $a['id'] - $b['id'];
                            });

                            foreach($counters as $counter):
                            ?>
                            <option value="<?php echo $counter['id']; ?>" <?php echo ($staff['loket_id'] == $counter['id']) ? 'selected' : ''; ?>>
                                Loket <?php echo $counter['id']; ?> (<?php echo ucfirst($counter['jenis_layanan']); ?>)
                                <?php if($counter['user_id'] && $counter['user_id'] != $staff['id']): ?>
                                (Sudah ditugaskan)
                                <?php endif; ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" name="assign_counter" class="btn btn-primary">Tetapkan</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endforeach; ?>

<!-- Delete Modals -->
<?php foreach($staff_list as $staff): ?>
<div class="modal fade" id="deleteModal<?php echo $staff['id']; ?>" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel<?php echo $staff['id']; ?>" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel<?php echo $staff['id']; ?>">Hapus Petugas</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="post" action="">
                <div class="modal-body">
                    <input type="hidden" name="staff_id" value="<?php echo $staff['id']; ?>">
                    <p>Apakah Anda yakin ingin menghapus petugas <strong><?php echo $staff['nama_lengkap']; ?></strong>?</p>
                    <p class="text-danger">Tindakan ini tidak dapat dibatalkan.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" name="delete_staff" class="btn btn-danger">Hapus</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endforeach; ?>

<?php
// Include footer
include_once 'includes/footer.php';
?>

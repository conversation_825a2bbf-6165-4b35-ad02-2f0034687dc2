<?php
// Bookings management page
$page_title = 'Manajemen Booking';
$active_menu = 'bookings';
$is_admin = true;
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!is_logged_in() || !is_admin()) {
    redirect('../login.php');
}

// Get filter and search parameters
$filter = isset($_GET['filter']) ? $_GET['filter'] : '';
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
$sort = isset($_GET['sort']) ? sanitize($_GET['sort']) : 'created_desc';

// Build query based on filter
$where_clause = '';
if ($filter == 'pending') {
    $where_clause = "WHERE b.status = 'pending'";
} elseif ($filter == 'confirmed') {
    $where_clause = "WHERE b.status = 'confirmed'";
} elseif ($filter == 'completed') {
    $where_clause = "WHERE b.status = 'completed'";
} elseif ($filter == 'cancelled') {
    $where_clause = "WHERE b.status = 'cancelled'";
} elseif ($filter == 'today') {
    $where_clause = "WHERE DATE(b.tanggal) = CURDATE()";
} elseif ($filter == 'tomorrow') {
    $where_clause = "WHERE DATE(b.tanggal) = DATE_ADD(CURDATE(), INTERVAL 1 DAY)";
} elseif ($filter == 'teller') {
    $where_clause = "WHERE b.layanan = 'teller'";
} elseif ($filter == 'cs') {
    $where_clause = "WHERE b.layanan = 'cs'";
} elseif ($filter == 'kredit') {
    $where_clause = "WHERE b.layanan = 'kredit'";
}

// Add search condition if provided
if (!empty($search)) {
    if (empty($where_clause)) {
        $where_clause = "WHERE ";
    } else {
        $where_clause .= " AND ";
    }
    $where_clause .= "(b.booking_id LIKE '%$search%' OR b.nama LIKE '%$search%' OR b.no_identitas LIKE '%$search%' OR b.email LIKE '%$search%' OR b.no_hp LIKE '%$search%')";
}

// Determine sort order
$order_clause = "ORDER BY b.created_at DESC";
if ($sort == 'created_asc') {
    $order_clause = "ORDER BY b.created_at ASC";
} elseif ($sort == 'tanggal_asc') {
    $order_clause = "ORDER BY b.tanggal ASC, b.waktu ASC";
} elseif ($sort == 'tanggal_desc') {
    $order_clause = "ORDER BY b.tanggal DESC, b.waktu DESC";
} elseif ($sort == 'nama_asc') {
    $order_clause = "ORDER BY b.nama ASC";
} elseif ($sort == 'nama_desc') {
    $order_clause = "ORDER BY b.nama DESC";
}

// Get bookings
$sql = "SELECT b.*, n.status_verifikasi, n.is_blocked
        FROM bookings b
        LEFT JOIN nasabah n ON b.nasabah_id = n.id
        $where_clause
        $order_clause";
$result = query($sql);
$bookings = fetch_all($result);

// Include header
include_once '../includes/header.php';
?>

<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Manajemen Booking</h1>
        </div>

        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-filter"></i> Filter & Pencarian
            </div>
            <div class="card-body">
                <form method="get" action="bookings.php" class="mb-3">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="Cari ID booking, nama, no. identitas, email, atau no. HP" name="search" value="<?php echo $search; ?>">
                                <div class="input-group-append">
                                    <button class="btn btn-primary" type="submit">
                                        <i class="fas fa-search"></i> Cari
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" name="sort" onchange="this.form.submit()">
                                <option value="created_desc" <?php echo $sort == 'created_desc' ? 'selected' : ''; ?>>Tanggal Booking (Terbaru)</option>
                                <option value="created_asc" <?php echo $sort == 'created_asc' ? 'selected' : ''; ?>>Tanggal Booking (Terlama)</option>
                                <option value="tanggal_asc" <?php echo $sort == 'tanggal_asc' ? 'selected' : ''; ?>>Tanggal Kunjungan (Terdekat)</option>
                                <option value="tanggal_desc" <?php echo $sort == 'tanggal_desc' ? 'selected' : ''; ?>>Tanggal Kunjungan (Terjauh)</option>
                                <option value="nama_asc" <?php echo $sort == 'nama_asc' ? 'selected' : ''; ?>>Nama (A-Z)</option>
                                <option value="nama_desc" <?php echo $sort == 'nama_desc' ? 'selected' : ''; ?>>Nama (Z-A)</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <a href="bookings.php" class="btn btn-secondary btn-block">
                                <i class="fas fa-sync"></i> Reset Filter
                            </a>
                        </div>
                    </div>

                    <!-- Hidden field to preserve filter when sorting -->
                    <?php if(!empty($filter)): ?>
                    <input type="hidden" name="filter" value="<?php echo $filter; ?>">
                    <?php endif; ?>
                </form>

                <div class="btn-group btn-group-sm d-flex flex-wrap">
                    <a href="bookings.php<?php echo !empty($search) ? '?search=' . $search : ''; ?>" class="btn btn-outline-primary <?php echo $filter == '' ? 'active' : ''; ?>">
                        <i class="fas fa-calendar-check"></i> Semua Booking
                    </a>
                    <a href="bookings.php?filter=pending<?php echo !empty($search) ? '&search=' . $search : ''; ?>" class="btn btn-outline-warning <?php echo $filter == 'pending' ? 'active' : ''; ?>">
                        <i class="fas fa-clock"></i> Menunggu Konfirmasi
                    </a>
                    <a href="bookings.php?filter=confirmed<?php echo !empty($search) ? '&search=' . $search : ''; ?>" class="btn btn-outline-success <?php echo $filter == 'confirmed' ? 'active' : ''; ?>">
                        <i class="fas fa-check"></i> Terkonfirmasi
                    </a>
                    <a href="bookings.php?filter=completed<?php echo !empty($search) ? '&search=' . $search : ''; ?>" class="btn btn-outline-info <?php echo $filter == 'completed' ? 'active' : ''; ?>">
                        <i class="fas fa-check-double"></i> Selesai
                    </a>
                    <a href="bookings.php?filter=cancelled<?php echo !empty($search) ? '&search=' . $search : ''; ?>" class="btn btn-outline-danger <?php echo $filter == 'cancelled' ? 'active' : ''; ?>">
                        <i class="fas fa-times"></i> Dibatalkan
                    </a>
                    <a href="bookings.php?filter=today<?php echo !empty($search) ? '&search=' . $search : ''; ?>" class="btn btn-outline-dark <?php echo $filter == 'today' ? 'active' : ''; ?>">
                        <i class="fas fa-calendar-day"></i> Hari Ini
                    </a>
                    <a href="bookings.php?filter=tomorrow<?php echo !empty($search) ? '&search=' . $search : ''; ?>" class="btn btn-outline-secondary <?php echo $filter == 'tomorrow' ? 'active' : ''; ?>">
                        <i class="fas fa-calendar-plus"></i> Besok
                    </a>
                </div>

                <div class="btn-group btn-group-sm d-flex flex-wrap mt-2">
                    <a href="bookings.php?filter=teller<?php echo !empty($search) ? '&search=' . $search : ''; ?>" class="btn btn-outline-primary <?php echo $filter == 'teller' ? 'active' : ''; ?>">
                        <i class="fas fa-money-check-alt"></i> Teller
                    </a>
                    <a href="bookings.php?filter=cs<?php echo !empty($search) ? '&search=' . $search : ''; ?>" class="btn btn-outline-primary <?php echo $filter == 'cs' ? 'active' : ''; ?>">
                        <i class="fas fa-headset"></i> Customer Service
                    </a>
                    <a href="bookings.php?filter=kredit<?php echo !empty($search) ? '&search=' . $search : ''; ?>" class="btn btn-outline-primary <?php echo $filter == 'kredit' ? 'active' : ''; ?>">
                        <i class="fas fa-hand-holding-usd"></i> Kredit
                    </a>
                </div>

                <?php if(!empty($search)): ?>
                <div class="alert alert-info mt-3">
                    <i class="fas fa-search"></i> Hasil pencarian untuk: <strong><?php echo $search; ?></strong>
                    <a href="bookings.php<?php echo !empty($filter) ? '?filter=' . $filter : ''; ?>" class="float-right">
                        <i class="fas fa-times"></i> Hapus Pencarian
                    </a>
                </div>
                <?php endif; ?>

                <div class="mt-3">
                    <small class="text-muted">
                        Menampilkan <?php echo count($bookings); ?> booking
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header bg-primary text-white">
        <i class="fas fa-calendar-check"></i> Daftar Booking
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>ID Booking</th>
                        <th>Nasabah</th>
                        <th>Layanan</th>
                        <th>Jadwal</th>
                        <th>Status</th>
                        <th>Tanggal Booking</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if(count($bookings) > 0): ?>
                    <?php foreach($bookings as $booking): ?>
                    <tr>
                        <td><?php echo $booking['booking_id']; ?></td>
                        <td>
                            <strong><?php echo $booking['nama']; ?></strong><br>
                            <small><?php echo $booking['no_hp']; ?></small>
                            
                            <?php if($booking['status_verifikasi'] == 'belum'): ?>
                            <span class="badge badge-warning">Belum Verifikasi</span>
                            <?php elseif($booking['status_verifikasi'] == 'ditolak'): ?>
                            <span class="badge badge-danger">Ditolak</span>
                            <?php endif; ?>
                            
                            <?php if($booking['is_blocked']): ?>
                            <span class="badge badge-dark">Diblokir</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php
                            $layanan_icon = '';
                            $layanan_text = '';
                            
                            switch($booking['layanan']) {
                                case 'teller':
                                    $layanan_icon = 'fa-money-check-alt';
                                    $layanan_text = 'Teller';
                                    break;
                                case 'cs':
                                    $layanan_icon = 'fa-headset';
                                    $layanan_text = 'Customer Service';
                                    break;
                                case 'kredit':
                                    $layanan_icon = 'fa-hand-holding-usd';
                                    $layanan_text = 'Kredit';
                                    break;
                            }
                            ?>
                            <i class="fas <?php echo $layanan_icon; ?>"></i> <?php echo $layanan_text; ?><br>
                            <small class="text-muted">No. Antrian: <?php echo $booking['nomor_antrian']; ?></small>
                        </td>
                        <td>
                            <?php echo date('d/m/Y', strtotime($booking['tanggal'])); ?><br>
                            <small><?php echo $booking['waktu']; ?></small>
                        </td>
                        <td>
                            <?php if($booking['status'] == 'pending'): ?>
                            <span class="badge badge-warning">Menunggu Konfirmasi</span>
                            <?php elseif($booking['status'] == 'confirmed'): ?>
                            <span class="badge badge-success">Terkonfirmasi</span>
                            <?php elseif($booking['status'] == 'completed'): ?>
                            <span class="badge badge-info">Selesai</span>
                            <?php else: ?>
                            <span class="badge badge-danger">Dibatalkan</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php echo date('d/m/Y H:i', strtotime($booking['created_at'])); ?>
                        </td>
                        <td>
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-info dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    Aksi
                                </button>
                                <div class="dropdown-menu">
                                    <a class="dropdown-item" href="#" data-toggle="modal" data-target="#detailModal" 
                                       data-id="<?php echo $booking['booking_id']; ?>"
                                       data-nama="<?php echo $booking['nama']; ?>"
                                       data-identitas="<?php echo $booking['no_identitas']; ?>"
                                       data-email="<?php echo $booking['email']; ?>"
                                       data-hp="<?php echo $booking['no_hp']; ?>"
                                       data-layanan="<?php echo $layanan_text; ?>"
                                       data-tanggal="<?php echo date('d/m/Y', strtotime($booking['tanggal'])); ?>"
                                       data-waktu="<?php echo $booking['waktu']; ?>"
                                       data-keterangan="<?php echo $booking['keterangan']; ?>"
                                       data-file="<?php echo $booking['file_identitas']; ?>"
                                       data-status="<?php echo $booking['status']; ?>"
                                       data-created="<?php echo date('d/m/Y H:i', strtotime($booking['created_at'])); ?>"
                                       data-antrian="<?php echo $booking['nomor_antrian']; ?>">
                                        <i class="fas fa-eye"></i> Lihat Detail
                                    </a>
                                    
                                    <?php if($booking['status'] == 'pending'): ?>
                                    <a class="dropdown-item text-success" href="#" data-toggle="modal" data-target="#confirmModal" 
                                       data-id="<?php echo $booking['booking_id']; ?>"
                                       data-nama="<?php echo $booking['nama']; ?>"
                                       data-antrian="<?php echo $booking['nomor_antrian']; ?>">
                                        <i class="fas fa-check"></i> Konfirmasi
                                    </a>
                                    
                                    <a class="dropdown-item text-danger" href="#" data-toggle="modal" data-target="#rejectModal" 
                                       data-id="<?php echo $booking['booking_id']; ?>"
                                       data-nama="<?php echo $booking['nama']; ?>">
                                        <i class="fas fa-times"></i> Tolak
                                    </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                    <?php else: ?>
                    <tr>
                        <td colspan="7" class="text-center">Tidak ada data booking.</td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Detail Modal -->
<div class="modal fade" id="detailModal" tabindex="-1" role="dialog" aria-labelledby="detailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="detailModalLabel">Detail Booking</h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>Informasi Booking</h5>
                        <table class="table table-sm">
                            <tr>
                                <th>ID Booking</th>
                                <td id="detail-id"></td>
                            </tr>
                            <tr>
                                <th>No. Antrian</th>
                                <td id="detail-antrian"></td>
                            </tr>
                            <tr>
                                <th>Status</th>
                                <td id="detail-status"></td>
                            </tr>
                            <tr>
                                <th>Tanggal Booking</th>
                                <td id="detail-created"></td>
                            </tr>
                            <tr>
                                <th>Jadwal Kunjungan</th>
                                <td id="detail-jadwal"></td>
                            </tr>
                            <tr>
                                <th>Layanan</th>
                                <td id="detail-layanan"></td>
                            </tr>
                            <tr>
                                <th>Keterangan</th>
                                <td id="detail-keterangan"></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h5>Informasi Nasabah</h5>
                        <table class="table table-sm">
                            <tr>
                                <th>Nama</th>
                                <td id="detail-nama"></td>
                            </tr>
                            <tr>
                                <th>No. Identitas</th>
                                <td id="detail-identitas"></td>
                            </tr>
                            <tr>
                                <th>Email</th>
                                <td id="detail-email"></td>
                            </tr>
                            <tr>
                                <th>No. HP</th>
                                <td id="detail-hp"></td>
                            </tr>
                        </table>
                        
                        <div class="mt-3">
                            <h5>Dokumen Identitas</h5>
                            <div class="text-center">
                                <img id="detail-file" src="" alt="Dokumen Identitas" class="img-fluid img-thumbnail" style="max-height: 200px;">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<!-- Confirm Modal -->
<div class="modal fade" id="confirmModal" tabindex="-1" role="dialog" aria-labelledby="confirmModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="confirmModalLabel">Konfirmasi Booking</h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="process_booking.php" method="post">
                <div class="modal-body">
                    <p>Anda akan mengkonfirmasi booking dengan ID: <strong id="confirm-id"></strong></p>
                    <p>Nasabah: <strong id="confirm-nama"></strong></p>
                    <p>Nomor Antrian: <strong id="confirm-antrian"></strong></p>
                    <p class="text-success">Booking yang dikonfirmasi akan dimasukkan ke dalam antrian.</p>
                    
                    <input type="hidden" id="confirm-booking-id" name="booking_id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" name="confirm_booking" class="btn btn-success">Konfirmasi</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Reject Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1" role="dialog" aria-labelledby="rejectModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="rejectModalLabel">Tolak Booking</h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="process_booking.php" method="post">
                <div class="modal-body">
                    <p>Anda akan menolak booking dengan ID: <strong id="reject-id"></strong></p>
                    <p>Nasabah: <strong id="reject-nama"></strong></p>
                    
                    <div class="form-group">
                        <label for="reason">Alasan Penolakan</label>
                        <textarea class="form-control" id="reason" name="reason" rows="3" required></textarea>
                    </div>
                    
                    <input type="hidden" id="reject-booking-id" name="booking_id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" name="reject_booking" class="btn btn-danger">Tolak</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>

<script>
$(document).ready(function() {
    // Detail modal
    $('#detailModal').on('show.bs.modal', function (event) {
        var button = $(event.relatedTarget);
        var id = button.data('id');
        var nama = button.data('nama');
        var identitas = button.data('identitas');
        var email = button.data('email');
        var hp = button.data('hp');
        var layanan = button.data('layanan');
        var tanggal = button.data('tanggal');
        var waktu = button.data('waktu');
        var keterangan = button.data('keterangan');
        var file = button.data('file');
        var status = button.data('status');
        var created = button.data('created');
        var antrian = button.data('antrian');
        
        var modal = $(this);
        
        modal.find('#detail-id').text(id);
        modal.find('#detail-nama').text(nama);
        modal.find('#detail-identitas').text(identitas);
        modal.find('#detail-email').text(email);
        modal.find('#detail-hp').text(hp);
        modal.find('#detail-layanan').text(layanan);
        modal.find('#detail-jadwal').text(tanggal + ' ' + waktu);
        modal.find('#detail-keterangan').text(keterangan || '-');
        modal.find('#detail-file').attr('src', '../uploads/' + file);
        modal.find('#detail-created').text(created);
        modal.find('#detail-antrian').text(antrian);
        
        var statusText = '';
        var statusClass = '';
        
        switch(status) {
            case 'pending':
                statusText = 'Menunggu Konfirmasi';
                statusClass = 'warning';
                break;
            case 'confirmed':
                statusText = 'Terkonfirmasi';
                statusClass = 'success';
                break;
            case 'completed':
                statusText = 'Selesai';
                statusClass = 'info';
                break;
            case 'cancelled':
                statusText = 'Dibatalkan';
                statusClass = 'danger';
                break;
        }
        
        modal.find('#detail-status').html('<span class="badge badge-' + statusClass + '">' + statusText + '</span>');
    });
    
    // Confirm modal
    $('#confirmModal').on('show.bs.modal', function (event) {
        var button = $(event.relatedTarget);
        var id = button.data('id');
        var nama = button.data('nama');
        var antrian = button.data('antrian');
        
        var modal = $(this);
        
        modal.find('#confirm-id').text(id);
        modal.find('#confirm-nama').text(nama);
        modal.find('#confirm-antrian').text(antrian);
        modal.find('#confirm-booking-id').val(id);
    });
    
    // Reject modal
    $('#rejectModal').on('show.bs.modal', function (event) {
        var button = $(event.relatedTarget);
        var id = button.data('id');
        var nama = button.data('nama');
        
        var modal = $(this);
        
        modal.find('#reject-id').text(id);
        modal.find('#reject-nama').text(nama);
        modal.find('#reject-booking-id').val(id);
    });
});
</script>

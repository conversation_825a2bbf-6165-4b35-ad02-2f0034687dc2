<?php
// Complete service API
header('Content-Type: application/json');
session_start();

// Include database configuration
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/queue_connector.php';

// Check if user is logged in and is staff
if (!is_logged_in() || !is_staff()) {
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized access.'
    ]);
    exit;
}

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method.'
    ]);
    exit;
}

// Get parameters
$counter_id = isset($_POST['counter_id']) ? (int)$_POST['counter_id'] : 0;
$queue_id = isset($_POST['queue_id']) ? (int)$_POST['queue_id'] : 0;

// Validate counter
$user_id = $_SESSION['user_id'];
$sql = "SELECT * FROM loket WHERE id = $counter_id AND user_id = $user_id AND status = 'aktif'";
$result = query($sql);

if (num_rows($result) == 0) {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid counter.'
    ]);
    exit;
}

// Validate queue
$sql = "SELECT * FROM antrian
        WHERE id = $queue_id
        AND loket_id = $counter_id
        AND status = 'dipanggil'
        AND tanggal = CURDATE()";
$result = query($sql);

if (num_rows($result) == 0) {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid queue.'
    ]);
    exit;
}

// Selesaikan antrian menggunakan fungsi dari queue_connector
$result = complete_queue($queue_id);

// Return success response
echo json_encode([
    'success' => true,
    'message' => 'Pelayanan berhasil diselesaikan.'
]);
?>

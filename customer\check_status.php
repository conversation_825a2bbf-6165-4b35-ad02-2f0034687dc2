<?php
// Check booking status page
$page_title = 'Cek Status Booking';
require_once '../includes/functions.php';

$booking = null;
$error = null;

// Process form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $nomor_antrian = sanitize($_POST['nomor_antrian']);

    // Validate input
    if (empty($nomor_antrian)) {
        $error = 'Nomor antrian harus diisi.';
    } else {
        // Check booking in database
        $sql = "SELECT a.*, n.nama, n.status_verifikasi, n.alasan_penolakan, n.is_blocked, p.nama_poli
                FROM antrian a
                JOIN nasabah n ON a.nasabah_id = n.id
                JOIN poli p ON a.poli_id = p.id
                WHERE a.nomor_antrian = '$nomor_antrian' AND a.booking_dari = 'online'";
        $result = query($sql);

        if (num_rows($result) == 1) {
            $booking = fetch_assoc($result);
        } else {
            $error = 'Nomor antrian tidak ditemukan.';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Bank BJB</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">

    <style>
        body {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            min-height: 100vh;
            padding: 50px 0;
        }
        .status-container {
            background-color: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
        .logo-container {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo-container img {
            height: 80px;
            margin-bottom: 15px;
        }
        .verification-status {
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            text-align: center;
            font-weight: bold;
        }
        .verification-status.pending {
            background-color: #ffc107;
            color: #212529;
        }
        .verification-status.verified {
            background-color: #28a745;
            color: white;
        }
        .verification-status.rejected {
            background-color: #dc3545;
            color: white;
        }
        .booking-details {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="status-container">
                    <div class="logo-container">
                        <img src="../assets/img/logo.jpeg" alt="Bank BJB">
                        <h2>Cek Status Booking</h2>
                        <p class="text-muted">Masukkan nomor antrian untuk melihat status booking Anda.</p>
                    </div>

                    <?php if($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error; ?>
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <?php endif; ?>

                    <form action="check_status.php" method="post">
                        <div class="form-group">
                            <label for="nomor_antrian">Nomor Antrian</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="nomor_antrian" name="nomor_antrian" placeholder="Masukkan nomor antrian" required>
                                <div class="input-group-append">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> Cek Status
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>

                    <?php if($booking): ?>
                    <div class="booking-details">
                        <h4 class="text-center mb-4">Detail Booking</h4>

                        <?php if($booking['status_verifikasi'] == 'belum'): ?>
                        <div class="verification-status pending">
                            <i class="fas fa-clock"></i> Menunggu Verifikasi
                        </div>
                        <?php elseif($booking['status_verifikasi'] == 'terverifikasi'): ?>
                        <div class="verification-status verified">
                            <i class="fas fa-check-circle"></i> Terverifikasi
                        </div>
                        <?php else: ?>
                        <div class="verification-status rejected">
                            <i class="fas fa-times-circle"></i> Ditolak
                        </div>
                        <?php endif; ?>

                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Nomor Antrian:</strong> <?php echo $booking['nomor_antrian']; ?></p>
                                <p><strong>Nama:</strong> <?php echo $booking['nama']; ?></p>
                                <p><strong>Layanan:</strong> <?php echo $booking['nama_poli']; ?></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Tanggal Kunjungan:</strong> <?php echo date('d/m/Y', strtotime($booking['tanggal'])); ?></p>
                                <p><strong>Status Antrian:</strong> <?php echo ucfirst($booking['status']); ?></p>
                                <p><strong>Estimasi Waktu:</strong> <?php echo $booking['estimasi_waktu']; ?> menit</p>
                            </div>
                        </div>

                        <?php if($booking['status_verifikasi'] == 'ditolak' && !empty($booking['alasan_penolakan'])): ?>
                        <div class="alert alert-danger mt-3">
                            <strong>Alasan Penolakan:</strong> <?php echo $booking['alasan_penolakan']; ?>
                        </div>
                        <?php endif; ?>

                        <?php if($booking['is_blocked'] == 1): ?>
                        <div class="alert alert-danger mt-3">
                            <i class="fas fa-ban"></i> Akun Anda telah diblokir. Silahkan hubungi petugas untuk informasi lebih lanjut.
                        </div>
                        <?php elseif($booking['status_verifikasi'] == 'terverifikasi'): ?>
                        <div class="alert alert-success mt-3">
                            <i class="fas fa-info-circle"></i> Booking Anda telah terverifikasi. Silahkan datang pada tanggal <?php echo date('d/m/Y', strtotime($booking['tanggal'])); ?> dan tunjukkan nomor antrian Anda.
                        </div>

                        <?php
                        // Get current queue status for the service
                        $poli_id = $booking['poli_id'];
                        $tanggal = $booking['tanggal'];
                        $sql_current = "SELECT COUNT(*) as total_menunggu FROM antrian
                                        WHERE poli_id = $poli_id
                                        AND tanggal = '$tanggal'
                                        AND status = 'menunggu'
                                        AND id < " . $booking['id'];
                        $result_current = query($sql_current);
                        $current_data = fetch_assoc($result_current);
                        $antrian_menunggu = $current_data['total_menunggu'];

                        // Get current serving queue
                        $sql_serving = "SELECT nomor_antrian FROM antrian
                                        WHERE poli_id = $poli_id
                                        AND tanggal = '$tanggal'
                                        AND status = 'dipanggil'
                                        ORDER BY waktu_dipanggil DESC LIMIT 1";
                        $result_serving = query($sql_serving);
                        ?>

                        <div class="alert alert-info mt-3">
                            <h5 class="mb-2"><i class="fas fa-info-circle"></i> Status Antrian Saat Ini:</h5>
                            <?php if(num_rows($result_serving) > 0):
                                $serving = fetch_assoc($result_serving);
                            ?>
                            <p><strong>Nomor Antrian yang Sedang Dilayani:</strong> <?php echo $serving['nomor_antrian']; ?></p>
                            <?php else: ?>
                            <p><strong>Belum ada antrian yang dilayani</strong></p>
                            <?php endif; ?>
                            <p><strong>Jumlah Antrian Menunggu Sebelum Anda:</strong> <?php echo $antrian_menunggu; ?></p>
                            <p><strong>Estimasi Waktu Tunggu:</strong> <?php echo $antrian_menunggu * $booking['estimasi_waktu']; ?> menit</p>
                        </div>
                        <?php elseif($booking['status_verifikasi'] == 'belum'): ?>
                        <div class="alert alert-warning mt-3">
                            <i class="fas fa-info-circle"></i> Booking Anda sedang dalam proses verifikasi. Silahkan cek kembali status booking secara berkala.
                        </div>
                        <?php else: ?>
                        <div class="alert alert-danger mt-3">
                            <i class="fas fa-info-circle"></i> Maaf, booking Anda ditolak. Silahkan buat booking baru dengan data yang valid.
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>

                    <div class="text-center mt-4">
                        <a href="booking.php" class="btn btn-outline-primary mr-2">
                            <i class="fas fa-calendar-plus"></i> Buat Booking Baru
                        </a>
                        <a href="../index.php" class="btn btn-link">
                            <i class="fas fa-arrow-left"></i> Kembali ke Halaman Utama
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

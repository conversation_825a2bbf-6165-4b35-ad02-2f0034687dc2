<?php
// Verify customer API
header('Content-Type: application/json');
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!is_logged_in() || !is_admin()) {
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized access.'
    ]);
    exit;
}

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method.'
    ]);
    exit;
}

// Get parameters
$customer_id = isset($_POST['customer_id']) ? (int)$_POST['customer_id'] : 0;
$status = isset($_POST['status']) ? sanitize($_POST['status']) : '';
$reason = isset($_POST['reason']) ? sanitize($_POST['reason']) : '';

// Validate parameters
if (empty($customer_id) || empty($status)) {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid parameters.'
    ]);
    exit;
}

// Validate status
if (!in_array($status, ['terverifikasi', 'ditolak'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid status.'
    ]);
    exit;
}

// Validate customer
$sql = "SELECT * FROM nasabah WHERE id = $customer_id";
$result = query($sql);

if (num_rows($result) == 0) {
    echo json_encode([
        'success' => false,
        'message' => 'Customer not found.'
    ]);
    exit;
}

// Update customer verification status
$sql = "UPDATE nasabah 
        SET status_verifikasi = '$status', 
            alasan_penolakan = '$reason', 
            updated_at = NOW() 
        WHERE id = $customer_id";
query($sql);

// Return success response
echo json_encode([
    'success' => true,
    'message' => 'Customer verification status updated successfully.'
]);
?>

<?php
require_once 'includes/functions.php';

// Check if jam_operasional setting exists
$jam_operasional = get_setting('jam_operasional');
echo "Jam operasional: " . $jam_operasional . "\n";

// If not exists, create it
if (empty($jam_operasional)) {
    $sql = "INSERT INTO pengaturan (nama_pengaturan, nilai, deskripsi) 
            VALUES ('jam_operasional', '08:00-16:00', 'Jam operasional pelayanan')";
    if (query($sql)) {
        echo "Berhasil menambahkan pengaturan jam operasional\n";
    } else {
        echo "Gagal menambahkan pengaturan jam operasional\n";
    }
}

// Check other settings
$settings = [
    'nama_instansi' => 'Bank BJB Kantor Cabang Khusus Banten',
    'logo_instansi' => 'logo.jpeg',
    'max_antrian' => '100',
    'verifikasi_wajib' => '1'
];

foreach ($settings as $key => $default_value) {
    $value = get_setting($key);
    echo "$key: $value\n";
    
    if (empty($value)) {
        $sql = "INSERT INTO pengaturan (nama_pengaturan, nilai, deskripsi) 
                VALUES ('$key', '$default_value', 'Pengaturan $key')";
        if (query($sql)) {
            echo "Berhasil menambahkan pengaturan $key\n";
        } else {
            echo "Gagal menambahkan pengaturan $key\n";
        }
    }
}

echo "Selesai memeriksa pengaturan\n";
?>

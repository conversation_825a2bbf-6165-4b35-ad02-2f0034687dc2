<?php
// Setup database tables for admin functionality
require_once '../includes/functions.php';

// Create users table if not exists
$sql_users = "CREATE TABLE IF NOT EXISTS users (
    id INT(11) NOT NULL AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL,
    password VARCHAR(255) NOT NULL,
    nama_lengkap VARCHAR(100) NOT NULL,
    email VARCHAR(100) DEFAULT NULL,
    role ENUM('admin', 'petugas') NOT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME DEFAULT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY (username)
)";

if (query($sql_users)) {
    echo "Table 'users' created successfully or already exists<br>";
} else {
    echo "Error creating table 'users': " . mysqli_error($conn) . "<br>";
}

// Check if admin user exists
$sql_check_admin = "SELECT id FROM users WHERE username = 'admin' AND role = 'admin'";
$result = query($sql_check_admin);

if (num_rows($result) === 0) {
    // Create default admin user
    $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
    $sql_insert_admin = "INSERT INTO users (username, password, nama_lengkap, role, created_at) 
                         VALUES ('admin', '$admin_password', 'Administrator', 'admin', NOW())";
    
    if (query($sql_insert_admin)) {
        echo "Default admin user created successfully<br>";
    } else {
        echo "Error creating default admin user: " . mysqli_error($conn) . "<br>";
    }
} else {
    echo "Admin user already exists<br>";
}

echo "<br>Database setup completed!";
echo "<br><a href='../admin.php'>Back to Admin Login</a>";

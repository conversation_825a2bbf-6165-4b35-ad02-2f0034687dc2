<?php
/**
 * Core Functions (Mock Version)
 *
 * Fungsi-fungsi utama untuk Sistem Manajemen Antrian Bank BJB
 * This version works without a database connection
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

/**
 * Function to check if user is logged in
 *
 * @return bool
 */
if (!function_exists('is_logged_in')) {
    function is_logged_in() {
        return isset($_SESSION['user_id']);
    }
}

/**
 * Function to check if user is admin
 *
 * @return bool
 */
if (!function_exists('is_admin')) {
    function is_admin() {
        return isset($_SESSION['role']) && $_SESSION['role'] == 'admin';
    }
}

/**
 * Function to check if user is staff
 *
 * @return bool
 */
if (!function_exists('is_staff')) {
    function is_staff() {
        return isset($_SESSION['role']) && $_SESSION['role'] == 'staff';
    }
}

/**
 * Function to redirect to a specific page
 *
 * @param string $page
 * @return void
 */
if (!function_exists('redirect')) {
    function redirect($page) {
        header("Location: $page");
        exit;
    }
}

/**
 * Function to display error message
 *
 * @param string $message
 * @return string
 */
if (!function_exists('error_message')) {
    function error_message($message) {
        return '<div class="alert alert-danger">' . $message . '</div>';
    }
}

/**
 * Function to display success message
 *
 * @param string $message
 * @return string
 */
if (!function_exists('success_message')) {
    function success_message($message) {
        return '<div class="alert alert-success">' . $message . '</div>';
    }
}

/**
 * Function to get setting value (mock version)
 *
 * @param string $name
 * @return string
 */
if (!function_exists('get_setting')) {
    function get_setting($name) {
        $settings = [
            'jam_operasional' => '08:00-16:00',
            'nama_instansi' => 'Bank BJB Kantor Cabang Khusus Banten',
            'logo_instansi' => 'logo.jpeg',
            'max_antrian' => '100',
            'verifikasi_wajib' => '1'
        ];

        return isset($settings[$name]) ? $settings[$name] : '';
    }
}

/**
 * Function to generate queue number (mock version)
 *
 * @param string $layanan
 * @param string $date
 * @return string
 */
if (!function_exists('generate_queue_number')) {
    function generate_queue_number($layanan, $date = null) {
        if ($date === null) {
            $date = date('Y-m-d');
        }

        // Generate a random number between 1 and 100
        $number = rand(1, 100);

        // Format based on service type
        if ($layanan == 'teller' || $layanan == 1) {
            return 'T' . sprintf("%03d", $number); // Teller
        } elseif ($layanan == 'cs' || $layanan == 2) {
            return 'C' . sprintf("%03d", $number); // Customer Service
        } elseif ($layanan == 'kredit' || $layanan == 3) {
            return 'K' . sprintf("%03d", $number); // Kredit
        } else {
            // Default to Teller if unknown
            return 'T' . sprintf("%03d", $number);
        }
    }
}

/**
 * Function to calculate estimated waiting time (mock version)
 *
 * @param int $poli_id
 * @param string $date
 * @return int
 */
if (!function_exists('calculate_estimated_time')) {
    function calculate_estimated_time($poli_id, $date = null) {
        // Return a random time between 5 and 30 minutes
        return rand(5, 30);
    }
}

/**
 * Function to check if IP is blocked (mock version)
 *
 * @param string $ip
 * @return bool
 */
if (!function_exists('is_ip_blocked')) {
    function is_ip_blocked($ip) {
        // No IPs are blocked in mock version
        return false;
    }
}

/**
 * Function to block an IP (mock version)
 *
 * @param string $ip
 * @param string $reason
 * @return bool
 */
if (!function_exists('block_ip')) {
    function block_ip($ip, $reason = '') {
        // Always return true in mock version
        return true;
    }
}

/**
 * Function to verify customer ID (mock version)
 *
 * @param int $nasabah_id
 * @param string $status
 * @param string $reason
 * @return bool
 */
if (!function_exists('verify_customer')) {
    function verify_customer($nasabah_id, $status = 'terverifikasi', $reason = '') {
        // Always return true in mock version
        return true;
    }
}

/**
 * Function to get current date in Indonesian format
 *
 * @return string
 */
if (!function_exists('tanggal_indonesia')) {
    function tanggal_indonesia() {
        $hari = array('Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu');
        $bulan = array('Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni', 'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember');

        $day = $hari[date('w')];
        $date = date('j');
        $month = $bulan[date('n')-1];
        $year = date('Y');

        return "$day, $date $month $year";
    }
}

/**
 * Function to format date in Indonesian format
 *
 * @param string $date
 * @return string
 */
if (!function_exists('format_date')) {
    function format_date($date) {
        if (empty($date)) {
            return '-';
        }

        $hari = array('Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu');
        $bulan = array('Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni', 'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember');

        $timestamp = strtotime($date);
        $day = $hari[date('w', $timestamp)];
        $date_num = date('j', $timestamp);
        $month = $bulan[date('n', $timestamp)-1];
        $year = date('Y', $timestamp);

        return "$day, $date_num $month $year";
    }
}

/**
 * Function to format datetime in Indonesian format
 *
 * @param string $datetime
 * @return string
 */
if (!function_exists('format_datetime')) {
    function format_datetime($datetime) {
        if (empty($datetime)) {
            return '-';
        }

        $hari = array('Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu');
        $bulan = array('Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni', 'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember');

        $timestamp = strtotime($datetime);
        $day = $hari[date('w', $timestamp)];
        $date_num = date('j', $timestamp);
        $month = $bulan[date('n', $timestamp)-1];
        $year = date('Y', $timestamp);
        $time = date('H:i:s', $timestamp);

        return "$day, $date_num $month $year $time";
    }
}

/**
 * Function to check if current time is within operational hours
 *
 * @return bool
 */
if (!function_exists('is_operational_hours')) {
    function is_operational_hours() {
        // Get operational hours from settings
        $jam_operasional = get_setting('jam_operasional');

        // If not set, default to 08:00-16:00
        if (empty($jam_operasional)) {
            $jam_operasional = '08:00-16:00';
        }

        // Parse operational hours
        $jam = explode('-', $jam_operasional);
        $jam_buka = isset($jam[0]) ? trim($jam[0]) : '08:00';
        $jam_tutup = isset($jam[1]) ? trim($jam[1]) : '16:00';

        // Get current time
        $current_time = date('H:i');

        // Check if current time is within operational hours
        return ($current_time >= $jam_buka && $current_time <= $jam_tutup);
    }
}

/**
 * Database functions - using real database connection
 */
// Include database configuration if not already included
if (!function_exists('query')) {
    require_once __DIR__ . '/../config/database.php';
}

/**
 * Function to get nasabah by ID
 *
 * @param int $id
 * @return array|bool
 */
function get_nasabah_by_id($id) {
    global $conn;

    if ($conn instanceof MockConnection) {
        // Use mock data if database connection failed
        return [
            'id' => $id,
            'nama' => 'Nasabah Test',
            'no_identitas' => '1234567890123456',
            'jenis_identitas' => 'ktp',
            'file_identitas' => 'ktp.jpg',
            'no_hp' => '081234567890',
            'email' => '<EMAIL>',
            'alamat' => 'Jl. Test No. 123',
            'status_verifikasi' => 'sudah',
            'is_blocked' => 0
        ];
    }

    // Use real database connection
    $id = (int)$id;
    $sql = "SELECT * FROM nasabah WHERE id = $id";
    $result = query($sql);

    if (num_rows($result) > 0) {
        return fetch_assoc($result);
    }

    return false;
}

/**
 * Function to get nasabah by email
 *
 * @param string $email
 * @return array|bool
 */
function get_nasabah_by_email($email) {
    global $conn;

    if ($conn instanceof MockConnection) {
        // Use mock data if database connection failed
        return [
            'id' => 1,
            'nama' => 'Nasabah Test',
            'no_identitas' => '1234567890123456',
            'jenis_identitas' => 'ktp',
            'file_identitas' => 'ktp.jpg',
            'no_hp' => '081234567890',
            'email' => $email,
            'alamat' => 'Jl. Test No. 123',
            'status_verifikasi' => 'sudah',
            'is_blocked' => 0
        ];
    }

    // Use real database connection
    $email = sanitize($email);
    $sql = "SELECT * FROM nasabah WHERE email = '$email'";
    $result = query($sql);

    if (num_rows($result) > 0) {
        return fetch_assoc($result);
    }

    return false;
}

/**
 * Function to get booking by ID
 *
 * @param string $booking_id
 * @return array|bool
 */
function get_booking_by_id($booking_id) {
    global $conn;

    if ($conn instanceof MockConnection) {
        // Use mock data if database connection failed
        return [
            'id' => 1,
            'booking_id' => $booking_id,
            'nasabah_id' => 1,
            'nama' => 'Nasabah Test',
            'no_identitas' => '1234567890123456',
            'email' => '<EMAIL>',
            'no_hp' => '081234567890',
            'layanan' => 'teller',
            'tanggal' => date('Y-m-d'),
            'waktu' => '10:00',
            'keterangan' => 'Test booking',
            'file_identitas' => 'ktp.jpg',
            'nomor_antrian' => 'T001',
            'status' => 'confirmed'
        ];
    }

    // Use real database connection
    $booking_id = sanitize($booking_id);
    $sql = "SELECT * FROM bookings WHERE booking_id = '$booking_id'";
    $result = query($sql);

    if (num_rows($result) > 0) {
        return fetch_assoc($result);
    }

    return false;
}

/**
 * Function to get bookings by nasabah ID
 *
 * @param int $nasabah_id
 * @return array
 */
function get_bookings_by_nasabah_id($nasabah_id) {
    global $conn;

    if ($conn instanceof MockConnection) {
        // Use mock data if database connection failed
        return [
            [
                'id' => 1,
                'booking_id' => 'BK' . date('Ymd') . rand(1000, 9999),
                'nasabah_id' => $nasabah_id,
                'nama' => 'Nasabah Test',
                'no_identitas' => '1234567890123456',
                'email' => '<EMAIL>',
                'no_hp' => '081234567890',
                'layanan' => 'teller',
                'tanggal' => date('Y-m-d'),
                'waktu' => '10:00',
                'keterangan' => 'Test booking',
                'file_identitas' => 'ktp.jpg',
                'nomor_antrian' => 'T001',
                'status' => 'confirmed'
            ]
        ];
    }

    // Use real database connection
    $nasabah_id = (int)$nasabah_id;
    $sql = "SELECT * FROM bookings WHERE nasabah_id = $nasabah_id ORDER BY created_at DESC";
    $result = query($sql);

    if (num_rows($result) > 0) {
        $bookings = [];
        while ($row = fetch_assoc($result)) {
            $bookings[] = $row;
        }
        return $bookings;
    }

    return [];
}

/**
 * Function to check if nasabah has pending booking
 *
 * @param int $nasabah_id
 * @return bool
 */
function has_pending_booking($nasabah_id) {
    global $conn;

    if ($conn instanceof MockConnection) {
        // Use mock data if database connection failed
        return false;
    }

    // Use real database connection
    $nasabah_id = (int)$nasabah_id;
    $sql = "SELECT COUNT(*) as count FROM bookings WHERE nasabah_id = $nasabah_id AND status IN ('pending', 'confirmed')";
    $result = query($sql);

    if (num_rows($result) > 0) {
        $row = fetch_assoc($result);
        return $row['count'] > 0;
    }

    return false;
}

/**
 * Check if user is logged in
 *
 * @return bool
 */
if (!function_exists('is_logged_in')) {
    function is_logged_in() {
        return isset($_SESSION['user_id']);
    }
}

/**
 * Check if user is admin
 *
 * @return bool
 */
if (!function_exists('is_admin')) {
    function is_admin() {
        return isset($_SESSION['role']) && $_SESSION['role'] == 'admin';
    }
}
?>

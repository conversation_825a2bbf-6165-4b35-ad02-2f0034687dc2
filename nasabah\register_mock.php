<?php
// Customer registration page (Mock Version)
$page_title = 'Pendaftaran Nasabah';
require_once '../includes/functions_mock.php';

// Process registration form
$success = false;
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Simulate successful registration
    $success = true;
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Bank BJB</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        
        .registration-container {
            max-width: 800px;
            margin: 50px auto;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .registration-header {
            background: linear-gradient(135deg, #1a6a83 0%, #0e4b5e 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .registration-header h3 {
            margin-bottom: 0;
            font-weight: 600;
        }
        
        .registration-form {
            padding: 30px;
        }
        
        .form-group label {
            font-weight: 600;
            color: #495057;
        }
        
        .form-control {
            border-radius: 5px;
            padding: 10px 15px;
            height: auto;
            border: 1px solid #ced4da;
        }
        
        .form-control:focus {
            border-color: #1a6a83;
            box-shadow: 0 0 0 0.2rem rgba(26, 106, 131, 0.25);
        }
        
        .btn-register {
            background: linear-gradient(135deg, #1a6a83 0%, #0e4b5e 100%);
            border: none;
            border-radius: 5px;
            padding: 10px 20px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(26, 106, 131, 0.3);
        }
        
        .logo-container {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .logo-container img {
            height: 60px;
        }
        
        .required-field::after {
            content: " *";
            color: red;
        }
        
        .success-message {
            background-color: #d4edda;
            color: #155724;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="registration-container">
        <div class="registration-header">
            <div class="logo-container">
                <img src="../assets/img/logo.jpeg" alt="Bank BJB">
            </div>
            <h3>Pendaftaran Nasabah</h3>
            <p class="mb-0">Silahkan isi formulir di bawah ini untuk mendaftar sebagai nasabah</p>
        </div>
        
        <div class="registration-form">
            <?php if ($success): ?>
            <div class="success-message">
                <i class="fas fa-check-circle fa-2x mb-2"></i>
                <h4>Pendaftaran Berhasil!</h4>
                <p>Akun Anda telah berhasil dibuat. Silahkan login untuk melanjutkan.</p>
                <a href="login.php" class="btn btn-success mt-2">
                    <i class="fas fa-sign-in-alt mr-1"></i> Login Sekarang
                </a>
            </div>
            <?php else: ?>
            
            <?php if (!empty($error)): ?>
            <div class="error-message">
                <i class="fas fa-exclamation-circle mr-1"></i> <?php echo $error; ?>
            </div>
            <?php endif; ?>
            
            <form action="" method="post" enctype="multipart/form-data">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="nama" class="required-field">Nama Lengkap</label>
                            <input type="text" class="form-control" id="nama" name="nama" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="email" class="required-field">Email</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="password" class="required-field">Password</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="confirm_password" class="required-field">Konfirmasi Password</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="jenis_identitas" class="required-field">Jenis Identitas</label>
                            <select class="form-control" id="jenis_identitas" name="jenis_identitas" required>
                                <option value="ktp">KTP</option>
                                <option value="sim">SIM</option>
                                <option value="passport">Passport</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="no_identitas" class="required-field">Nomor Identitas</label>
                            <input type="text" class="form-control" id="no_identitas" name="no_identitas" required>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="no_hp" class="required-field">Nomor HP</label>
                            <input type="text" class="form-control" id="no_hp" name="no_hp" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="file_identitas" class="required-field">Upload Identitas</label>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input" id="file_identitas" name="file_identitas" required>
                                <label class="custom-file-label" for="file_identitas">Pilih file...</label>
                            </div>
                            <small class="form-text text-muted">Format: JPG, PNG, PDF. Maks: 2MB</small>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="alamat" class="required-field">Alamat</label>
                    <textarea class="form-control" id="alamat" name="alamat" rows="3" required></textarea>
                </div>
                
                <div class="form-group form-check">
                    <input type="checkbox" class="form-check-input" id="terms" name="terms" required>
                    <label class="form-check-label" for="terms">Saya menyetujui syarat dan ketentuan yang berlaku</label>
                </div>
                
                <div class="text-center mt-4">
                    <button type="submit" class="btn btn-primary btn-register">
                        <i class="fas fa-user-plus mr-1"></i> Daftar Sekarang
                    </button>
                </div>
                
                <div class="text-center mt-3">
                    <p>Sudah memiliki akun? <a href="login.php">Login di sini</a></p>
                    <a href="../index.php" class="btn btn-link">
                        <i class="fas fa-arrow-left mr-1"></i> Kembali ke Halaman Utama
                    </a>
                </div>
            </form>
            <?php endif; ?>
        </div>
    </div>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Show filename in custom file input
            $('.custom-file-input').on('change', function() {
                var fileName = $(this).val().split('\\').pop();
                $(this).next('.custom-file-label').html(fileName);
            });
            
            // Form validation
            $('form').on('submit', function(e) {
                var password = $('#password').val();
                var confirmPassword = $('#confirm_password').val();
                
                if (password !== confirmPassword) {
                    e.preventDefault();
                    alert('Password dan konfirmasi password tidak cocok!');
                }
            });
        });
    </script>
</body>
</html>

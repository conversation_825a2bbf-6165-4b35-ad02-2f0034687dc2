            </div>
            <!-- End Content -->
        </div>
        <!-- End Page Content -->
    </div>
    <!-- End Wrapper -->

    <!-- j<PERSON><PERSON><PERSON> and <PERSON>trap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script>
        $(document).ready(function() {
            // Toggle sidebar
            $('#sidebarCollapse').on('click', function() {
                $('#sidebar').toggleClass('active');
            });

            // Update time
            function updateTime() {
                var now = new Date();
                var hours = now.getHours().toString().padStart(2, '0');
                var minutes = now.getMinutes().toString().padStart(2, '0');
                var seconds = now.getSeconds().toString().padStart(2, '0');
                $('#navbar-time').text(hours + ':' + minutes + ':' + seconds);
            }

            // Update time every second
            setInterval(updateTime, 1000);
        });
    </script>
</body>
</html>

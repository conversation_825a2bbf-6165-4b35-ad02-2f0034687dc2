<?php
// Test call queue functionality
require_once 'includes/functions.php';

echo "<h1>Test Call Queue</h1>";

if (isset($_GET['id']) && isset($_GET['service'])) {
    $queue_id = $_GET['id'];
    $service = $_GET['service'];
    
    echo "<h2>Calling Queue ID: $queue_id (Service: $service)</h2>";
    
    // Get queue details
    $sql = "SELECT * FROM antrian WHERE id = $queue_id";
    $result = query($sql);
    
    if ($result && num_rows($result) > 0) {
        $queue = fetch_assoc($result);
        
        echo "<p><strong>Queue Details:</strong></p>";
        echo "<ul>";
        echo "<li>Nomor Antrian: {$queue['nomor_antrian']}</li>";
        echo "<li>Nama: {$queue['nama']}</li>";
        echo "<li>Layanan: {$queue['jenis_layanan']}</li>";
        echo "<li>Status: {$queue['status']}</li>";
        echo "</ul>";
        
        if ($queue['status'] == 'menunggu') {
            // Find available counter for this service
            $sql = "SELECT * FROM loket WHERE jenis_layanan = '$service' AND status = 'aktif' LIMIT 1";
            $result = query($sql);
            
            if ($result && num_rows($result) > 0) {
                $counter = fetch_assoc($result);
                
                // Update queue status to 'dipanggil' and assign to counter
                $sql = "UPDATE antrian SET status = 'dipanggil', loket_id = {$counter['id']}, waktu_dipanggil = NOW() WHERE id = $queue_id";
                
                if (query($sql)) {
                    echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
                    echo "<h3>✅ Queue Called Successfully!</h3>";
                    echo "<p><strong>Nomor Antrian {$queue['nomor_antrian']}</strong> telah dipanggil ke <strong>{$counter['nama_loket']}</strong></p>";
                    echo "<p>Customer: {$queue['nama']}</p>";
                    echo "<p>Service: " . ucfirst($service) . "</p>";
                    echo "</div>";
                    
                    // Show audio notification simulation
                    echo "<div style='background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
                    echo "<h4>🔊 Audio Notification:</h4>";
                    echo "<p><em>\"Nomor antrian {$queue['nomor_antrian']}, silahkan menuju ke {$counter['nama_loket']}\"</em></p>";
                    echo "</div>";
                } else {
                    echo "<p style='color: red;'>❌ Failed to call queue</p>";
                }
            } else {
                echo "<p style='color: red;'>❌ No available counter for service: $service</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ Queue is not in waiting status. Current status: {$queue['status']}</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Queue not found</p>";
    }
} else {
    echo "<h2>Call Next Queue by Service Type</h2>";
    
    $services = ['teller', 'cs', 'kredit'];
    
    foreach ($services as $service) {
        echo "<h3>" . ucfirst($service) . " Service</h3>";
        
        // Get next waiting queue for this service
        $sql = "SELECT * FROM antrian WHERE jenis_layanan = '$service' AND status = 'menunggu' AND tanggal = CURDATE() ORDER BY id LIMIT 1";
        $result = query($sql);
        
        if ($result && num_rows($result) > 0) {
            $queue = fetch_assoc($result);
            
            echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
            echo "<p><strong>Next Queue:</strong> {$queue['nomor_antrian']} - {$queue['nama']}</p>";
            echo "<p><strong>Booking Time:</strong> " . date('H:i', strtotime($queue['waktu_booking'])) . "</p>";
            echo "<a href='test_call_queue.php?id={$queue['id']}&service=$service' style='display: inline-block; padding: 8px 15px; background-color: #007bff; color: white; text-decoration: none; border-radius: 3px;'>Call This Queue</a>";
            echo "</div>";
        } else {
            echo "<p>No waiting queues for $service service</p>";
        }
        
        // Show current called queue for this service
        $sql = "SELECT a.*, l.nama_loket FROM antrian a LEFT JOIN loket l ON a.loket_id = l.id WHERE a.jenis_layanan = '$service' AND a.status = 'dipanggil' AND a.tanggal = CURDATE() ORDER BY a.waktu_dipanggil DESC LIMIT 1";
        $result = query($sql);
        
        if ($result && num_rows($result) > 0) {
            $current = fetch_assoc($result);
            echo "<div style='background-color: #d1ecf1; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            echo "<p><strong>Currently Called:</strong> {$current['nomor_antrian']} - {$current['nama']} at {$current['nama_loket']}</p>";
            echo "<a href='test_complete_service.php?id={$current['id']}' style='display: inline-block; padding: 5px 10px; background-color: #28a745; color: white; text-decoration: none; border-radius: 3px; font-size: 12px;'>Complete Service</a>";
            echo "</div>";
        }
    }
}

echo "<br><br>";
echo "<a href='test_complete_system.php' style='display: inline-block; padding: 10px 15px; background-color: #6c757d; color: white; text-decoration: none; border-radius: 3px;'>Back to System Test</a>";
?>

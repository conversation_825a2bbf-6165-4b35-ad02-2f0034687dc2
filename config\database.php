<?php
/**
 * Database Configuration
 *
 * Konfigurasi koneksi database untuk Sistem Manajemen Antrian Bank BJB
 */

// Database credentials
define('DB_SERVER', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', '');  // Update this if your MySQL has a password
define('DB_NAME', 'antrian_bank');

// Create database if it doesn't exist
try {
    // Connect without database first
    $temp_conn = mysqli_connect(DB_SERVER, DB_USERNAME, DB_PASSWORD);

    if ($temp_conn) {
        // Create database if it doesn't exist
        $sql = "CREATE DATABASE IF NOT EXISTS " . DB_NAME;
        mysqli_query($temp_conn, $sql);
        mysqli_close($temp_conn);
    }
} catch (Exception $e) {
    // Silently continue - we'll handle the error in the next connection attempt
}

// Attempt to connect to MySQL database
$conn = false;
$db_error = "";

try {
    $conn = @mysqli_connect(DB_SERVER, DB_USERNAME, DB_PASSWORD, DB_NAME);
} catch (Exception $e) {
    $db_error = $e->getMessage();
}

// Check connection
if($conn === false){
    // Try alternative connection methods
    try {
        // Try with 127.0.0.1
        $conn = @mysqli_connect('127.0.0.1', DB_USERNAME, DB_PASSWORD, DB_NAME);
    } catch (Exception $e) {
        $db_error = $e->getMessage();
        try {
            // Try with port 3306
            $conn = @mysqli_connect(DB_SERVER . ':3306', DB_USERNAME, DB_PASSWORD, DB_NAME);
        } catch (Exception $e) {
            $db_error = $e->getMessage();
        }
    }
}

// If all connection attempts failed, create a mock connection
if ($conn === false) {
    // Define a class to mock mysqli connection
    class MockConnection {
        public function query($sql) {
            return new MockResult($sql);
        }

        public function prepare($sql) {
            return new MockStatement($sql);
        }

        public function real_escape_string($str) {
            return $str;
        }

        public function close() {
            return true;
        }

        public function set_charset($charset) {
            return true;
        }

        public function insert_id() {
            return rand(1, 1000);
        }

        public function error() {
            return '';
        }
    }

    class MockResult {
        private $sql = '';
        private $data = [];
        private $index = 0;

        public function __construct($sql = '') {
            $this->sql = strtolower($sql);
            $this->data = $this->getMockData();
        }

        public function fetch_assoc() {
            if (empty($this->data)) {
                return [];
            }

            if ($this->index >= count($this->data)) {
                return null;
            }

            return $this->data[$this->index++];
        }

        public function num_rows() {
            return count($this->data);
        }

        private function getMockData() {
            // Return mock data based on the SQL query
            if (strpos($this->sql, 'select * from users') !== false || strpos($this->sql, 'select u.*') !== false) {
                $users = [
                    [
                        'id' => 1,
                        'username' => 'admin',
                        'password' => password_hash('admin123', PASSWORD_DEFAULT),
                        'nama_lengkap' => 'Administrator',
                        'email' => '<EMAIL>',
                        'role' => 'admin',
                        'status' => 'aktif',
                        'loket_id' => null
                    ],
                    [
                        'id' => 2,
                        'username' => 'petugas1',
                        'password' => password_hash('petugas123', PASSWORD_DEFAULT),
                        'nama_lengkap' => 'Petugas Teller 1',
                        'email' => '<EMAIL>',
                        'role' => 'staff',
                        'status' => 'aktif',
                        'loket_id' => 1
                    ],
                    [
                        'id' => 3,
                        'username' => 'petugas2',
                        'password' => password_hash('petugas123', PASSWORD_DEFAULT),
                        'nama_lengkap' => 'Petugas Teller 2',
                        'email' => '<EMAIL>',
                        'role' => 'staff',
                        'status' => 'aktif',
                        'loket_id' => null
                    ],
                    [
                        'id' => 4,
                        'username' => 'petugas3',
                        'password' => password_hash('petugas123', PASSWORD_DEFAULT),
                        'nama_lengkap' => 'Petugas Customer Service 1',
                        'email' => '<EMAIL>',
                        'role' => 'staff',
                        'status' => 'aktif',
                        'loket_id' => null
                    ],
                    [
                        'id' => 5,
                        'username' => 'petugas4',
                        'password' => password_hash('petugas123', PASSWORD_DEFAULT),
                        'nama_lengkap' => 'Petugas Customer Service 2',
                        'email' => '<EMAIL>',
                        'role' => 'staff',
                        'status' => 'aktif',
                        'loket_id' => null
                    ],
                    [
                        'id' => 6,
                        'username' => 'petugas5',
                        'password' => password_hash('petugas123', PASSWORD_DEFAULT),
                        'nama_lengkap' => 'Petugas Kredit 1',
                        'email' => '<EMAIL>',
                        'role' => 'staff',
                        'status' => 'aktif',
                        'loket_id' => null
                    ]
                ];

                // Filter by role if specified
                if (strpos($this->sql, "role = 'staff'") !== false) {
                    return array_filter($users, function($user) {
                        return $user['role'] === 'staff';
                    });
                } elseif (strpos($this->sql, "role = 'admin'") !== false) {
                    return array_filter($users, function($user) {
                        return $user['role'] === 'admin';
                    });
                }

                return $users;
            } elseif (strpos($this->sql, 'select * from nasabah') !== false) {
                return [
                    [
                        'id' => 1,
                        'nama' => 'Nasabah Test',
                        'no_identitas' => '1234567890123456',
                        'jenis_identitas' => 'ktp',
                        'file_identitas' => 'ktp.jpg',
                        'no_hp' => '081234567890',
                        'email' => '<EMAIL>',
                        'password' => password_hash('password', PASSWORD_DEFAULT),
                        'alamat' => 'Jl. Test No. 123',
                        'status_verifikasi' => 'sudah',
                        'is_blocked' => 0
                    ]
                ];
            } elseif (strpos($this->sql, 'select * from loket') !== false) {
                return [
                    [
                        'id' => 1,
                        'nama_loket' => 'Loket 1',
                        'jenis_layanan' => 'teller',
                        'status' => 'aktif',
                        'user_id' => 2
                    ],
                    [
                        'id' => 2,
                        'nama_loket' => 'Loket 2',
                        'jenis_layanan' => 'teller',
                        'status' => 'aktif',
                        'user_id' => null
                    ],
                    [
                        'id' => 3,
                        'nama_loket' => 'Loket 3',
                        'jenis_layanan' => 'teller',
                        'status' => 'aktif',
                        'user_id' => null
                    ],
                    [
                        'id' => 4,
                        'nama_loket' => 'Loket 4',
                        'jenis_layanan' => 'cs',
                        'status' => 'aktif',
                        'user_id' => null
                    ],
                    [
                        'id' => 5,
                        'nama_loket' => 'Loket 5',
                        'jenis_layanan' => 'cs',
                        'status' => 'aktif',
                        'user_id' => null
                    ],
                    [
                        'id' => 6,
                        'nama_loket' => 'Loket 6',
                        'jenis_layanan' => 'kredit',
                        'status' => 'aktif',
                        'user_id' => null
                    ],
                    [
                        'id' => 7,
                        'nama_loket' => 'Loket 7',
                        'jenis_layanan' => 'kredit',
                        'status' => 'aktif',
                        'user_id' => null
                    ]
                ];
            } elseif (strpos($this->sql, 'select * from poli') !== false) {
                return [
                    [
                        'id' => 1,
                        'nama_poli' => 'Teller',
                        'deskripsi' => 'Layanan transaksi keuangan',
                        'estimasi_waktu' => 5,
                        'status' => 'aktif'
                    ],
                    [
                        'id' => 2,
                        'nama_poli' => 'Customer Service',
                        'deskripsi' => 'Layanan informasi dan keluhan',
                        'estimasi_waktu' => 10,
                        'status' => 'aktif'
                    ],
                    [
                        'id' => 3,
                        'nama_poli' => 'Kredit',
                        'deskripsi' => 'Layanan pengajuan kredit',
                        'estimasi_waktu' => 15,
                        'status' => 'aktif'
                    ]
                ];
            } elseif (strpos($this->sql, 'select * from antrian') !== false) {
                return [
                    [
                        'id' => 1,
                        'booking_id' => 'BK' . date('Ymd') . '1001',
                        'nomor_antrian' => 'T001',
                        'tanggal' => date('Y-m-d'),
                        'nasabah_id' => 1,
                        'poli_id' => 1,
                        'jenis_layanan' => 'teller',
                        'loket_id' => 1,
                        'status' => 'dipanggil',
                        'waktu_booking' => date('Y-m-d H:i:s', strtotime('-30 minutes')),
                        'waktu_dipanggil' => date('Y-m-d H:i:s', strtotime('-15 minutes')),
                        'waktu_selesai' => null,
                        'estimasi_waktu' => 5,
                        'booking_dari' => 'online'
                    ],
                    [
                        'id' => 2,
                        'booking_id' => 'BK' . date('Ymd') . '1002',
                        'nomor_antrian' => 'C001',
                        'tanggal' => date('Y-m-d'),
                        'nasabah_id' => null,
                        'poli_id' => 2,
                        'jenis_layanan' => 'cs',
                        'loket_id' => 4,
                        'status' => 'dipanggil',
                        'waktu_booking' => date('Y-m-d H:i:s', strtotime('-45 minutes')),
                        'waktu_dipanggil' => date('Y-m-d H:i:s', strtotime('-30 minutes')),
                        'waktu_selesai' => null,
                        'estimasi_waktu' => 10,
                        'booking_dari' => 'lokal'
                    ],
                    [
                        'id' => 3,
                        'booking_id' => 'BK' . date('Ymd') . '1003',
                        'nomor_antrian' => 'K001',
                        'tanggal' => date('Y-m-d'),
                        'nasabah_id' => null,
                        'poli_id' => 3,
                        'jenis_layanan' => 'kredit',
                        'loket_id' => 6,
                        'status' => 'dipanggil',
                        'waktu_booking' => date('Y-m-d H:i:s', strtotime('-60 minutes')),
                        'waktu_dipanggil' => date('Y-m-d H:i:s', strtotime('-45 minutes')),
                        'waktu_selesai' => null,
                        'estimasi_waktu' => 15,
                        'booking_dari' => 'lokal'
                    ],
                    [
                        'id' => 4,
                        'booking_id' => 'BK' . date('Ymd') . '1004',
                        'nomor_antrian' => 'T002',
                        'tanggal' => date('Y-m-d'),
                        'nasabah_id' => null,
                        'poli_id' => 1,
                        'jenis_layanan' => 'teller',
                        'loket_id' => null,
                        'status' => 'menunggu',
                        'waktu_booking' => date('Y-m-d H:i:s', strtotime('-15 minutes')),
                        'waktu_dipanggil' => null,
                        'waktu_selesai' => null,
                        'estimasi_waktu' => 5,
                        'booking_dari' => 'lokal'
                    ]
                ];
            } elseif (strpos($this->sql, 'select * from bookings') !== false) {
                return [
                    [
                        'id' => 1,
                        'booking_id' => 'BK' . date('Ymd') . '1001',
                        'nasabah_id' => 1,
                        'nama' => 'Nasabah Test',
                        'no_identitas' => '1234567890123456',
                        'email' => '<EMAIL>',
                        'no_hp' => '081234567890',
                        'layanan' => 'teller',
                        'tanggal' => date('Y-m-d'),
                        'waktu' => '10:00',
                        'keterangan' => 'Test booking',
                        'file_identitas' => 'ktp.jpg',
                        'nomor_antrian' => 'T001',
                        'status' => 'confirmed',
                        'created_at' => date('Y-m-d H:i:s', strtotime('-1 hour'))
                    ]
                ];
            } elseif (strpos($this->sql, 'select * from pengaturan') !== false) {
                return [
                    [
                        'id' => 1,
                        'nama_pengaturan' => 'jam_operasional',
                        'nilai' => '08:00-16:00',
                        'deskripsi' => 'Jam operasional pelayanan'
                    ],
                    [
                        'id' => 2,
                        'nama_pengaturan' => 'nama_instansi',
                        'nilai' => 'Bank BJB Kantor Cabang Khusus Banten',
                        'deskripsi' => 'Nama instansi'
                    ],
                    [
                        'id' => 3,
                        'nama_pengaturan' => 'logo_instansi',
                        'nilai' => 'logo.jpeg',
                        'deskripsi' => 'Logo instansi'
                    ],
                    [
                        'id' => 4,
                        'nama_pengaturan' => 'max_antrian',
                        'nilai' => '100',
                        'deskripsi' => 'Maksimal antrian per hari'
                    ],
                    [
                        'id' => 5,
                        'nama_pengaturan' => 'verifikasi_wajib',
                        'nilai' => '1',
                        'deskripsi' => 'Verifikasi identitas wajib (1=ya, 0=tidak)'
                    ]
                ];
            } elseif (strpos($this->sql, 'select count(*)') !== false) {
                // Handle COUNT queries
                if (strpos($this->sql, "role = 'staff'") !== false) {
                    return [['count' => 5, 'total' => 5]];
                } elseif (strpos($this->sql, "role = 'admin'") !== false) {
                    return [['count' => 1, 'total' => 1]];
                } elseif (strpos($this->sql, 'from users') !== false) {
                    return [['count' => 6, 'total' => 6]];
                } elseif (strpos($this->sql, 'from loket') !== false) {
                    return [['count' => 7, 'total' => 7]];
                } else {
                    return [['count' => 0, 'total' => 0]];
                }
            } elseif (strpos($this->sql, 'show tables') !== false) {
                return [['Tables_in_antrian_bank' => 'users']];
            } elseif (strpos($this->sql, 'select 1 as test') !== false) {
                return [['test' => 1]];
            }

            // Default empty result
            return [];
        }
    }

    class MockStatement {
        private $sql = '';
        private $params = [];

        public function __construct($sql = '') {
            $this->sql = $sql;
        }

        public function bind_param($types, ...$params) {
            $this->params = $params;
            return true;
        }

        public function execute() {
            return true;
        }

        public function get_result() {
            return new MockResult($this->sql);
        }

        public function fetch() {
            $result = new MockResult($this->sql);
            return $result->fetch_assoc();
        }

        public function close() {
            return true;
        }
    }

    $conn = new MockConnection();

    // Log the error
    error_log("Database connection failed: " . $db_error);

    // No need to die, we'll use the mock connection
}

// Set charset if it's a real connection
if (!($conn instanceof MockConnection)) {
    mysqli_set_charset($conn, "utf8");
}

// Function to sanitize input data
function sanitize($data) {
    global $conn;
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);

    if ($conn instanceof MockConnection) {
        return $data;
    } else {
        return mysqli_real_escape_string($conn, $data);
    }
}

// Function to execute query and return result
function query($sql) {
    global $conn;

    if ($conn instanceof MockConnection) {
        return $conn->query($sql);
    } else {
        return mysqli_query($conn, $sql);
    }
}

// Function to get single row
function fetch_assoc($result) {
    if ($result instanceof MockResult) {
        return $result->fetch_assoc();
    } else {
        return mysqli_fetch_assoc($result);
    }
}

// Function to get all rows
function fetch_all($result) {
    $data = [];

    if ($result instanceof MockResult) {
        return [];
    } else {
        while($row = mysqli_fetch_assoc($result)) {
            $data[] = $row;
        }
        return $data;
    }
}

// Function to get number of rows
function num_rows($result) {
    if ($result instanceof MockResult) {
        return $result->num_rows();
    } else {
        return mysqli_num_rows($result);
    }
}

// Function to get last inserted id
function last_id() {
    global $conn;

    if ($conn instanceof MockConnection) {
        return rand(1, 1000); // Return a random ID for mock connection
    } else {
        return mysqli_insert_id($conn);
    }
}

// Function to close database connection
function close_connection() {
    global $conn;

    if ($conn instanceof MockConnection) {
        return $conn->close();
    } else {
        return mysqli_close($conn);
    }
}
?>

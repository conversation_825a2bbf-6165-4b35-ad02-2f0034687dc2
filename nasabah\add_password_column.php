<?php
// <PERSON><PERSON><PERSON> to add password column to nasabah table
require_once '../includes/db.php';

// Check if password column already exists
$result = $conn->query("SHOW COLUMNS FROM nasabah LIKE 'password'");
$exists = ($result->num_rows > 0);

if (!$exists) {
    // Add password column to nasabah table
    $sql = "ALTER TABLE nasabah ADD COLUMN password VARCHAR(255) NOT NULL AFTER email";
    
    if ($conn->query($sql) === TRUE) {
        echo "Password column added successfully to nasabah table.";
    } else {
        echo "Error adding password column: " . $conn->error;
    }
} else {
    echo "Password column already exists in nasabah table.";
}

echo "<br><br><a href='register.php'>Go to Registration Page</a>";
?>

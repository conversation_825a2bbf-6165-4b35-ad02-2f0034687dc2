<?php
// Waiting list partial view
session_start();

// Include database configuration
require_once '../config/database.php';
require_once '../includes/functions_mock.php';

// Check if user is logged in and is staff
if (!is_logged_in() || !is_staff()) {
    echo '<div class="alert alert-danger">Unauthorized access.</div>';
    exit;
}

// Get user's counter
$user_id = $_SESSION['user_id'];
$sql = "SELECT * FROM loket WHERE user_id = $user_id AND status = 'aktif'";
$result = query($sql);

if (num_rows($result) == 0) {
    echo '<div class="alert alert-warning">Anda belum memilih loket.</div>';
    exit;
}

$counter = fetch_assoc($result);

// Get waiting queues using FIFO (First In First Out) method
$sql = "SELECT a.*, n.nama, p.nama_poli, b.layanan as jenis_layanan
        FROM antrian a
        LEFT JOIN nasabah n ON a.nasabah_id = n.id
        LEFT JOIN poli p ON a.poli_id = p.id
        LEFT JOIN bookings b ON a.booking_id = b.booking_id
        WHERE a.status = 'menunggu'
        AND a.tanggal = CURDATE()
        ORDER BY a.created_at ASC
        LIMIT 10";
$result = query($sql);
$waiting_queues = fetch_all($result);
?>

<div class="table-responsive">
    <table class="table table-striped table-hover mb-0">
        <thead>
            <tr>
                <th>No. Antrian</th>
                <th>Nama</th>
                <th>Layanan</th>
                <th>Waktu Tunggu</th>
                <th>Aksi</th>
            </tr>
        </thead>
        <tbody>
            <?php if(count($waiting_queues) > 0): ?>
            <?php foreach($waiting_queues as $queue): ?>
            <tr>
                <td><span class="badge badge-primary p-2"><?php echo $queue['nomor_antrian']; ?></span></td>
                <td><?php echo $queue['nama']; ?></td>
                <td>
                    <?php
                    if (!empty($queue['jenis_layanan'])) {
                        // Display service from booking
                        $layanan = '';
                        switch($queue['jenis_layanan']) {
                            case 'teller':
                                $layanan = 'Teller';
                                break;
                            case 'cs':
                                $layanan = 'Customer Service';
                                break;
                            case 'kredit':
                                $layanan = 'Kredit';
                                break;
                            default:
                                $layanan = $queue['nama_poli'];
                        }
                        echo $layanan;
                    } else {
                        // Fallback to poli name
                        echo $queue['nama_poli'];
                    }
                    ?>
                </td>
                <td>
                    <?php
                    // Use waktu_booking if available, otherwise use created_at
                    $booking_time = !empty($queue['waktu_booking']) ? $queue['waktu_booking'] : $queue['created_at'];
                    $wait_time = round((time() - strtotime($booking_time)) / 60);
                    echo $wait_time . ' menit';
                    ?>
                </td>
                <td>
                    <button class="btn btn-sm btn-primary btn-call-direct shadow-sm" data-counter="<?php echo $counter['id']; ?>" data-queue="<?php echo $queue['id']; ?>">
                        <i class="fas fa-bullhorn mr-1"></i> Panggil
                    </button>
                </td>
            </tr>
            <?php endforeach; ?>
            <?php else: ?>
            <tr>
                <td colspan="5" class="text-center py-4">
                    <i class="fas fa-info-circle text-info mr-2"></i> Tidak ada antrian yang menunggu.
                </td>
            </tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>

<?php
// Admin dashboard page
$page_title = 'Dashboard Admin';
$active_menu = 'dashboard';
$is_admin = true;
require_once '../includes/functions_mock.php';

// Check if user is logged in
if (!is_logged_in()) {
    $_SESSION['error_message'] = 'Anda harus login terlebih dahulu untuk mengakses halaman ini.';
    redirect('../login_admin.php');
}

// Check if user is admin
if (!is_admin()) {
    $_SESSION['error_message'] = 'Anda tidak memiliki akses ke halaman ini.';
    redirect('../index.php');
}

// Get today's date
$today = date('Y-m-d');

// Get statistics
try {
    global $conn;

    if ($conn instanceof MockConnection) {
        // Using mock data
        $total_antrian = rand(50, 100);
        $antrian_selesai = rand(30, 50);
        $antrian_menunggu = rand(10, 20);
        $total_loket = rand(5, 10);
        $total_petugas = rand(10, 20);
        $total_nasabah = rand(100, 200);

        // Latest queues
        $latest_queues = [
            [
                'nomor' => 'T001',
                'nama' => 'Budi Santoso',
                'layanan' => 'Teller',
                'status' => 'selesai',
                'waktu' => '09:15'
            ],
            [
                'nomor' => 'C002',
                'nama' => 'Siti Aminah',
                'layanan' => 'Customer Service',
                'status' => 'dipanggil',
                'waktu' => '09:30'
            ],
            [
                'nomor' => 'K003',
                'nama' => 'Ahmad Hidayat',
                'layanan' => 'Kredit',
                'status' => 'menunggu',
                'waktu' => '09:45'
            ],
            [
                'nomor' => 'T004',
                'nama' => 'Dewi Lestari',
                'layanan' => 'Teller',
                'status' => 'menunggu',
                'waktu' => '10:00'
            ],
            [
                'nomor' => 'C005',
                'nama' => 'Joko Widodo',
                'layanan' => 'Customer Service',
                'status' => 'menunggu',
                'waktu' => '10:15'
            ]
        ];
    } else {
        // Using real database
        // Get total antrian
        $sql = "SELECT COUNT(*) as total FROM antrian WHERE DATE(tanggal) = '$today'";
        $result = query($sql);
        $row = fetch_assoc($result);
        $total_antrian = $row['total'];

        // Get antrian selesai
        $sql = "SELECT COUNT(*) as total FROM antrian WHERE DATE(tanggal) = '$today' AND status = 'selesai'";
        $result = query($sql);
        $row = fetch_assoc($result);
        $antrian_selesai = $row['total'];

        // Get antrian menunggu
        $sql = "SELECT COUNT(*) as total FROM antrian WHERE DATE(tanggal) = '$today' AND status = 'menunggu'";
        $result = query($sql);
        $row = fetch_assoc($result);
        $antrian_menunggu = $row['total'];

        // Get total loket
        $sql = "SELECT COUNT(*) as total FROM loket";
        $result = query($sql);
        $row = fetch_assoc($result);
        $total_loket = $row['total'];

        // Get total petugas
        $sql = "SELECT COUNT(*) as total FROM users WHERE role = 'petugas'";
        $result = query($sql);
        $row = fetch_assoc($result);
        $total_petugas = $row['total'];

        // Get total nasabah
        $sql = "SELECT COUNT(*) as total FROM nasabah";
        $result = query($sql);
        $row = fetch_assoc($result);
        $total_nasabah = $row['total'];

        // Get latest queues
        $sql = "SELECT a.nomor, n.nama, l.nama as layanan, a.status, TIME(a.waktu_panggil) as waktu
                FROM antrian a
                JOIN nasabah n ON a.nasabah_id = n.id
                JOIN layanan l ON a.layanan_id = l.id
                WHERE DATE(a.tanggal) = '$today'
                ORDER BY a.id DESC LIMIT 5";
        $result = query($sql);

        $latest_queues = [];
        while ($row = fetch_assoc($result)) {
            $latest_queues[] = $row;
        }
    }
} catch (Exception $e) {
    // Log the error
    error_log("Error fetching statistics: " . $e->getMessage());

    // Use default values
    $total_antrian = 0;
    $antrian_selesai = 0;
    $antrian_menunggu = 0;
    $total_loket = 0;
    $total_petugas = 0;
    $total_nasabah = 0;
    $latest_queues = [];
}

// Include header
include 'includes/header.php';
?>

<h2 class="content-title mb-4">Dashboard Admin</h2>

<div class="row">
    <div class="col-md-4">
        <div class="dashboard-card bg-primary text-white">
            <div class="icon"><i class="fas fa-users"></i></div>
            <div class="count"><?php echo $total_antrian; ?></div>
            <div class="title">Total Antrian Hari Ini</div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="dashboard-card bg-success text-white">
            <div class="icon"><i class="fas fa-check-circle"></i></div>
            <div class="count"><?php echo $antrian_selesai; ?></div>
            <div class="title">Antrian Selesai</div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="dashboard-card bg-warning text-white">
            <div class="icon"><i class="fas fa-clock"></i></div>
            <div class="count"><?php echo $antrian_menunggu; ?></div>
            <div class="title">Antrian Menunggu</div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-4">
        <div class="dashboard-card bg-info text-white">
            <div class="icon"><i class="fas fa-door-open"></i></div>
            <div class="count"><?php echo $total_loket; ?></div>
            <div class="title">Total Loket</div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="dashboard-card bg-secondary text-white">
            <div class="icon"><i class="fas fa-user-tie"></i></div>
            <div class="count"><?php echo $total_petugas; ?></div>
            <div class="title">Total Petugas</div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="dashboard-card bg-dark text-white">
            <div class="icon"><i class="fas fa-user-friends"></i></div>
            <div class="count"><?php echo $total_nasabah; ?></div>
            <div class="title">Total Nasabah</div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <?php include 'components/latest_queue.php'; ?>
    </div>
</div>

<?php
// Include footer
include 'includes/footer.php';
?>


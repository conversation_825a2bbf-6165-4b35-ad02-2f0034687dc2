<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include functions if not already included
if (!function_exists('is_logged_in')) {
    require_once __DIR__ . '/functions.php';
}

// Get site settings
$nama_instansi = get_setting('nama_instansi');
$logo_instansi = get_setting('logo_instansi');
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?><?php echo $nama_instansi; ?></title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo isset($is_admin) ? '../' : ''; ?>assets/css/style.css">

    <!-- New Sidebar CSS -->
    <link rel="stylesheet" href="<?php echo isset($is_admin) ? '../' : ''; ?>assets/css/sidebar-new.css?v=<?php echo time(); ?>">

    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo isset($is_admin) ? '../' : ''; ?>assets/img/favicon.ico" type="image/x-icon">

    <?php if(isset($extra_css)): ?>
    <?php echo $extra_css; ?>
    <?php endif; ?>

    <!-- jQuery (early load) -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>

    <!-- Inline script for sidebar toggle -->
    <script>
        $(document).ready(function() {
            // Direct sidebar toggle handler
            $(document).on('click', '#sidebarCollapse', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $('#sidebar').toggleClass('active');
                $('#content').toggleClass('active');
                console.log('Sidebar toggle clicked (inline)');
            });

            // Update time every second
            setInterval(function() {
                var now = new Date();
                var hours = now.getHours().toString().padStart(2, '0');
                var minutes = now.getMinutes().toString().padStart(2, '0');
                var seconds = now.getSeconds().toString().padStart(2, '0');
                $('#navbar-time').text(hours + ':' + minutes + ':' + seconds);
            }, 1000);

            // Add animation delay to menu items for a staggered effect
            $('#sidebar ul.components li').each(function(index) {
                $(this).css({
                    'animation': 'fadeInLeft 0.3s ease forwards',
                    'animation-delay': (index * 0.05) + 's',
                    'opacity': '0'
                });
            });
        });
    </script>

    <style>
        @keyframes fadeInLeft {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
    </style>
</head>
<body class="<?php echo isset($body_class) ? $body_class : ''; ?>">
    <?php if(!isset($no_header) && is_logged_in()): ?>
    <div class="wrapper">
        <!-- Sidebar -->
        <nav id="sidebar">
            <div class="sidebar-header">
                <div class="logo-container">
                    <?php
                    // Determine the current directory
                    $current_dir = dirname($_SERVER['PHP_SELF']);
                    $is_staff = strpos($current_dir, '/staff') !== false;
                    $is_admin = strpos($current_dir, '/admin') !== false;

                    if($is_admin || $is_staff):
                    ?>
                    <img src="../assets/img/logo.jpeg" alt="Bank BJB" class="logo-bjb">
                    <?php else: ?>
                    <img src="assets/img/logo.jpeg" alt="Bank BJB" class="logo-bjb">
                    <?php endif; ?>
                </div>
                <h4>Bank BJB Kantor<br>Cabang Khusus<br>Banten</h4>
                <p><?php echo $_SESSION['nama_lengkap']; ?></p>
            </div>

            <ul class="list-unstyled components">
                <?php if(is_admin()): ?>
                <!-- Menu Utama -->
                <li class="<?php echo isset($active_menu) && $active_menu == 'dashboard' ? 'active' : ''; ?>">
                    <a href="<?php echo isset($is_admin) ? './' : 'admin/'; ?>">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                </li>

                <!-- Menu Booking Online -->
                <li class="<?php echo isset($active_menu) && $active_menu == 'bookings' ? 'active' : ''; ?>">
                    <a href="<?php echo isset($is_admin) ? './bookings.php' : 'admin/bookings.php'; ?>">
                        <i class="fas fa-calendar-check"></i> Booking Online
                    </a>
                </li>

                <!-- Menu Verifikasi -->
                <li class="<?php echo isset($active_menu) && $active_menu == 'verifikasi' ? 'active' : ''; ?>">
                    <a href="<?php echo isset($is_admin) ? './verifikasi.php' : 'admin/verifikasi.php'; ?>">
                        <i class="fas fa-id-card"></i> Verifikasi Identitas
                    </a>
                </li>

                <!-- Menu Nasabah -->
                <li class="<?php echo isset($active_menu) && $active_menu == 'nasabah' ? 'active' : ''; ?>">
                    <a href="<?php echo isset($is_admin) ? './nasabah.php' : 'admin/nasabah.php'; ?>">
                        <i class="fas fa-users"></i> Nasabah
                    </a>
                </li>

                <!-- Menu Petugas -->
                <li class="<?php echo isset($active_menu) && $active_menu == 'petugas' ? 'active' : ''; ?>">
                    <a href="<?php echo isset($is_admin) ? './petugas.php' : 'admin/petugas.php'; ?>">
                        <i class="fas fa-user-tie"></i> Petugas
                    </a>
                </li>

                <!-- Menu Statistik -->
                <li class="<?php echo isset($active_menu) && $active_menu == 'statistik' ? 'active' : ''; ?>">
                    <a href="<?php echo isset($is_admin) ? './statistik.php' : 'admin/statistik.php'; ?>">
                        <i class="fas fa-chart-line"></i> Statistik & Analitik
                    </a>
                </li>

                <!-- Menu Pengaturan -->
                <li class="<?php echo isset($active_menu) && $active_menu == 'pengaturan' ? 'active' : ''; ?>">
                    <a href="<?php echo isset($is_admin) ? './pengaturan.php' : 'admin/pengaturan.php'; ?>">
                        <i class="fas fa-cog"></i> Pengaturan
                    </a>
                </li>
                <?php elseif(is_staff()): ?>
                <li class="<?php echo isset($active_menu) && $active_menu == 'loket' ? 'active' : ''; ?>">
                    <a href="<?php echo isset($is_admin) ? './index.php' : 'staff/index.php'; ?>">
                        <i class="fas fa-door-open"></i> Loket Pelayanan
                    </a>
                </li>
                <li class="<?php echo isset($active_menu) && $active_menu == 'riwayat' ? 'active' : ''; ?>">
                    <a href="<?php echo isset($is_admin) ? './riwayat.php' : 'staff/riwayat.php'; ?>">
                        <i class="fas fa-history"></i> Riwayat Antrian
                    </a>
                </li>
                <?php endif; ?>

                <li>
                    <a href="<?php echo isset($is_admin) ? '../logout.php' : '../logout.php'; ?>">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Page Content -->
        <div id="content">
            <nav class="navbar navbar-expand-lg navbar-light bg-light shadow-sm">
                <div class="container-fluid">

                    <div class="d-flex align-items-center">
                        <span class="ml-3 font-weight-bold h5 mb-0"><?php echo isset($page_title) ? $page_title : 'Dashboard'; ?></span>
                    </div>

                    <div class="ml-auto d-flex align-items-center">
                        <div class="text-muted mr-3">
                            <i class="fas fa-calendar-day mr-1"></i> <?php echo tanggal_indonesia(date('Y-m-d')); ?>
                        </div>
                        <div class="text-primary mr-4 font-weight-bold" id="navbar-clock">
                            <i class="fas fa-clock mr-1"></i> <span id="navbar-time">00:00:00</span>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-light dropdown-toggle shadow-sm d-flex align-items-center" type="button" id="userDropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <div class="bg-primary text-white rounded-circle p-2 mr-2">
                                    <i class="fas fa-user"></i>
                                </div>
                                <span><?php echo $_SESSION['nama_lengkap']; ?></span>
                            </button>
                            <div class="dropdown-menu dropdown-menu-right shadow" aria-labelledby="userDropdown">
                                <div class="dropdown-item-text">
                                    <small class="text-muted">Login sebagai</small><br>
                                    <strong><?php echo ucfirst($_SESSION['role']); ?></strong>
                                </div>
                                <div class="dropdown-divider"></div>
                                <?php if(is_admin()): ?>
                                <a class="dropdown-item" href="<?php echo isset($is_admin) ? './pengaturan.php' : 'admin/pengaturan.php'; ?>">
                                    <i class="fas fa-cog mr-2"></i> Pengaturan
                                </a>
                                <?php endif; ?>
                                <a class="dropdown-item text-danger" href="<?php echo isset($is_admin) ? '../logout.php' : '../logout.php'; ?>">
                                    <i class="fas fa-sign-out-alt mr-2"></i> Logout
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </nav>

            <?php if(isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo $_SESSION['success_message']; ?>
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <?php unset($_SESSION['success_message']); ?>
            <?php endif; ?>

            <?php if(isset($_SESSION['error_message'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo $_SESSION['error_message']; ?>
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <?php unset($_SESSION['error_message']); ?>
            <?php endif; ?>
    <?php else: ?>
    <main class="py-4">
        <div class="container">
            <?php if(isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo $_SESSION['success_message']; ?>
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <?php unset($_SESSION['success_message']); ?>
            <?php endif; ?>

            <?php if(isset($_SESSION['error_message'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo $_SESSION['error_message']; ?>
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <?php unset($_SESSION['error_message']); ?>
            <?php endif; ?>
    <?php endif; ?>

<?php
// Login process for nasabah
require_once '../includes/functions.php';
require_once '../includes/db.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if form is submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $email = isset($_POST['email']) ? trim($_POST['email']) : '';
    $password = isset($_POST['password']) ? $_POST['password'] : '';
    $remember = isset($_POST['remember']) ? true : false;

    // Validate required fields
    $errors = [];

    if (empty($email)) {
        $errors[] = "Email harus diisi";
    }

    if (empty($password)) {
        $errors[] = "Password harus diisi";
    }

    // If there are errors, redirect back with error messages
    if (!empty($errors)) {
        $_SESSION['error_messages'] = $errors;
        $_SESSION['form_data'] = ['email' => $email]; // Save email for repopulating the form
        header("Location: login.php");
        exit;
    }

    // Check if user exists
    $stmt = $conn->prepare("SELECT * FROM nasabah WHERE email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        $_SESSION['error_messages'] = ["Email atau password salah"];
        $_SESSION['form_data'] = ['email' => $email];
        header("Location: login.php");
        exit;
    }

    $user = $result->fetch_assoc();

    // Verify password
    if (!password_verify($password, $user['password'])) {
        $_SESSION['error_messages'] = ["Email atau password salah"];
        $_SESSION['form_data'] = ['email' => $email];
        header("Location: login.php");
        exit;
    }

    // Check if account is verified
    if ($user['status_verifikasi'] == 'belum') {
        $_SESSION['error_messages'] = ["Akun Anda belum diverifikasi. Silakan hubungi customer service untuk verifikasi."];
        $_SESSION['form_data'] = ['email' => $email];
        header("Location: login.php");
        exit;
    }

    // Check if account is blocked
    if ($user['is_blocked'] == 1) {
        $_SESSION['error_messages'] = ["Akun Anda telah diblokir. Silakan hubungi customer service."];
        $_SESSION['form_data'] = ['email' => $email];
        header("Location: login.php");
        exit;
    }

    // Login successful
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['user_name'] = $user['nama'];
    $_SESSION['user_email'] = $user['email'];
    $_SESSION['user_role'] = 'nasabah';

    // Set remember me cookie if requested
    if ($remember) {
        $token = bin2hex(random_bytes(32));
        $expires = time() + (30 * 24 * 60 * 60); // 30 days

        // Store token in database - we'll use updated_at field to track when the token was created
        $stmt = $conn->prepare("UPDATE nasabah SET updated_at = NOW() WHERE id = ?");
        $stmt->bind_param("i", $user['id']);
        $stmt->execute();

        // Set cookie
        setcookie('remember_token', $token, $expires, '/');
        setcookie('user_id', $user['id'], $expires, '/');
    }

    // Redirect to dashboard
    $_SESSION['success_message'] = "Login berhasil! Selamat datang, " . $user['nama'] . ".";
    header("Location: dashboard.php");
    exit;
} else {
    // If not POST request, redirect to login page
    header("Location: login.php");
    exit;
}

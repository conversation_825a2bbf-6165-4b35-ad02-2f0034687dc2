<?php
// Staff change counter page
$page_title = 'Ganti Loket';
$active_menu = 'loket';
require_once '../includes/functions.php';

// Check if user is logged in and is staff
if (!is_logged_in() || !is_staff()) {
    redirect('../login.php');
}

// Get user ID
$user_id = $_SESSION['user_id'];

// Get available counters
$sql = "SELECT * FROM loket WHERE status = 'aktif' AND (user_id IS NULL OR user_id = $user_id) ORDER BY id ASC";
$result = query($sql);
$available_counters = fetch_all($result);

// Process counter selection
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['select_counter'])) {
    $counter_id = (int)$_POST['counter_id'];

    // Validate counter
    $sql = "SELECT * FROM loket WHERE id = $counter_id AND status = 'aktif'";
    $result = query($sql);

    if (num_rows($result) == 1) {
        // Check if there's any active queue for current counter
        $sql = "SELECT l.id FROM loket l
                JOIN antrian a ON l.id = a.loket_id
                WHERE l.user_id = $user_id
                AND a.status = 'dipanggil'
                AND a.tanggal = CURDATE()";
        $result = query($sql);

        if (num_rows($result) > 0) {
            $_SESSION['error_message'] = 'Anda masih memiliki antrian yang sedang dilayani. Silahkan selesaikan terlebih dahulu.';
        } else {
            // Update counter assignment
            $sql = "UPDATE loket SET user_id = NULL WHERE user_id = $user_id";
            query($sql);

            $sql = "UPDATE loket SET user_id = $user_id WHERE id = $counter_id";
            query($sql);

            $_SESSION['success_message'] = 'Loket berhasil diganti.';
            redirect('index.php');
        }
    } else {
        $_SESSION['error_message'] = 'Loket tidak valid.';
    }
}

// Don't include header as we have our own layout
// include_once '../includes/header.php';
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ganti Loket - Sistem Antrian Bank BJB</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>

<div class="container-fluid p-0">
    <!-- Header with BJB Logo and User Info -->
    <div class="bjb-header">
        <div class="bjb-logo">
            <img src="../assets/img/logo.jpeg" alt="Bank BJB">
            <div class="bjb-title">
                <h1>bank bjb</h1>
                <div class="bjb-subtitle">
                    <p>Bank BJB Kantor</p>
                    <p>Cabang Khusus</p>
                    <p>Banten</p>
                </div>
                <p class="staff-name"><?php echo $_SESSION['nama_lengkap']; ?></p>
            </div>
        </div>
        <div class="bjb-info">
            <div class="date-time">
                <span class="date-icon"><i class="far fa-calendar-alt"></i> <?php echo tanggal_indonesia(); ?></span>
                <span class="time-icon"><i class="far fa-clock"></i> <span id="current-time">00:00:00</span></span>
            </div>
            <div class="user-info">
                <span class="user-name"><?php echo $_SESSION['nama_lengkap']; ?></span>
                <div class="dropdown">
                    <button class="btn dropdown-toggle" type="button" id="userDropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="fas fa-user-circle"></i>
                    </button>
                    <div class="dropdown-menu dropdown-menu-right" aria-labelledby="userDropdown">
                        <a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sidebar Navigation -->
    <div class="bjb-container">
        <div class="bjb-sidebar">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link active" href="index.php">
                        <i class="fas fa-door-open"></i> Loket Pelayanan
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="history.php">
                        <i class="fas fa-history"></i> Riwayat Antrian
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="../logout.php">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </li>
            </ul>
        </div>

        <!-- Main Content -->
        <div class="bjb-content">
            <div class="page-header">
                <h2>Ganti Loket</h2>
                <?php include_once '../includes/alerts.php'; ?>
            </div>

            <div class="content-body">
                <?php if(count($available_counters) > 0): ?>
                <form action="change_counter.php" method="post">
                    <div class="counter-selection">
                        <?php foreach($available_counters as $counter): ?>
                        <div class="counter-option <?php echo ($counter['user_id'] == $user_id) ? 'active' : ''; ?>">
                            <input type="radio" id="counter_<?php echo $counter['id']; ?>" name="counter_id" value="<?php echo $counter['id']; ?>" <?php echo ($counter['user_id'] == $user_id) ? 'checked' : ''; ?> required>
                            <label for="counter_<?php echo $counter['id']; ?>">
                                <?php
                                $icon_class = 'fa-door-open';

                                if (strpos(strtolower($counter['nama_loket']), 'teller') !== false) {
                                    $icon_class = 'fa-money-check-alt';
                                } elseif (strpos(strtolower($counter['nama_loket']), 'customer') !== false) {
                                    $icon_class = 'fa-headset';
                                } elseif (strpos(strtolower($counter['nama_loket']), 'kredit') !== false) {
                                    $icon_class = 'fa-credit-card';
                                }
                                ?>
                                <i class="fas <?php echo $icon_class; ?>"></i>
                                <span><?php echo $counter['nama_loket']; ?></span>
                            </label>
                        </div>
                        <?php endforeach; ?>
                    </div>

                    <div class="action-buttons">
                        <button type="submit" name="select_counter" class="btn btn-primary">
                            <i class="fas fa-check"></i> Pilih Loket
                        </button>
                    </div>
                </form>
                <?php else: ?>
                <div class="no-counter-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h4>Tidak ada loket yang tersedia</h4>
                    <p>Silahkan hubungi administrator untuk mengaktifkan loket.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
    /* Reset and Base Styles */
    body {
        margin: 0;
        padding: 0;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background-color: #f5f5f5;
        color: #333;
    }

    /* BJB Header Styles */
    .bjb-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 15px 20px;
        background-color: #fff;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    .bjb-logo {
        display: flex;
        align-items: flex-start;
    }

    .bjb-logo img {
        height: 60px;
        margin-right: 15px;
    }

    .bjb-title h1 {
        color: #1e6285;
        font-size: 28px;
        font-weight: bold;
        margin: 0;
        padding: 0;
    }

    .bjb-subtitle {
        margin-top: 5px;
    }

    .bjb-subtitle p {
        margin: 0;
        padding: 0;
        font-size: 14px;
        line-height: 1.3;
        color: #333;
    }

    .staff-name {
        font-size: 12px;
        color: #666;
        margin-top: 5px;
    }

    .bjb-info {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
    }

    .date-time {
        display: flex;
        gap: 15px;
        margin-bottom: 10px;
    }

    .date-icon, .time-icon {
        font-size: 14px;
        color: #666;
    }

    .user-info {
        display: flex;
        align-items: center;
    }

    .user-name {
        margin-right: 10px;
        font-size: 14px;
        font-weight: 500;
    }

    .dropdown-toggle {
        background: none;
        border: none;
        color: #1e6285;
        font-size: 20px;
    }

    .dropdown-toggle::after {
        display: none;
    }

    /* Container Layout */
    .bjb-container {
        display: flex;
        min-height: calc(100vh - 90px);
    }

    /* Sidebar Styles */
    .bjb-sidebar {
        width: 220px;
        background-color: #fff;
        box-shadow: 2px 0 5px rgba(0,0,0,0.05);
        padding: 20px 0;
    }

    .bjb-sidebar .nav-link {
        color: #555;
        padding: 12px 20px;
        display: flex;
        align-items: center;
        transition: all 0.3s;
    }

    .bjb-sidebar .nav-link i {
        margin-right: 10px;
        width: 20px;
        text-align: center;
    }

    .bjb-sidebar .nav-link.active {
        background-color: #e9f5ff;
        color: #1e6285;
        border-left: 3px solid #1e6285;
    }

    .bjb-sidebar .nav-link:hover {
        background-color: #f5f5f5;
    }

    /* Content Styles */
    .bjb-content {
        flex: 1;
        padding: 20px;
    }

    .page-header {
        margin-bottom: 20px;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
    }

    .page-header h2 {
        font-size: 24px;
        color: #1e6285;
        margin: 0 0 10px 0;
    }

    .content-body {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        padding: 20px;
    }

    /* Counter Selection Styles */
    .counter-selection {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin-bottom: 30px;
    }

    .counter-option {
        position: relative;
        width: 200px;
    }

    .counter-option input[type="radio"] {
        position: absolute;
        opacity: 0;
        width: 0;
        height: 0;
    }

    .counter-option label {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 20px;
        background-color: #f9f9f9;
        border: 1px solid #ddd;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s;
        height: 100px;
        width: 100%;
    }

    .counter-option label i {
        font-size: 24px;
        margin-bottom: 10px;
        color: #1e6285;
    }

    .counter-option label span {
        font-weight: 500;
    }

    .counter-option input[type="radio"]:checked + label {
        background-color: #e9f5ff;
        border-color: #1e6285;
        box-shadow: 0 0 0 2px #1e6285;
    }

    .counter-option.active label {
        background-color: #e9f5ff;
        border-color: #1e6285;
    }

    .counter-option.active::after {
        content: "Loket Aktif";
        position: absolute;
        top: -10px;
        right: -10px;
        background-color: #1e6285;
        color: white;
        font-size: 11px;
        padding: 3px 8px;
        border-radius: 10px;
    }

    /* Action Buttons */
    .action-buttons {
        display: flex;
        justify-content: flex-start;
    }

    .action-buttons .btn {
        padding: 8px 20px;
        font-weight: 500;
    }

    .btn-primary {
        background-color: #1e6285;
        border-color: #1e6285;
    }

    .btn-primary:hover {
        background-color: #174d6a;
        border-color: #174d6a;
    }

    /* No Counter Message */
    .no-counter-message {
        text-align: center;
        padding: 40px 20px;
    }

    .no-counter-message i {
        font-size: 48px;
        color: #f0ad4e;
        margin-bottom: 15px;
    }

    .no-counter-message h4 {
        font-size: 20px;
        margin-bottom: 10px;
    }

    .no-counter-message p {
        color: #777;
    }
</style>

<script>
    // Update current time
    function updateTime() {
        const now = new Date();
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        const seconds = String(now.getSeconds()).padStart(2, '0');
        document.getElementById('current-time').textContent = `${hours}:${minutes}:${seconds}`;
    }

    // Update time every second
    setInterval(updateTime, 1000);
    updateTime(); // Initial call
</script>

<?php
// Don't include footer as we have our own layout
// include_once '../includes/footer.php';
?>

<!-- jQuery and Bootstrap JS -->
<script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

</body>
</html>

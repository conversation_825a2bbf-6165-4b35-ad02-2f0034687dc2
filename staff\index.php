<?php
// Staff dashboard page
$page_title = 'Dashboard Petugas';
$active_menu = 'dashboard';
$is_staff = true;

// Include database configuration
require_once '../config/database.php';
// require_once '../includes/functions_mock.php'; // HAPUS atau KOMENTARI baris ini agar tidak terjadi duplikasi fungsi
require_once '../includes/functions.php';

// Check if user is logged in
if (!is_logged_in()) {
    redirect('../login.php');
}

// Check if user is staff
if (!is_staff()) {
    redirect('../index.php');
}

// Get user ID from session
$user_id = $_SESSION['user_id'];

// Check if user is assigned to a counter
$sql = "SELECT l.* FROM loket l WHERE l.user_id = $user_id";
$result = query($sql);

if (num_rows($result) == 0) {
    // User not assigned to any counter
    $counter = null;
    $has_counter = false;
} else {
    $counter = fetch_assoc($result);
    $has_counter = true;

    // Get current serving queue
    $sql = "SELECT a.*, n.nama, p.nama_poli
            FROM antrian a
            LEFT JOIN nasabah n ON a.nasabah_id = n.id
            LEFT JOIN poli p ON a.poli_id = p.id
            WHERE a.loket_id = {$counter['id']}
            AND a.status = 'dipanggil'
            AND a.tanggal = CURDATE()
            ORDER BY a.waktu_dipanggil DESC
            LIMIT 1";
    $result = query($sql);

    if (num_rows($result) > 0) {
        $current_queue = fetch_assoc($result);
    } else {
        $current_queue = null;
    }

    // Get waiting queues based on counter's jenis_layanan
    $jenis_layanan = $counter['jenis_layanan'];
    $sql = "SELECT a.*, n.nama, p.nama_poli
            FROM antrian a
            LEFT JOIN nasabah n ON a.nasabah_id = n.id
            LEFT JOIN poli p ON a.poli_id = p.id
            WHERE a.status = 'menunggu'
            AND a.tanggal = CURDATE()";

    // Filter by jenis_layanan - only show queues that match counter type
    if (!empty($jenis_layanan)) {
        $sql .= " AND a.jenis_layanan = '$jenis_layanan'";
    }

    $sql .= " ORDER BY a.id LIMIT 10";
    $result = query($sql);
    $waiting_queues = fetch_all($result);
}

// Get available counters for selection
$sql = "SELECT * FROM loket WHERE status = 'aktif' AND (user_id IS NULL OR user_id = $user_id) ORDER BY id";
$result = query($sql);
$available_counters = fetch_all($result);

// Process counter selection
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['select_counter'])) {
    $counter_id = (int)$_POST['counter_id'];

    // Validate counter
    $sql = "SELECT * FROM loket WHERE id = $counter_id AND status = 'aktif'";
    $result = query($sql);

    if (num_rows($result) == 1) {
        // Check if user_id exists in users table
        $sql_check_user = "SELECT id FROM users WHERE id = $user_id";
        $result_check_user = query($sql_check_user);

        if (num_rows($result_check_user) == 0) {
            $_SESSION['error_message'] = 'User ID tidak valid. Silakan login ulang.';
            redirect('../logout.php');
            exit;
        }

        // Update counter assignment
        $sql = "UPDATE loket SET user_id = NULL WHERE user_id = $user_id";
        query($sql);

        $sql = "UPDATE loket SET user_id = $user_id WHERE id = $counter_id";
        query($sql);

        $_SESSION['success_message'] = 'Loket berhasil dipilih.';
        redirect('index.php');
    } else {
        $_SESSION['error_message'] = 'Loket tidak valid.';
    }
}

// Include header
include_once 'includes/header.php';

// Add custom CSS for staff page
echo '<style>
    /* Custom styles for Loket Pelayanan page */
    .card {
        border-radius: 10px;
        border: none;
    }

    .card-header {
        border-radius: 10px 10px 0 0 !important;
        padding: 15px 20px;
    }

    .current-serving {
        background-color: #f8f9fa;
        border-radius: 10px;
    }

    .btn-primary {
        background-color: #007bff;
        border-color: #007bff;
    }

    .table th, .table td { vertical-align: middle; }
    .table td.text-truncate { max-width: 150px; }
</style>';


?>

<!-- Header with blue icon -->
<div class="container-fluid px-4 py-3">
    <div class="row mb-4">
        <div class="col-md-12">
            <h2 class="text-primary">
                <i class="fas fa-door-open bg-primary text-white p-2 rounded-circle mr-2"></i>
                Loket Pelayanan
            </h2>
        </div>
    </div>

<?php if(!$has_counter): ?>
<div class="row">
    <div class="col-md-6 mx-auto">
        <div class="card shadow-lg">
            <div class="card-header bg-gradient-primary text-white">
                <i class="fas fa-door-open mr-2"></i> Pilih Loket
            </div>
            <div class="card-body p-4">
                <?php if(count($available_counters) > 0): ?>
                <form action="index.php" method="post">
                    <div class="form-group">
                        <label for="counter_id" class="font-weight-bold">Pilih Loket yang Akan Digunakan</label>
                        <select class="form-control form-control-lg" id="counter_id" name="counter_id" required>
                            <option value="">-- Pilih Loket --</option>
                            <?php foreach($available_counters as $c): ?>
                            <option value="<?php echo $c['id']; ?>"><?php echo $c['nama_loket']; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group mt-4">
                        <button type="submit" name="select_counter" class="btn btn-primary btn-lg btn-block shadow">
                            <i class="fas fa-check mr-2"></i> Pilih Loket
                        </button>
                    </div>
                </form>
                <?php else: ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle mr-2"></i> Tidak ada loket yang tersedia. Silahkan hubungi administrator.
                </div>
                <?php endif; ?>

                <div class="text-center mt-4">
                    <a href="../logout.php" class="btn btn-danger btn-lg shadow">
                        <i class="fas fa-sign-out-alt mr-2"></i> Logout
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php else: ?>
<div class="row">
    <div class="col-lg-8 col-md-12">
        <div class="card mb-4 shadow">
            <div class="card-header bg-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <span class="h5 mb-0"><i class="fas fa-door-open mr-2"></i> Loket: <?php echo $counter['nama_loket']; ?></span>
                    <button type="button" class="btn btn-light btn-sm" data-toggle="modal" data-target="#changeCounterModal">
                        <i class="fas fa-exchange-alt mr-1"></i> Ganti Loket
                    </button>
                </div>
            </div>
            <div class="card-body p-4">
                <div class="current-serving p-3 bg-light rounded shadow-sm fade-in"
                     data-queue-id="<?php echo $current_queue ? $current_queue['id'] : ''; ?>"
                     data-counter-id="<?php echo $counter['id']; ?>"
                     data-counter-type="<?php echo $counter['jenis_layanan'] ?? 'loket'; ?>">
                    <h4 class="mb-4 text-primary d-flex align-items-center">
                        <i class="fas fa-user-clock bg-primary text-white p-2 rounded-circle mr-2"></i>
                        Sedang Melayani
                    </h4>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center mb-3 p-3 bg-white rounded shadow-sm">
                                <div class="text-muted mb-2">Nomor Antrian</div>
                                <div class="display-4 font-weight-bold text-primary number">
                                    <?php echo $current_queue ? $current_queue['nomor_antrian'] : '-'; ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center mb-3 p-3 bg-white rounded shadow-sm">
                                <div class="text-muted mb-2">Nama</div>
                                <div class="h3 customer">
                                    <?php echo $current_queue ? $current_queue['nama'] : '-'; ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center mb-3 p-3 bg-white rounded shadow-sm">
                                <div class="text-muted mb-2">Layanan</div>
                                <div class="h3 service">
                                    <?php
                                    if ($current_queue) {
                                        if (!empty($current_queue['jenis_layanan'])) {
                                            // Display service from booking
                                            $layanan = '';
                                            switch($current_queue['jenis_layanan']) {
                                                case 'teller':
                                                    $layanan = 'Teller';
                                                    break;
                                                case 'cs':
                                                    $layanan = 'Customer Service';
                                                    break;
                                                case 'kredit':
                                                    $layanan = 'Kredit';
                                                    break;
                                                default:
                                                    $layanan = $current_queue['nama_poli'];
                                            }
                                            echo $layanan;
                                        } else {
                                            // Fallback to poli name
                                            echo $current_queue['nama_poli'];
                                        }
                                    } else {
                                        echo '-';
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <button id="btnNextCustomer" class="btn btn-success btn-lg mr-3 shadow" data-counter="<?php echo $counter['id']; ?>">
                        <i class="fas fa-user-plus mr-2"></i> Panggil Antrian Berikutnya
                    </button>
                    <button id="btnCompleteService" class="btn btn-primary btn-lg mr-3 shadow" data-counter="<?php echo $counter['id']; ?>">
                        <i class="fas fa-check-circle mr-2"></i> Selesai Melayani
                    </button>
                    <button id="btnCallAgain" class="btn btn-warning btn-lg shadow">
                        <i class="fas fa-volume-up mr-2"></i> Panggil Ulang
                    </button>
                </div>
            </div>
        </div>

        <div class="card shadow">
            <div class="card-header bg-info text-white">
                <i class="fas fa-list mr-2"></i> Daftar Antrian Menunggu (FIFO - First In First Out)
            </div>
            <div class="card-body p-0">
                <div class="waiting-list">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover mb-0">
                            <thead>
                                <tr>
                                    <th class="text-center">No. Antrian</th>
                                    <th class="text-truncate" style="max-width:150px;">Nama</th>
                                    <th class="text-center">Layanan</th>
                                    <th class="text-center">Loket</th>
                                    <th class="text-center">Status</th>
                                    <th class="text-center">Waktu Booking</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if(count($waiting_queues) > 0): ?>
                                <?php foreach($waiting_queues as $queue): ?>
                                <tr>
                                    <td class="text-center font-weight-bold"><?php echo $queue['nomor_antrian']; ?></td>
                                    <td class="text-truncate" style="max-width:150px;"><?php echo htmlspecialchars($queue['nama']); ?></td>
                                    <td class="text-center">
                                        <?php
                                        if (!empty($queue['jenis_layanan'])) {
                                            switch($queue['jenis_layanan']) {
                                                case 'teller': echo 'Teller'; break;
                                                case 'cs': echo 'Customer Service'; break;
                                                case 'kredit': echo 'Kredit'; break;
                                                default: echo $queue['nama_poli'];
                                            }
                                        } else {
                                            echo $queue['nama_poli'];
                                        }
                                        ?>
                                    </td>
                                    <td class="text-center">-</td>
                                    <td class="text-center">
                                        <?php if ($queue['status'] == 'menunggu'): ?>
                                            <span class="badge badge-warning font-weight-bold">Menunggu</span>
                                        <?php elseif ($queue['status'] == 'dipanggil'): ?>
                                            <span class="badge badge-info font-weight-bold">Dipanggil</span>
                                        <?php elseif ($queue['status'] == 'selesai'): ?>
                                            <span class="badge badge-success font-weight-bold">Selesai</span>
                                        <?php elseif ($queue['status'] == 'batal'): ?>
                                            <span class="badge badge-danger font-weight-bold">Batal</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center">
                                        <?php echo date('H:i', strtotime($queue['waktu_booking'])); ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                                <?php else: ?>
                                <tr>
                                    <td colspan="6" class="text-center py-3">Tidak ada antrian yang menunggu.</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-12">
        <div class="card mb-4 shadow">
            <div class="card-header bg-success text-white">
                <i class="fas fa-history mr-2"></i> Riwayat Pelayanan Hari Ini
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-striped table-hover mb-0">
                        <thead>
                            <tr>
                                <th>No. Antrian</th>
                                <th>Waktu Selesai</th>
                                <th>Durasi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $sql = "SELECT a.*, n.nama,
                                    TIMESTAMPDIFF(MINUTE, a.waktu_dipanggil, a.waktu_selesai) as durasi
                                    FROM antrian a
                                    LEFT JOIN nasabah n ON a.nasabah_id = n.id
                                    WHERE a.loket_id = {$counter['id']}
                                    AND a.status = 'selesai'
                                    AND a.tanggal = CURDATE()
                                    ORDER BY a.waktu_selesai DESC
                                    LIMIT 10";
                            $result = query($sql);
                            $history = fetch_all($result);
                            ?>

                            <?php if(count($history) > 0): ?>
                            <?php foreach($history as $h): ?>
                            <tr>
                                <td><?php echo $h['nomor_antrian']; ?></td>
                                <td><?php echo date('H:i', strtotime($h['waktu_selesai'])); ?></td>
                                <td><?php echo $h['durasi']; ?> menit</td>
                            </tr>
                            <?php endforeach; ?>
                            <?php else: ?>
                            <tr>
                                <td colspan="3" class="text-center py-3">Belum ada riwayat pelayanan.</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="card shadow">
            <div class="card-header bg-warning text-dark">
                <i class="fas fa-info-circle mr-2"></i> Informasi
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="bg-light p-3 rounded-circle mr-3">
                        <i class="fas fa-user-tie text-primary fa-2x"></i>
                    </div>
                    <div>
                        <div class="text-muted small">Petugas</div>
                        <div class="font-weight-bold"><?php echo $_SESSION['nama_lengkap']; ?></div>
                    </div>
                </div>

                <div class="d-flex align-items-center mb-3">
                    <div class="bg-light p-3 rounded-circle mr-3">
                        <i class="fas fa-door-open text-success fa-2x"></i>
                    </div>
                    <div>
                        <div class="text-muted small">Loket</div>
                        <div class="font-weight-bold"><?php echo $counter['nama_loket']; ?></div>
                    </div>
                </div>

                <div class="d-flex align-items-center mb-3">
                    <div class="bg-light p-3 rounded-circle mr-3">
                        <i class="fas fa-calendar-day text-info fa-2x"></i>
                    </div>
                    <div>
                        <div class="text-muted small">Tanggal</div>
                        <div class="font-weight-bold"><?php echo tanggal_indonesia(); ?></div>
                    </div>
                </div>

                <div class="d-flex align-items-center mb-3">
                    <div class="bg-light p-3 rounded-circle mr-3">
                        <i class="fas fa-clock text-warning fa-2x"></i>
                    </div>
                    <div>
                        <div class="text-muted small">Waktu</div>
                        <div class="font-weight-bold" id="clock">00:00:00</div>
                    </div>
                </div>

                <hr>

                <div class="d-flex justify-content-between mt-4">
                    <a href="../display/index.php" target="_blank" class="btn btn-info btn-lg flex-grow-1 mr-2 shadow">
                        <i class="fas fa-desktop mr-2"></i> Display Antrian
                    </a>
                    <a href="../logout.php" class="btn btn-danger btn-lg flex-grow-1 shadow">
                        <i class="fas fa-sign-out-alt mr-2"></i> Logout
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>
</div> <!-- Close container-fluid -->

<?php
// Include footer
include_once 'includes/footer.php';
?>

<!-- Change Counter Modal -->
<div class="modal fade" id="changeCounterModal" tabindex="-1" role="dialog" aria-labelledby="changeCounterModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="changeCounterModalLabel"><i class="fas fa-exchange-alt mr-2"></i> Ganti Loket</h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <?php if(count($available_counters) > 0): ?>
                <div class="text-center mb-4">
                    <img src="../assets/img/logo.jpeg" alt="Bank BJB" class="img-fluid mb-3" style="max-height: 80px;">
                    <h5 class="text-muted">Silakan pilih loket yang akan Anda gunakan untuk melayani nasabah</h5>
                </div>

                <form id="changeCounterForm" action="ajax_change_counter.php" method="post">
                    <div class="counter-selection d-flex flex-wrap justify-content-center">
                        <?php foreach($available_counters as $c): ?>
                        <div class="counter-option m-2 position-relative" style="width: 200px;">
                            <input type="radio" id="modal_counter_<?php echo $c['id']; ?>" name="counter_id" value="<?php echo $c['id']; ?>" class="position-absolute invisible" <?php echo ($c['user_id'] == $user_id) ? 'checked' : ''; ?> required>
                            <label for="modal_counter_<?php echo $c['id']; ?>" class="d-flex flex-column align-items-center justify-content-center p-4 bg-light rounded border <?php echo ($c['user_id'] == $user_id) ? 'border-primary' : ''; ?>" style="height: 120px; cursor: pointer; transition: all 0.3s;">
                                <?php
                                $icon_class = 'fa-door-open';
                                $icon_color = 'text-primary';

                                switch($c['jenis_layanan']) {
                                    case 'teller':
                                        $icon_class = 'fa-money-check-alt';
                                        $icon_color = 'text-success';
                                        break;
                                    case 'cs':
                                        $icon_class = 'fa-headset';
                                        $icon_color = 'text-warning';
                                        break;
                                    case 'kredit':
                                        $icon_class = 'fa-credit-card';
                                        $icon_color = 'text-danger';
                                        break;
                                }
                                ?>
                                <i class="fas <?php echo $icon_class; ?> <?php echo $icon_color; ?> fa-2x mb-2"></i>
                                <span class="font-weight-bold"><?php echo $c['nama_loket']; ?></span>
                                <span class="badge badge-pill <?php echo $icon_color; ?> mt-2">
                                    <?php
                                    switch($c['jenis_layanan']) {
                                        case 'teller':
                                            echo 'Teller';
                                            break;
                                        case 'cs':
                                            echo 'Customer Service';
                                            break;
                                        case 'kredit':
                                            echo 'Kredit';
                                            break;
                                        default:
                                            echo 'Umum';
                                    }
                                    ?>
                                </span>
                            </label>
                            <?php if($c['user_id'] == $user_id): ?>
                            <div class="position-absolute" style="top: -10px; right: -10px; background-color: #007bff; color: white; font-size: 11px; padding: 3px 8px; border-radius: 10px;">
                                Loket Aktif
                            </div>
                            <?php endif; ?>
                        </div>
                        <?php endforeach; ?>
                    </div>

                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-primary btn-lg px-5">
                            <i class="fas fa-check mr-2"></i> Pilih Loket
                        </button>
                    </div>
                </form>
                <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-exclamation-triangle fa-4x text-warning mb-3"></i>
                    <h4>Tidak ada loket yang tersedia</h4>
                    <p class="text-muted">Silahkan hubungi administrator untuk mengaktifkan loket.</p>
                </div>
                <?php endif; ?>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times mr-2"></i> Tutup
                </button>
            </div>
        </div>
    </div>
</div>

<style>
    /* Counter Selection Styles */
    .counter-selection label:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .counter-selection input[type="radio"]:checked + label {
        background-color: #e9f5ff !important;
        border-color: #007bff !important;
        border-width: 2px !important;
        box-shadow: 0 0 0 2px #007bff;
    }
</style>

<!-- Voice System Script -->
<script src="../assets/sounds/voice-system.js"></script>

<!-- Add custom JavaScript for counter operations -->
<script>


$(document).ready(function() {

    // Function to play call sound
    function playCallSound(queueNumber) {
        // Get counter information
        var counterId = $('.current-serving').data('counter-id');
        var counterType = $('.current-serving').data('counter-type');

        if (!counterId) counterId = 1;
        if (!counterType) counterType = 'loket';

        // Initialize voice system
        window.voiceSystem.initAudio();

        // Announce queue number
        window.voiceSystem.announceQueueNumber(queueNumber, counterType, counterId);

        console.log('Announcing queue number: ' + queueNumber + ' to ' + counterType + ' ' + counterId);
    }
    // Next customer button
    $('#btnNextCustomer').on('click', function() {
        var counterId = $(this).data('counter');

        $.ajax({
            url: 'next_customer.php',
            type: 'POST',
            data: {counter_id: counterId},
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Update current serving
                    $('.current-serving .number').text(response.queue.nomor_antrian);
                    $('.current-serving .customer').text(response.queue.nama);
                    $('.current-serving .service').text(response.queue.nama_poli);
                    $('.current-serving').data('queue-id', response.queue.id);

                    // Play call sound
                    playCallSound(response.queue.nomor_antrian);

                    // Refresh waiting list
                    refreshWaitingList();
                } else {
                    alert(response.message);
                }
            }
        });
    });

    // Complete service button
    $('#btnCompleteService').on('click', function() {
        var counterId = $(this).data('counter');
        var queueId = $('.current-serving').data('queue-id');

        if (!queueId) {
            alert('Tidak ada antrian yang sedang dilayani.');
            return;
        }

        $.ajax({
            url: 'complete_service.php',
            type: 'POST',
            data: {
                counter_id: counterId,
                queue_id: queueId
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Clear current serving
                    $('.current-serving .number').text('-');
                    $('.current-serving .customer').text('-');
                    $('.current-serving .service').text('-');
                    $('.current-serving').data('queue-id', '');

                    // Refresh waiting list
                    refreshWaitingList();
                } else {
                    alert(response.message);
                }
            }
        });
    });

    // Call again button
    $('#btnCallAgain').on('click', function() {
        var queueId = $('.current-serving').data('queue-id');

        if (!queueId) {
            alert('Tidak ada antrian yang sedang dilayani.');
            return;
        }

        // Get queue number
        var queueNumber = $('.current-serving .number').text();

        // Play call sound
        playCallSound(queueNumber);
    });

    // Direct call button
    $('.btn-call-direct').on('click', function() {
        var counterId = $(this).data('counter');
        var queueId = $(this).data('queue');

        $.ajax({
            url: 'call_direct.php',
            type: 'POST',
            data: {
                counter_id: counterId,
                queue_id: queueId
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Update current serving
                    $('.current-serving .number').text(response.queue.nomor_antrian);
                    $('.current-serving .customer').text(response.queue.nama);
                    $('.current-serving .service').text(response.queue.nama_poli);
                    $('.current-serving').data('queue-id', response.queue.id);

                    // Play call sound
                    playCallSound(response.queue.nomor_antrian);

                    // Refresh waiting list
                    refreshWaitingList();
                } else {
                    alert(response.message);
                }
            }
        });
    });

    // Function to refresh waiting list
    function refreshWaitingList() {
        $.ajax({
            url: 'waiting_list.php',
            type: 'GET',
            success: function(html) {
                $('.waiting-list').html(html);

                // Reattach event handlers
                $('.btn-call-direct').on('click', function() {
                    var counterId = $(this).data('counter');
                    var queueId = $(this).data('queue');

                    $.ajax({
                        url: 'call_direct.php',
                        type: 'POST',
                        data: {
                            counter_id: counterId,
                            queue_id: queueId
                        },
                        dataType: 'json',
                        success: function(response) {
                            if (response.success) {
                                // Update current serving
                                $('.current-serving .number').text(response.queue.nomor_antrian);
                                $('.current-serving .customer').text(response.queue.nama);
                                $('.current-serving .service').text(response.queue.nama_poli);
                                $('.current-serving').data('queue-id', response.queue.id);

                                // Play call sound
                                playCallSound(response.queue.nomor_antrian);

                                // Refresh waiting list
                                refreshWaitingList();
                            } else {
                                alert(response.message);
                            }
                        }
                    });
                });
            }
        });
    }

    // Update clock
    function updateClock() {
        var now = new Date();
        var hours = now.getHours();
        var minutes = now.getMinutes();
        var seconds = now.getSeconds();

        // Add leading zeros
        hours = (hours < 10) ? "0" + hours : hours;
        minutes = (minutes < 10) ? "0" + minutes : minutes;
        seconds = (seconds < 10) ? "0" + seconds : seconds;

        // Display the time
        $('#clock').text(hours + ":" + minutes + ":" + seconds);

        // Update every second
        setTimeout(updateClock, 1000);
    }

    updateClock();

    // Auto refresh waiting list every 30 seconds
    setInterval(refreshWaitingList, 30000);

    // Handle counter selection in modal
    $('#changeCounterForm').on('submit', function(e) {
        e.preventDefault();

        $.ajax({
            url: 'ajax_change_counter.php',
            type: 'POST',
            data: $(this).serialize(),
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Show success message
                    $('#changeCounterModal').modal('hide');

                    // Reload page after a short delay
                    setTimeout(function() {
                        location.reload();
                    }, 500);
                } else {
                    // Show error message
                    alert(response.message);
                }
            }
        });
    });
});
</script>

<div class="row mt-4">
    <div class="col-md-12">
        <?php include 'components/latest_queue.php'; ?>
    </div>
</div>

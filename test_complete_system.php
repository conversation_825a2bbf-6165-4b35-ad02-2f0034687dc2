<?php
// Complete system integration test
require_once 'includes/functions.php';

echo "<h1>Complete Queue Management System Test</h1>";

// Test 1: Create sample customers
echo "<h2>1. Creating Sample Customers</h2>";

$customers = [
    ['nama' => '<PERSON>', 'email' => '<EMAIL>', 'no_hp' => '081234567890'],
    ['nama' => 'Siti Nurhaliza', 'email' => '<EMAIL>', 'no_hp' => '081234567891'],
    ['nama' => 'Budi Santoso', 'email' => '<EMAIL>', 'no_hp' => '081234567892'],
    ['nama' => 'Dewi Lestari', 'email' => '<EMAIL>', 'no_hp' => '081234567893'],
    ['nama' => 'Rudi Hartono', 'email' => '<EMAIL>', 'no_hp' => '081234567894']
];

foreach ($customers as $index => $customer) {
    $customer_id = $index + 1;
    
    // Check if customer exists
    $sql = "SELECT COUNT(*) as count FROM nasabah WHERE id = $customer_id";
    $result = query($sql);
    $exists = fetch_assoc($result);
    
    if ($exists['count'] == 0) {
        // Insert customer
        $sql = "INSERT INTO nasabah (id, nama, email, no_hp, no_identitas, status, created_at) 
                VALUES ($customer_id, '{$customer['nama']}', '{$customer['email']}', '{$customer['no_hp']}', 
                        '123456789012345$customer_id', 'aktif', NOW())";
        
        if (query($sql)) {
            echo "<p>✅ Created customer: {$customer['nama']}</p>";
        } else {
            echo "<p>❌ Failed to create customer: {$customer['nama']}</p>";
        }
    } else {
        echo "<p>ℹ️ Customer already exists: {$customer['nama']}</p>";
    }
}

// Test 2: Create bookings for different services
echo "<h2>2. Creating Bookings for Different Services</h2>";

$services = ['teller', 'cs', 'kredit'];

foreach ($services as $index => $service) {
    $customer_id = $index + 1;
    $customer = $customers[$index];
    
    // Generate queue number
    $prefix = '';
    switch($service) {
        case 'teller': $prefix = 'T'; break;
        case 'cs': $prefix = 'C'; break;
        case 'kredit': $prefix = 'K'; break;
    }
    
    // Get next number for today
    $sql = "SELECT COUNT(*) as count FROM antrian WHERE jenis_layanan = '$service' AND tanggal = CURDATE()";
    $result = query($sql);
    $count = fetch_assoc($result);
    $next_number = $count['count'] + 1;
    $nomor_antrian = $prefix . str_pad($next_number, 3, '0', STR_PAD_LEFT);
    
    // Check if queue already exists for this customer today
    $sql = "SELECT COUNT(*) as count FROM antrian WHERE nasabah_id = $customer_id AND tanggal = CURDATE()";
    $result = query($sql);
    $existing = fetch_assoc($result);
    
    if ($existing['count'] == 0) {
        // Insert queue
        $sql = "INSERT INTO antrian (nomor_antrian, nama, jenis_layanan, tanggal, status, nasabah_id, waktu_booking, created_at) 
                VALUES ('$nomor_antrian', '{$customer['nama']}', '$service', CURDATE(), 'menunggu', $customer_id, NOW(), NOW())";
        
        if (query($sql)) {
            echo "<p>✅ Created queue: $nomor_antrian - {$customer['nama']} ($service)</p>";
        } else {
            echo "<p>❌ Failed to create queue for {$customer['nama']} ($service)</p>";
        }
    } else {
        echo "<p>ℹ️ Queue already exists for {$customer['nama']} today</p>";
    }
}

// Test 3: Show queues by service type (what staff will see)
echo "<h2>3. Queues by Service Type (Staff View)</h2>";

foreach ($services as $service) {
    echo "<h3>" . ucfirst($service) . " Queue:</h3>";
    
    $sql = "SELECT a.*, n.nama as customer_name FROM antrian a 
            LEFT JOIN nasabah n ON a.nasabah_id = n.id 
            WHERE a.jenis_layanan = '$service' AND a.tanggal = CURDATE() AND a.status = 'menunggu' 
            ORDER BY a.id";
    $result = query($sql);
    
    if ($result && num_rows($result) > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin-bottom: 20px;'>";
        echo "<tr style='background-color: #f0f0f0;'><th>Nomor Antrian</th><th>Nama</th><th>Status</th><th>Waktu Booking</th><th>Action</th></tr>";
        while ($row = fetch_assoc($result)) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . $row['nomor_antrian'] . "</td>";
            echo "<td style='padding: 8px;'>" . $row['customer_name'] . "</td>";
            echo "<td style='padding: 8px;'>" . $row['status'] . "</td>";
            echo "<td style='padding: 8px;'>" . date('H:i', strtotime($row['waktu_booking'])) . "</td>";
            echo "<td style='padding: 8px;'>";
            echo "<a href='test_call_queue.php?id={$row['id']}&service=$service' class='btn btn-sm btn-primary'>Call Next</a>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No queues found for $service</p>";
    }
}

// Test 4: Show counter assignments
echo "<h2>4. Counter Assignments</h2>";

$sql = "SELECT * FROM loket WHERE status = 'aktif' ORDER BY id";
$result = query($sql);

if ($result && num_rows($result) > 0) {
    echo "<table border='1' style='border-collapse: collapse; margin-bottom: 20px;'>";
    echo "<tr style='background-color: #f0f0f0;'><th>Loket</th><th>Jenis Layanan</th><th>Status</th><th>Current Queue</th></tr>";
    while ($row = fetch_assoc($result)) {
        // Get current queue for this counter
        $sql2 = "SELECT nomor_antrian FROM antrian WHERE loket_id = {$row['id']} AND status = 'dipanggil' AND tanggal = CURDATE() ORDER BY id DESC LIMIT 1";
        $result2 = query($sql2);
        $current_queue = '';
        if ($result2 && num_rows($result2) > 0) {
            $queue = fetch_assoc($result2);
            $current_queue = $queue['nomor_antrian'];
        }
        
        echo "<tr>";
        echo "<td style='padding: 8px;'>" . $row['nama_loket'] . "</td>";
        echo "<td style='padding: 8px;'>" . ucfirst($row['jenis_layanan']) . "</td>";
        echo "<td style='padding: 8px;'>" . $row['status'] . "</td>";
        echo "<td style='padding: 8px;'>" . ($current_queue ? $current_queue : 'None') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No counters found</p>";
}

// Test 5: Show all queues status
echo "<h2>5. All Queues Status Today</h2>";

$sql = "SELECT a.*, l.nama_loket, n.nama as customer_name FROM antrian a 
        LEFT JOIN loket l ON a.loket_id = l.id 
        LEFT JOIN nasabah n ON a.nasabah_id = n.id 
        WHERE a.tanggal = CURDATE() 
        ORDER BY a.jenis_layanan, a.id";
$result = query($sql);

if ($result && num_rows($result) > 0) {
    echo "<table border='1' style='border-collapse: collapse; margin-bottom: 20px;'>";
    echo "<tr style='background-color: #f0f0f0;'><th>Nomor Antrian</th><th>Nama</th><th>Layanan</th><th>Status</th><th>Loket</th><th>Waktu</th></tr>";
    while ($row = fetch_assoc($result)) {
        $status_color = '';
        switch($row['status']) {
            case 'menunggu': $status_color = 'background-color: #fff3cd;'; break;
            case 'dipanggil': $status_color = 'background-color: #d1ecf1;'; break;
            case 'selesai': $status_color = 'background-color: #d4edda;'; break;
        }
        
        echo "<tr style='$status_color'>";
        echo "<td style='padding: 8px;'>" . $row['nomor_antrian'] . "</td>";
        echo "<td style='padding: 8px;'>" . $row['customer_name'] . "</td>";
        echo "<td style='padding: 8px;'>" . ucfirst($row['jenis_layanan']) . "</td>";
        echo "<td style='padding: 8px;'>" . ucfirst($row['status']) . "</td>";
        echo "<td style='padding: 8px;'>" . ($row['nama_loket'] ? $row['nama_loket'] : '-') . "</td>";
        echo "<td style='padding: 8px;'>" . date('H:i', strtotime($row['waktu_booking'])) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No queues found for today</p>";
}

echo "<br><br>";
echo "<div style='background-color: #f8f9fa; padding: 20px; border-radius: 5px;'>";
echo "<h3>Test Links:</h3>";
echo "<a href='nasabah/simple_booking.php' style='display: inline-block; margin: 5px; padding: 10px 15px; background-color: #007bff; color: white; text-decoration: none; border-radius: 3px;'>Customer Booking</a> ";
echo "<a href='staff/simple_login.php' style='display: inline-block; margin: 5px; padding: 10px 15px; background-color: #28a745; color: white; text-decoration: none; border-radius: 3px;'>Staff Interface</a> ";
echo "<a href='admin/index.php' style='display: inline-block; margin: 5px; padding: 10px 15px; background-color: #17a2b8; color: white; text-decoration: none; border-radius: 3px;'>Admin Dashboard</a>";
echo "<br><br>";
echo "<a href='test_call_queue.php' style='display: inline-block; margin: 5px; padding: 10px 15px; background-color: #ffc107; color: black; text-decoration: none; border-radius: 3px;'>Test Call Queue</a> ";
echo "<a href='test_complete_service.php' style='display: inline-block; margin: 5px; padding: 10px 15px; background-color: #6c757d; color: white; text-decoration: none; border-radius: 3px;'>Test Complete Service</a>";
echo "</div>";
?>

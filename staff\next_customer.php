<?php
// Next customer API
header('Content-Type: application/json');
session_start();

// Include database configuration
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/queue_connector.php';

// Check if user is logged in and is staff
if (!is_logged_in() || !is_staff()) {
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized access.'
    ]);
    exit;
}

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method.'
    ]);
    exit;
}

// Get counter ID
$counter_id = isset($_POST['counter_id']) ? (int)$_POST['counter_id'] : 0;

// Validate counter
$user_id = $_SESSION['user_id'];
$sql = "SELECT * FROM loket WHERE id = $counter_id AND user_id = $user_id AND status = 'aktif'";
$result = query($sql);

if (num_rows($result) == 0) {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid counter.'
    ]);
    exit;
}

$counter = fetch_assoc($result);

// Check if there's already a customer being served
$sql = "SELECT * FROM antrian
        WHERE loket_id = $counter_id
        AND status = 'dipanggil'
        AND tanggal = CURDATE()";
$result = query($sql);

if (num_rows($result) > 0) {
    echo json_encode([
        'success' => false,
        'message' => 'Ada antrian yang sedang dilayani. Silahkan selesaikan terlebih dahulu.'
    ]);
    exit;
}

// Get counter's jenis_layanan
$jenis_layanan = $counter['jenis_layanan'];

// Panggil antrian berikutnya menggunakan fungsi dari queue_connector
$queue = call_next_queue($counter_id);

if (!$queue) {
    echo json_encode([
        'success' => false,
        'message' => 'Tidak ada antrian yang menunggu.'
    ]);
    exit;
}

$queue_id = $queue['id'];

// Return success response
echo json_encode([
    'success' => true,
    'message' => 'Antrian berhasil dipanggil.',
    'queue' => $queue
]);
?>

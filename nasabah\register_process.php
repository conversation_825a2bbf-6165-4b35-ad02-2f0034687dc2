<?php
// Registration process for nasabah
require_once '../includes/functions.php';
require_once '../includes/db.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if form is submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $nama = isset($_POST['nama']) ? trim($_POST['nama']) : '';
    $no_ktp = isset($_POST['no_ktp']) ? trim($_POST['no_ktp']) : '';
    $email = isset($_POST['email']) ? trim($_POST['email']) : '';
    $no_hp = isset($_POST['no_hp']) ? trim($_POST['no_hp']) : '';
    $password = isset($_POST['password']) ? $_POST['password'] : '';
    $confirm_password = isset($_POST['confirm_password']) ? $_POST['confirm_password'] : '';
    $alamat = isset($_POST['alamat']) ? trim($_POST['alamat']) : '';
    $terms = isset($_POST['terms']) ? true : false;

    // Validate required fields
    $errors = [];

    if (empty($nama)) {
        $errors[] = "Nama lengkap harus diisi";
    }

    if (empty($no_ktp)) {
        $errors[] = "Nomor KTP harus diisi";
    } elseif (strlen($no_ktp) !== 16 || !is_numeric($no_ktp)) {
        $errors[] = "Nomor KTP harus 16 digit angka";
    }

    if (empty($email)) {
        $errors[] = "Email harus diisi";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Format email tidak valid";
    }

    if (empty($no_hp)) {
        $errors[] = "Nomor HP harus diisi";
    } elseif (!preg_match('/^[0-9]{10,13}$/', $no_hp)) {
        $errors[] = "Nomor HP harus 10-13 digit angka";
    }

    if (empty($password)) {
        $errors[] = "Password harus diisi";
    } elseif (strlen($password) < 8) {
        $errors[] = "Password minimal 8 karakter";
    } elseif (!preg_match('/[A-Z]/', $password)) {
        $errors[] = "Password harus mengandung minimal 1 huruf besar";
    } elseif (!preg_match('/[a-z]/', $password)) {
        $errors[] = "Password harus mengandung minimal 1 huruf kecil";
    } elseif (!preg_match('/[0-9]/', $password)) {
        $errors[] = "Password harus mengandung minimal 1 angka";
    }

    if ($password !== $confirm_password) {
        $errors[] = "Konfirmasi password tidak sesuai";
    }

    if (empty($alamat)) {
        $errors[] = "Alamat harus diisi";
    }

    if (!$terms) {
        $errors[] = "Anda harus menyetujui syarat dan ketentuan";
    }

    // Check if email already exists
    $stmt = $conn->prepare("SELECT id FROM nasabah WHERE email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $errors[] = "Email sudah terdaftar. Silakan gunakan email lain atau login.";
    }

    // Check if KTP already exists
    $stmt = $conn->prepare("SELECT id FROM nasabah WHERE no_identitas = ?");
    $stmt->bind_param("s", $no_ktp);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $errors[] = "Nomor KTP sudah terdaftar. Silakan login atau hubungi customer service.";
    }

    // If there are errors, redirect back with error messages
    if (!empty($errors)) {
        $_SESSION['error_messages'] = $errors;
        $_SESSION['form_data'] = $_POST; // Save form data for repopulating the form
        header("Location: register.php");
        exit;
    }

    // Hash password
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);

    // Generate verification token
    $verification_token = bin2hex(random_bytes(32));

    // Insert user data into database
    $stmt = $conn->prepare("INSERT INTO nasabah (nama, no_identitas, jenis_identitas, email, no_hp, password, alamat, status_verifikasi, created_at) VALUES (?, ?, 'ktp', ?, ?, ?, ?, 'belum', NOW())");
    $stmt->bind_param("ssssss", $nama, $no_ktp, $email, $no_hp, $hashed_password, $alamat);

    if ($stmt->execute()) {
        // Success
        $_SESSION['success_message'] = "Registrasi berhasil! Silakan login untuk melanjutkan.";

        // In a real application, you would send a verification email here
        // For demo purposes, we'll just redirect to login page

        header("Location: login.php");
        exit;
    } else {
        // Error
        $_SESSION['error_messages'] = ["Gagal melakukan registrasi. Silakan coba lagi. Error: " . $stmt->error];
        $_SESSION['form_data'] = $_POST;
        header("Location: register.php");
        exit;
    }
} else {
    // If not POST request, redirect to register page
    header("Location: register.php");
    exit;
}

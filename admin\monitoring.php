<?php
// Display Monitoring page
$page_title = 'Monitoring Display';
$active_menu = 'monitoring';
$is_admin = true;
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!is_logged_in() || !is_admin()) {
    redirect('../login.php');
}

// Get active counters
$sql = "SELECT l.*, u.nama_lengkap
        FROM loket l
        LEFT JOIN users u ON l.user_id = u.id
        WHERE l.status = 'aktif'
        ORDER BY l.id";
$result = query($sql);
$counters = fetch_all($result);

// Get current queue for each counter
foreach ($counters as $key => $counter) {
    $sql = "SELECT a.*, n.nama, p.nama_poli
            FROM antrian a
            LEFT JOIN nasabah n ON a.nasabah_id = n.id
            LEFT JOIN poli p ON a.poli_id = p.id
            WHERE a.loket_id = {$counter['id']}
            AND a.status = 'dipanggil'
            AND a.tanggal = CURDATE()
            ORDER BY a.waktu_dipanggil DESC
            LIMIT 1";
    $result = query($sql);

    if (num_rows($result) > 0) {
        $counters[$key]['current_queue'] = fetch_assoc($result);
    } else {
        $counters[$key]['current_queue'] = null;
    }
}

// Get next queues
$sql = "SELECT a.*, n.nama, p.nama_poli
        FROM antrian a
        LEFT JOIN nasabah n ON a.nasabah_id = n.id
        LEFT JOIN poli p ON a.poli_id = p.id
        WHERE a.status = 'menunggu'
        AND a.tanggal = CURDATE()
        ORDER BY a.id
        LIMIT 10";
$result = query($sql);
$next_queues = fetch_all($result);

// Get service statistics
$sql = "SELECT p.id, p.nama_poli, COUNT(a.id) as total_antrian,
        SUM(CASE WHEN a.status = 'menunggu' THEN 1 ELSE 0 END) as menunggu,
        SUM(CASE WHEN a.status = 'dipanggil' THEN 1 ELSE 0 END) as dipanggil,
        SUM(CASE WHEN a.status = 'selesai' THEN 1 ELSE 0 END) as selesai
        FROM poli p
        LEFT JOIN antrian a ON p.id = a.poli_id AND a.tanggal = CURDATE()
        GROUP BY p.id
        ORDER BY p.id";
$result = query($sql);
$service_stats = fetch_all($result);

// Include header
include_once '../includes/header.php';
?>

<div class="row">
    <div class="col-md-12">
        <h1 class="mb-4">Monitoring Display Antrian</h1>
        
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-tv"></i> Tautan Display
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <a href="../display/dashboard.php" target="_blank" class="btn btn-primary btn-block">
                                    <i class="fas fa-desktop"></i> Dashboard Antrian
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="../display/index.php" target="_blank" class="btn btn-info btn-block">
                                    <i class="fas fa-desktop"></i> Display Utama
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="../display/teller.php" target="_blank" class="btn btn-success btn-block">
                                    <i class="fas fa-money-check-alt"></i> Display Teller
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="../display/customer_service.php" target="_blank" class="btn btn-warning btn-block">
                                    <i class="fas fa-headset"></i> Display CS
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="../display/kredit.php" target="_blank" class="btn btn-danger btn-block">
                                    <i class="fas fa-credit-card"></i> Display Kredit
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-door-open"></i> Status Loket
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Loket</th>
                                        <th>Petugas</th>
                                        <th>Layanan</th>
                                        <th>Antrian Saat Ini</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if(count($counters) > 0): ?>
                                    <?php foreach($counters as $counter): ?>
                                    <tr>
                                        <td><?php echo $counter['nama_loket']; ?></td>
                                        <td><?php echo $counter['nama_lengkap'] ?: 'Belum ada petugas'; ?></td>
                                        <td>
                                            <?php
                                            $sql = "SELECT p.nama_poli FROM poli p WHERE p.id = {$counter['poli_id']}";
                                            $result = query($sql);
                                            $poli = fetch_assoc($result);
                                            echo $poli ? $poli['nama_poli'] : 'Semua Layanan';
                                            ?>
                                        </td>
                                        <td>
                                            <?php if($counter['current_queue']): ?>
                                            <span class="badge badge-primary"><?php echo $counter['current_queue']['nomor_antrian']; ?></span>
                                            <small class="text-muted"><?php echo $counter['current_queue']['nama']; ?></small>
                                            <?php else: ?>
                                            <span class="text-muted">Tidak ada antrian</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($counter['user_id']): ?>
                                            <span class="badge badge-success">Aktif</span>
                                            <?php else: ?>
                                            <span class="badge badge-warning">Menunggu Petugas</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                    <?php else: ?>
                                    <tr>
                                        <td colspan="5" class="text-center">Tidak ada loket yang aktif.</td>
                                    </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-list-ol"></i> Antrian Berikutnya
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>No. Antrian</th>
                                        <th>Nama</th>
                                        <th>Layanan</th>
                                        <th>Waktu Ambil</th>
                                        <th>Estimasi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if(count($next_queues) > 0): ?>
                                    <?php foreach($next_queues as $queue): ?>
                                    <tr>
                                        <td><span class="badge badge-primary"><?php echo $queue['nomor_antrian']; ?></span></td>
                                        <td><?php echo $queue['nama']; ?></td>
                                        <td><?php echo $queue['nama_poli']; ?></td>
                                        <td><?php echo date('H:i', strtotime($queue['created_at'])); ?></td>
                                        <td>
                                            <?php
                                            // Calculate estimated time
                                            $waiting_count = 0;
                                            $sql = "SELECT COUNT(*) as count FROM antrian 
                                                    WHERE poli_id = {$queue['poli_id']} 
                                                    AND status = 'menunggu' 
                                                    AND id <= {$queue['id']}";
                                            $result = query($sql);
                                            $row = fetch_assoc($result);
                                            $waiting_count = $row['count'];
                                            
                                            // Assume 5 minutes per customer
                                            $estimated_minutes = $waiting_count * 5;
                                            echo $estimated_minutes . ' menit';
                                            ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                    <?php else: ?>
                                    <tr>
                                        <td colspan="5" class="text-center">Tidak ada antrian yang menunggu.</td>
                                    </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <i class="fas fa-chart-pie"></i> Statistik Layanan
                    </div>
                    <div class="card-body">
                        <?php if(count($service_stats) > 0): ?>
                        <?php foreach($service_stats as $stat): ?>
                        <div class="mb-4">
                            <h5><?php echo $stat['nama_poli']; ?></h5>
                            <div class="row text-center mb-2">
                                <div class="col-4">
                                    <div class="h4 mb-0"><?php echo $stat['total_antrian']; ?></div>
                                    <div class="small text-muted">Total</div>
                                </div>
                                <div class="col-4">
                                    <div class="h4 mb-0"><?php echo $stat['menunggu']; ?></div>
                                    <div class="small text-muted">Menunggu</div>
                                </div>
                                <div class="col-4">
                                    <div class="h4 mb-0"><?php echo $stat['selesai']; ?></div>
                                    <div class="small text-muted">Selesai</div>
                                </div>
                            </div>
                            <div class="progress" style="height: 10px;">
                                <?php
                                $total = $stat['total_antrian'] > 0 ? $stat['total_antrian'] : 1;
                                $waiting_percent = round(($stat['menunggu'] / $total) * 100);
                                $called_percent = round(($stat['dipanggil'] / $total) * 100);
                                $done_percent = round(($stat['selesai'] / $total) * 100);
                                ?>
                                <div class="progress-bar bg-warning" role="progressbar" style="width: <?php echo $waiting_percent; ?>%" title="Menunggu: <?php echo $stat['menunggu']; ?>"></div>
                                <div class="progress-bar bg-primary" role="progressbar" style="width: <?php echo $called_percent; ?>%" title="Dipanggil: <?php echo $stat['dipanggil']; ?>"></div>
                                <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $done_percent; ?>%" title="Selesai: <?php echo $stat['selesai']; ?>"></div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                        <?php else: ?>
                        <div class="text-center py-3">
                            <i class="fas fa-info-circle"></i> Tidak ada data statistik hari ini.
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <i class="fas fa-info-circle"></i> Informasi Sistem
                    </div>
                    <div class="card-body">
                        <ul class="list-group">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Total Antrian Hari Ini
                                <span class="badge badge-primary badge-pill">
                                    <?php
                                    $sql = "SELECT COUNT(*) as count FROM antrian WHERE tanggal = CURDATE()";
                                    $result = query($sql);
                                    $row = fetch_assoc($result);
                                    echo $row['count'];
                                    ?>
                                </span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Antrian Menunggu
                                <span class="badge badge-warning badge-pill">
                                    <?php
                                    $sql = "SELECT COUNT(*) as count FROM antrian WHERE tanggal = CURDATE() AND status = 'menunggu'";
                                    $result = query($sql);
                                    $row = fetch_assoc($result);
                                    echo $row['count'];
                                    ?>
                                </span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Antrian Selesai
                                <span class="badge badge-success badge-pill">
                                    <?php
                                    $sql = "SELECT COUNT(*) as count FROM antrian WHERE tanggal = CURDATE() AND status = 'selesai'";
                                    $result = query($sql);
                                    $row = fetch_assoc($result);
                                    echo $row['count'];
                                    ?>
                                </span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Loket Aktif
                                <span class="badge badge-info badge-pill">
                                    <?php
                                    $sql = "SELECT COUNT(*) as count FROM loket WHERE status = 'aktif'";
                                    $result = query($sql);
                                    $row = fetch_assoc($result);
                                    echo $row['count'];
                                    ?>
                                </span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Petugas Online
                                <span class="badge badge-success badge-pill">
                                    <?php
                                    $sql = "SELECT COUNT(DISTINCT user_id) as count FROM loket WHERE user_id IS NOT NULL";
                                    $result = query($sql);
                                    $row = fetch_assoc($result);
                                    echo $row['count'];
                                    ?>
                                </span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Auto refresh page every 30 seconds
    setTimeout(function() {
        location.reload();
    }, 30000);
});
</script>

<?php
// Include footer
include_once '../includes/footer.php';
?>

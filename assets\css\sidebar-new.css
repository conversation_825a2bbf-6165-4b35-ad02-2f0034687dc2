/* New Sidebar Styling for Bank BJB Queue System */
:root {
    --primary-color: #1a6a83; /* BJ<PERSON> Blue */
    --secondary-color: #0e4b5e; /* BJB Dark Blue */
    --accent-color: #d4b45a; /* BJB Gold */
    --dark-color: #0d3c4e; /* Darker Blue */
    --light-color: #f8f9fa; /* Light Gray */
    --white-color: #ffffff; /* White */
}

/* CSS Reset for Sidebar Elements */
#sidebar * {
    box-sizing: border-box !important;
}

#sidebar ul,
#sidebar li,
#sidebar a,
#sidebar i {
    margin: 0 !important;
    padding: 0 !important;
    list-style: none !important;
    text-decoration: none !important;
}

/* Main Sidebar Container */
#sidebar {
    min-width: 250px !important;
    max-width: 250px !important;
    background: linear-gradient(180deg, #1a6a83 0%, #0d3c4e 100%) !important;
    color: #fff !important;
    transition: all 0.3s !important;
    height: 100vh !important;
    position: fixed !important;
    z-index: 999 !important;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.2) !important;
    overflow-y: auto !important;
    padding: 0 !important;
}

/* Sidebar Header */
#sidebar .sidebar-header {
    padding: 20px;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    margin-bottom: 0;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Logo Container */
#sidebar .sidebar-header .logo-container {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    max-width: 200px;
    margin-left: auto;
    margin-right: auto;
}

/* Logo Image */
#sidebar .sidebar-header .logo-bjb {
    width: 100%;
    max-width: 180px;
    height: auto;
    object-fit: contain;
    display: block !important;
    margin: 0 auto;
}

/* Bank Name */
#sidebar .sidebar-header h4 {
    color: white;
    font-weight: bold;
    margin: 15px 0;
    font-size: 1.2rem;
    text-align: center;
    line-height: 1.4;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.5px;
}

/* Username Display */
#sidebar .sidebar-header p {
    color: white;
    margin: 10px auto;
    font-size: 0.9rem;
    background-color: rgba(0, 0, 0, 0.3);
    padding: 8px 15px;
    border-radius: 30px;
    display: inline-block;
    font-weight: 500;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Menu Items Container */
#sidebar ul.components {
    padding: 20px 0;
    margin-bottom: 0;
}

/* Menu Item */
#sidebar ul li {
    margin-bottom: 12px !important;
    padding: 0 15px !important;
    position: relative !important;
}

/* Menu Item Link */
#sidebar ul li a {
    padding: 12px 15px !important;
    font-size: 1rem !important;
    display: flex !important;
    align-items: center !important;
    color: rgba(255, 255, 255, 0.9) !important;
    text-decoration: none !important;
    transition: all 0.3s !important;
    border-radius: 10px !important;
    position: relative !important;
    overflow: hidden !important;
    border-left: 4px solid transparent !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    font-weight: 500 !important;
    letter-spacing: 0.3px !important;
}

/* Menu Item Icon */
#sidebar ul li a i {
    margin-right: 15px !important;
    width: 40px !important;
    height: 40px !important;
    line-height: 40px !important;
    text-align: center !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
    border-radius: 50% !important;
    transition: all 0.3s !important;
    font-size: 1.1rem !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
    border: 1px solid rgba(255, 255, 255, 0.05) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Icon Colors */
#sidebar ul li a i.fa-door-open {
    background-color: #ffc107 !important;
    color: #000 !important;
}

#sidebar ul li a i.fa-history {
    background-color: #17a2b8 !important;
    color: #fff !important;
}

#sidebar ul li a i.fa-sign-out-alt {
    background-color: #dc3545 !important;
    color: #fff !important;
}

/* Additional Icon Colors for Admin */
#sidebar ul li a i.fa-tachometer-alt {
    background-color: #007bff;
    color: #fff;
}

#sidebar ul li a i.fa-calendar-check {
    background-color: #28a745;
    color: #fff;
}

#sidebar ul li a i.fa-id-card {
    background-color: #6f42c1;
    color: #fff;
}

#sidebar ul li a i.fa-users {
    background-color: #fd7e14;
    color: #fff;
}

#sidebar ul li a i.fa-user-tie {
    background-color: #20c997;
    color: #fff;
}

#sidebar ul li a i.fa-chart-line {
    background-color: #e83e8c;
    color: #fff;
}

#sidebar ul li a i.fa-cog {
    background-color: #6c757d;
    color: #fff;
}

/* Active Menu Item */
#sidebar ul li.active > a {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    border-left: 4px solid var(--accent-color);
    transform: translateX(5px);
}

/* Active Menu Item Icon */
#sidebar ul li.active > a i {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    transform: scale(1.05);
}

/* Hover Effect */
#sidebar ul li a:hover {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

#sidebar ul li a:hover i {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Logout Item */
#sidebar ul li:last-child {
    margin-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    padding-top: 20px;
}

/* Add a subtle divider between menu items */
#sidebar ul li:not(:last-child) {
    position: relative;
}

#sidebar ul li:not(:last-child)::after {
    content: '';
    position: absolute;
    bottom: -6px;
    left: 15px;
    right: 15px;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.1), transparent);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    #sidebar {
        margin-left: -250px;
    }
    #sidebar.active {
        margin-left: 0;
    }
    #content {
        margin-left: 0;
        width: 100%;
    }
    #content.active {
        margin-left: 250px;
        width: 100%;
    }
}

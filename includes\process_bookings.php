<?php
/**
 * Process Bookings
 *
 * Script untuk memproses booking menjadi antrian
 * Dijalankan melalui cron job atau dapat dipanggil secara manual
 */

require_once 'functions.php';
require_once 'db.php';

// Fungsi untuk memproses booking menjadi antrian
function process_bookings() {
    global $conn;

    // Ambil semua booking dengan status confirmed yang belum diproses menjadi antrian
    $sql = "SELECT b.*
            FROM bookings b
            LEFT JOIN antrian a ON b.booking_id = a.booking_id
            WHERE b.status = 'confirmed'
            AND a.id IS NULL
            ORDER BY b.created_at ASC";

    $result = $conn->query($sql);

    if ($result->num_rows == 0) {
        return "Tidak ada booking yang perlu diproses.";
    }

    $processed = 0;
    $errors = [];

    while ($booking = $result->fetch_assoc()) {
        // Tentukan poli_id berdasarkan layanan
        $poli_id = 0;
        switch ($booking['layanan']) {
            case 'teller':
                $poli_id = 1; // ID poli untuk Teller
                break;
            case 'cs':
                $poli_id = 2; // ID poli untuk Customer Service
                break;
            case 'kredit':
                $poli_id = 3; // ID poli untuk Kredit
                break;
            default:
                $errors[] = "Layanan tidak valid untuk booking ID: " . $booking['booking_id'];
                continue 2;
        }

        // Buat entri di tabel antrian
        $stmt = $conn->prepare("INSERT INTO antrian (
            booking_id,
            nomor_antrian,
            nasabah_id,
            poli_id,
            status,
            tanggal,
            booking_dari
        ) VALUES (?, ?, ?, ?, 'menunggu', CURDATE(), 'online')");

        $stmt->bind_param(
            "ssii",
            $booking['booking_id'],
            $booking['nomor_antrian'],
            $booking['nasabah_id'],
            $poli_id
        );

        if ($stmt->execute()) {
            $processed++;
        } else {
            $errors[] = "Gagal memproses booking ID: " . $booking['booking_id'] . " - " . $stmt->error;
        }
    }

    $message = "Berhasil memproses $processed booking menjadi antrian.";
    if (!empty($errors)) {
        $message .= "\nError:\n" . implode("\n", $errors);
    }

    return $message;
}

// Jalankan fungsi jika file ini diakses langsung
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    echo process_bookings();
}
?>

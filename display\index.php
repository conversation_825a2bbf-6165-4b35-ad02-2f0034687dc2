<?php
// Display Antrian page
$page_title = 'Display Antrian';
$no_header = true;
require_once '../includes/functions.php';

// Get active counters with current queue
$sql = "SELECT l.*, u.nama_leng<PERSON>p,
               a.nomor_antrian, a.nama, p.nama_poli
        FROM loket l
        LEFT JOIN users u ON l.user_id = u.id
        LEFT JOIN antrian a ON l.id = a.loket_id AND a.status = 'dipanggil'
        LEFT JOIN poli p ON a.poli_id = p.id
        WHERE l.status = 'aktif'
        ORDER BY l.id";
$result = query($sql);
$counters = fetch_all($result);

// Get next queues
$sql_next = "SELECT a.nomor_antrian, a.nama, p.nama_poli
             FROM antrian a
             JOIN poli p ON a.poli_id = p.id
             WHERE a.status = 'menunggu'
             ORDER BY a.created_at
             LIMIT 5";
$result_next = query($sql_next);
$next_queues = fetch_all($result_next);
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Display Antrian Bank BJB</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #8e24aa 0%, #5e35b1 50%, #3949ab 100%);
            font-family: 'Roboto', sans-serif;
            color: white;
            overflow-x: hidden;
            min-height: 100vh;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
        }

        .logo-section {
            display: flex;
            align-items: center;
        }

        .logo-section img {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            margin-right: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .logo-section h1 {
            font-size: 28px;
            font-weight: 500;
            letter-spacing: 1px;
            margin: 0;
        }

        .time-section {
            text-align: right;
        }

        .time-section .time {
            font-size: 36px;
            font-weight: 700;
            color: #00e676;
            text-shadow: 0 0 20px rgba(0, 230, 118, 0.5);
            margin-bottom: 5px;
        }

        .time-section .date {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.8);
        }

        .main-content {
            padding: 30px;
            display: grid;
            grid-template-columns: 1fr 1fr 400px;
            gap: 30px;
            min-height: calc(100vh - 120px);
        }

        .counter-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            grid-column: span 2;
        }

        .counter-card {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .counter-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
        }

        .counter-header {
            padding: 15px 20px;
            text-align: center;
            font-size: 18px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .counter-header i {
            margin-right: 10px;
            font-size: 20px;
        }

        .loket1 { background: linear-gradient(135deg, #00bcd4 0%, #0097a7 100%); }
        .loket2 { background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%); }
        .loket3 { background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%); }
        .loket4 { background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%); }

        .counter-body {
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            padding: 30px 20px;
            text-align: center;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .queue-number {
            font-size: 72px;
            font-weight: 700;
            margin: 20px 0;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
            line-height: 1;
        }

        .customer-name {
            font-size: 18px;
            background: rgba(255, 255, 255, 0.2);
            padding: 10px 15px;
            border-radius: 8px;
            margin-top: 15px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .video-section {
            background: rgba(0, 0, 0, 0.4);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .video-section iframe {
            width: 100%;
            height: 250px;
            border: none;
        }

        .welcome-section {
            background: linear-gradient(135deg, #00bcd4 0%, #0097a7 100%);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .welcome-section i {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.9;
        }

        .welcome-section h3 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .welcome-section p {
            font-size: 16px;
            opacity: 0.9;
            margin: 0;
        }

        /* Responsive design */
        @media (max-width: 1400px) {
            .main-content {
                grid-template-columns: 1fr 350px;
            }
            .counter-grid {
                grid-column: span 1;
            }
        }

        @media (max-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            .counter-grid {
                grid-column: span 1;
            }
            .queue-number {
                font-size: 60px;
            }
        }

        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            .counter-grid {
                grid-template-columns: 1fr;
            }
            .queue-number {
                font-size: 48px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="logo-section">
            <img src="../assets/img/logo.jpeg" alt="Bank BJB">
            <h1>SISTEM ANTRIAN</h1>
        </div>
        <div class="time-section">
            <div class="time" id="clock">02:49:12</div>
            <div class="date">Minggu, 11 Mei 2025</div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Counter Grid -->
        <div class="counter-grid">
            <?php
            $counter_colors = ['loket1', 'loket2', 'loket3', 'loket4'];
            $counter_icons = ['fas fa-money-check-alt', 'fas fa-money-check-alt', 'fas fa-headset', 'fas fa-credit-card'];

            for($i = 0; $i < 4; $i++):
                $counter = isset($counters[$i]) ? $counters[$i] : null;
                $color_class = $counter_colors[$i];
                $icon_class = $counter_icons[$i];
            ?>
            <div class="counter-card">
                <div class="counter-header <?php echo $color_class; ?>">
                    <i class="<?php echo $icon_class; ?>"></i>
                    LOKET <?php echo $i + 1; ?>
                </div>
                <div class="counter-body">
                    <div class="queue-number" id="counter-<?php echo $i + 1; ?>">
                        <?php
                        if ($counter && $counter['nomor_antrian']) {
                            $nomor_antrian = $counter['nomor_antrian'];
                            // Extract number only
                            if (strpos($nomor_antrian, '-') !== false) {
                                $parts = explode('-', $nomor_antrian);
                                echo isset($parts[1]) ? $parts[1] : preg_replace('/[^0-9]/', '', $nomor_antrian);
                            } else {
                                echo preg_replace('/[^0-9]/', '', $nomor_antrian);
                            }
                        } else {
                            echo '-';
                        }
                        ?>
                    </div>
                    <div class="customer-name">
                        <?php
                        if ($counter && $counter['nama']) {
                            echo $counter['nama'];
                        } else {
                            echo '-';
                        }
                        ?>
                    </div>
                </div>
            </div>
            <?php endfor; ?>
        </div>

        <!-- Sidebar -->
        <div class="sidebar">
            <!-- Video Section -->
            <div class="video-section">
                <iframe src="https://www.youtube.com/embed/QkPv-tkRflI" allow="autoplay; encrypted-media" allowfullscreen></iframe>
            </div>

            <!-- Welcome Section -->
            <div class="welcome-section">
                <i class="fas fa-university"></i>
                <h3>Selamat Datang di Bank BJB</h3>
                <p>Terima kasih telah menggunakan layanan kami</p>
            </div>
        </div>
    </div>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        $(document).ready(function() {
            // Update clock
            function updateClock() {
                var now = new Date();
                var hours = now.getHours();
                var minutes = now.getMinutes();
                var seconds = now.getSeconds();

                // Add leading zeros
                hours = (hours < 10) ? "0" + hours : hours;
                minutes = (minutes < 10) ? "0" + minutes : minutes;
                seconds = (seconds < 10) ? "0" + seconds : seconds;

                var timeString = hours + ":" + minutes + ":" + seconds;
                $('#clock').text(timeString);

                // Update date
                var options = {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                };
                var dateString = now.toLocaleDateString('id-ID', options);
                $('.date').text(dateString);

                setTimeout(updateClock, 1000);
            }

            updateClock();

            // Auto refresh display
            function refreshDisplay() {
                $.ajax({
                    url: 'refresh_display.php',
                    type: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        // Update counters
                        if (data.counters) {
                            $.each(data.counters, function(index, counter) {
                                var counterElement = $('#counter-' + (index + 1));
                                var customerElement = counterElement.next('.customer-name');

                                // Update queue number with animation
                                if (counterElement.text() !== counter.number) {
                                    counterElement.fadeOut(200, function() {
                                        $(this).text(counter.number || '-').fadeIn(200);
                                    });
                                }

                                // Update customer name
                                if (customerElement.text() !== counter.customer) {
                                    customerElement.fadeOut(200, function() {
                                        $(this).text(counter.customer || '-').fadeIn(200);
                                    });
                                }
                            });
                        }

                        // Play notification sound if needed
                        if (data.play_sound) {
                            playNotificationSound();
                        }
                    },
                    complete: function() {
                        setTimeout(refreshDisplay, 3000);
                    }
                });
            }

            // Function to play notification sound
            function playNotificationSound() {
                var audio = new Audio('../assets/sounds/notification.mp3');
                audio.volume = 0.7;

                var playPromise = audio.play();
                if (playPromise !== undefined) {
                    playPromise.catch(error => {
                        console.log("Audio play was prevented by browser: " + error);
                    });
                }

                // Visual notification
                $('body').css('background', 'linear-gradient(135deg, #00bcd4 0%, #5e35b1 50%, #3949ab 100%)');
                setTimeout(function() {
                    $('body').css('background', 'linear-gradient(135deg, #8e24aa 0%, #5e35b1 50%, #3949ab 100%)');
                }, 1000);
            }

            // Start auto refresh
            setTimeout(refreshDisplay, 3000);

            // Refresh page every 2 hours to prevent memory leaks
            setTimeout(function() {
                location.reload();
            }, 7200000);
        });
    </script>
</body>
</html>

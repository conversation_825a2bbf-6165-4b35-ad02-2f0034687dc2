<?php
// Nasabah check status page
$page_title = 'Cek Status Antrian';
require_once '../includes/functions.php';
require_once '../includes/db.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cek Status Antrian - Bank BJB</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">

    <style>
        body {
            background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .header {
            background-color: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            padding: 15px 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 100;
        }

        .logo-container {
            display: flex;
            align-items: center;
        }

        .logo-container img {
            height: 50px;
            margin-right: 15px;
        }

        .logo-container h1 {
            color: white;
            font-size: 24px;
            margin: 0;
            font-weight: 600;
        }

        .nav-menu {
            display: flex;
            justify-content: flex-end;
        }

        .nav-menu a {
            color: white;
            margin-left: 20px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s;
            padding: 8px 15px;
            border-radius: 50px;
        }

        .nav-menu a:hover {
            background-color: rgba(255, 255, 255, 0.2);
            transform: translateY(-3px);
        }

        .nav-menu a.active {
            background-color: white;
            color: #4caf50;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .nav-menu a i {
            margin-right: 5px;
        }

        .page-title {
            text-align: center;
            color: white;
            padding: 40px 0;
        }

        .page-title h2 {
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .page-title p {
            font-size: 18px;
            opacity: 0.9;
            max-width: 700px;
            margin: 0 auto;
        }

        .status-container {
            background-color: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 50px;
        }

        .form-title {
            text-align: center;
            margin-bottom: 30px;
        }

        .form-title h3 {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .form-title p {
            color: #666;
        }

        .form-group label {
            font-weight: 600;
            color: #555;
        }

        .form-control {
            border-radius: 8px;
            padding: 12px 15px;
            border: 1px solid #ddd;
        }

        .form-control:focus {
            box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
            border-color: #4caf50;
        }

        .btn-check {
            background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
        }

        .btn-check:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .status-result {
            display: none;
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid #eee;
        }

        .status-card {
            background-color: #f9f9f9;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .status-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .status-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            margin-right: 20px;
            flex-shrink: 0;
        }

        .status-title h4 {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .status-title p {
            color: #666;
            margin: 0;
        }

        .status-details {
            background-color: white;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #eee;
        }

        .status-item {
            display: flex;
            margin-bottom: 10px;
        }

        .status-item:last-child {
            margin-bottom: 0;
        }

        .status-label {
            font-weight: 600;
            color: #555;
            width: 150px;
            flex-shrink: 0;
        }

        .status-value {
            color: #333;
        }

        .status-badge {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 14px;
        }

        .status-badge.confirmed {
            background-color: #e8f5e9;
            color: #388e3c;
        }

        .status-badge.pending {
            background-color: #fff8e1;
            color: #ffa000;
        }

        .status-badge.completed {
            background-color: #e3f2fd;
            color: #1976d2;
        }

        .status-badge.cancelled {
            background-color: #ffebee;
            color: #d32f2f;
        }

        .queue-number {
            font-size: 36px;
            font-weight: 700;
            color: #4caf50;
            text-align: center;
            margin: 20px 0;
        }

        .status-actions {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }

        .btn-action {
            margin: 0 10px;
            padding: 8px 20px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s;
        }

        .btn-action:hover {
            transform: translateY(-3px);
        }

        .btn-print {
            background-color: #e8f5e9;
            color: #388e3c;
            border: 1px solid #c8e6c9;
        }

        .btn-print:hover {
            background-color: #c8e6c9;
        }

        .btn-cancel {
            background-color: #ffebee;
            color: #d32f2f;
            border: 1px solid #ffcdd2;
        }

        .btn-cancel:hover {
            background-color: #ffcdd2;
        }

        footer {
            background-color: #333;
            color: white;
            padding: 40px 0;
            text-align: center;
        }

        .footer-logo {
            height: 60px;
            margin-bottom: 20px;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }

        .footer-links a {
            color: white;
            margin: 0 15px;
            text-decoration: none;
            transition: all 0.3s;
        }

        .footer-links a:hover {
            color: #4caf50;
        }

        .copyright {
            opacity: 0.7;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-4">
                    <div class="logo-container">
                        <img src="../assets/img/logo.jpeg" alt="Bank BJB">
                        <h1>Bank BJB</h1>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="nav-menu">
                        <a href="index.php"><i class="fas fa-home"></i> Beranda</a>
                        <a href="booking.php"><i class="fas fa-calendar-check"></i> Booking</a>
                        <a href="check_status.php" class="active"><i class="fas fa-search"></i> Cek Status</a>
                        <a href="register.php"><i class="fas fa-user-plus"></i> Registrasi</a>
                        <a href="login.php"><i class="fas fa-sign-in-alt"></i> Login</a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Page Title -->
    <div class="page-title">
        <div class="container">
            <h2>Cek Status Antrian</h2>
            <p>Lihat status booking antrian Anda di Bank BJB</p>
        </div>
    </div>

    <!-- Status Check Container -->
    <div class="container">
        <div class="status-container">
            <div class="form-title">
                <h3>Cek Status Booking</h3>
                <p>Masukkan nomor booking atau nomor identitas Anda</p>
            </div>

            <?php if (isset($_SESSION['error_messages']) && !empty($_SESSION['error_messages'])): ?>
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        <?php foreach ($_SESSION['error_messages'] as $error): ?>
                            <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php unset($_SESSION['error_messages']); ?>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-8 mx-auto">
                    <form id="status-form">
                        <div class="form-group">
                            <label for="search">Nomor Booking / Nomor Identitas</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="search" name="search" placeholder="Masukkan nomor booking atau nomor identitas" required>
                                <div class="input-group-append">
                                    <button type="submit" class="btn btn-check">Cek Status</button>
                                </div>
                            </div>
                        </div>
                        <div id="search-error" class="text-danger mt-2" style="display: none;"></div>
                    </form>
                </div>
            </div>

            <!-- Status Result (will be shown after form submission) -->
            <div class="status-result" id="status-result" style="display: none;">
                <div class="status-card">
                    <div class="status-header">
                        <div class="status-icon">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                        <div class="status-title">
                            <h4>Booking #<span id="booking-id"></span></h4>
                            <p>Dibuat pada: <span id="created-at"></span></p>
                        </div>
                    </div>

                    <div class="status-details">
                        <div class="status-item">
                            <div class="status-label">Status:</div>
                            <div class="status-value">
                                <span id="status-badge" class="status-badge"></span>
                            </div>
                        </div>

                        <div class="status-item">
                            <div class="status-label">Nama:</div>
                            <div class="status-value" id="nama"></div>
                        </div>

                        <div class="status-item">
                            <div class="status-label">Layanan:</div>
                            <div class="status-value" id="layanan"></div>
                        </div>

                        <div class="status-item">
                            <div class="status-label">Tanggal Kunjungan:</div>
                            <div class="status-value" id="tanggal"></div>
                        </div>

                        <div class="status-item">
                            <div class="status-label">Waktu Kunjungan:</div>
                            <div class="status-value" id="waktu"></div>
                        </div>
                    </div>

                    <div class="queue-number">
                        Nomor Antrian: <span id="nomor-antrian"></span>
                    </div>

                    <div class="status-actions">
                        <button class="btn btn-action btn-print" id="btn-print">
                            <i class="fas fa-print mr-2"></i> Cetak Tiket
                        </button>
                        <button class="btn btn-action btn-cancel" id="btn-cancel">
                            <i class="fas fa-times mr-2"></i> Batalkan Booking
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <img src="../assets/img/logo.jpeg" alt="Bank BJB" class="footer-logo">
            <div class="footer-links">
                <a href="#">Tentang Kami</a>
                <a href="#">Syarat & Ketentuan</a>
                <a href="#">Kebijakan Privasi</a>
                <a href="#">Hubungi Kami</a>
            </div>
            <p class="copyright">© 2023 Bank BJB. All Rights Reserved.</p>
        </div>
    </footer>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        $(document).ready(function() {
            // Handle form submission
            $("#status-form").submit(function(e) {
                e.preventDefault();

                var search = $("#search").val().trim();
                if (!search) {
                    $("#search-error").text("Silakan masukkan nomor booking atau nomor identitas.").show();
                    return;
                }

                // Clear previous error
                $("#search-error").hide();

                // Show loading
                $("#status-result").hide();
                $(".btn-check").prop("disabled", true).html('<i class="fas fa-spinner fa-spin mr-2"></i> Mencari...');

                // Make AJAX request to check status
                $.ajax({
                    url: "check_status_process.php",
                    type: "POST",
                    data: { search: search },
                    dataType: "json",
                    success: function(response) {
                        $(".btn-check").prop("disabled", false).html('Cek Status');

                        if (response.success) {
                            // Populate result with booking data
                            $("#booking-id").text(response.data.booking_id);
                            $("#created-at").text(response.data.created_at);
                            $("#status-badge").text(response.data.status).addClass(response.data.status_class);
                            $("#nama").text(response.data.nama);
                            $("#layanan").text(response.data.layanan);
                            $("#tanggal").text(response.data.tanggal);
                            $("#waktu").text(response.data.waktu);
                            $("#nomor-antrian").text(response.data.nomor_antrian);

                            // Show/hide cancel button based on booking status
                            if (response.data.can_cancel) {
                                $("#btn-cancel").show();
                            } else {
                                $("#btn-cancel").hide();
                            }

                            // Show result
                            $("#status-result").fadeIn();

                            // Scroll to result
                            $('html, body').animate({
                                scrollTop: $("#status-result").offset().top - 100
                            }, 500);
                        } else {
                            // Show error message
                            $("#search-error").text(response.message).show();
                        }
                    },
                    error: function() {
                        $(".btn-check").prop("disabled", false).html('Cek Status');
                        $("#search-error").text("Terjadi kesalahan. Silakan coba lagi.").show();
                    }
                });
            });

            // Handle print button
            $("#btn-print").click(function() {
                alert("Fitur cetak tiket akan segera tersedia.");
            });

            // Handle cancel button
            $("#btn-cancel").click(function() {
                if (confirm("Apakah Anda yakin ingin membatalkan booking ini?")) {
                    var booking_id = $("#booking-id").text();

                    // Make AJAX request to cancel booking
                    $.ajax({
                        url: "cancel_booking.php",
                        type: "POST",
                        data: { booking_id: booking_id },
                        dataType: "json",
                        success: function(response) {
                            if (response.success) {
                                alert("Booking berhasil dibatalkan.");
                                $("#status-result").fadeOut();
                                $("#search").val("");
                            } else {
                                alert(response.message || "Gagal membatalkan booking. Silakan coba lagi.");
                            }
                        },
                        error: function() {
                            alert("Terjadi kesalahan. Silakan coba lagi.");
                        }
                    });
                }
            });
        });
    </script>
</body>
</html>

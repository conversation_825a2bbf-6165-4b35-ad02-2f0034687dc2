<?php
// Debug display data
require_once 'includes/functions.php';

echo "<h1>Debug Display Data</h1>";

// Check current antrian data
echo "<h2>1. Current Antrian Data (Today)</h2>";
$sql = "SELECT * FROM antrian WHERE tanggal = CURDATE() ORDER BY id DESC";
$result = query($sql);
if ($result && num_rows($result) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Booking ID</th><th>Nomor Antrian</th><th>Nasabah ID</th><th>Poli ID</th><th><PERSON><PERSON></th><th>Loket ID</th><th>Status</th><th>Tanggal</th></tr>";
    while ($row = fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . ($row['booking_id'] ?? 'NULL') . "</td>";
        echo "<td>" . $row['nomor_antrian'] . "</td>";
        echo "<td>" . ($row['nasabah_id'] ?? 'NULL') . "</td>";
        echo "<td>" . ($row['poli_id'] ?? 'NULL') . "</td>";
        echo "<td>" . ($row['jenis_layanan'] ?? 'NULL') . "</td>";
        echo "<td>" . ($row['loket_id'] ?? 'NULL') . "</td>";
        echo "<td>" . $row['status'] . "</td>";
        echo "<td>" . $row['tanggal'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No antrian data found for today.</p>";
}

// Check nasabah data
echo "<h2>2. Nasabah Data</h2>";
$sql = "SELECT * FROM nasabah ORDER BY id DESC LIMIT 5";
$result = query($sql);
if ($result && num_rows($result) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Nama</th><th>No Identitas</th><th>Email</th><th>Status Verifikasi</th></tr>";
    while ($row = fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['nama'] . "</td>";
        echo "<td>" . $row['no_identitas'] . "</td>";
        echo "<td>" . $row['email'] . "</td>";
        echo "<td>" . $row['status_verifikasi'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No nasabah data found.</p>";
}

// Check bookings data
echo "<h2>3. Bookings Data</h2>";
$sql = "SELECT * FROM bookings ORDER BY id DESC LIMIT 5";
$result = query($sql);
if ($result && num_rows($result) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Booking ID</th><th>Nama</th><th>Email</th><th>Layanan</th><th>Nomor Antrian</th><th>Status</th></tr>";
    while ($row = fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['booking_id'] . "</td>";
        echo "<td>" . $row['nama'] . "</td>";
        echo "<td>" . $row['email'] . "</td>";
        echo "<td>" . $row['layanan'] . "</td>";
        echo "<td>" . $row['nomor_antrian'] . "</td>";
        echo "<td>" . $row['status'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No bookings data found.</p>";
}

// Test the display query
echo "<h2>4. Test Display Query</h2>";
$sql = "SELECT l.*, u.nama_lengkap,
               a.nomor_antrian, 
               COALESCE(n.nama, b.nama) as nama_customer,
               p.nama_poli
        FROM loket l
        LEFT JOIN users u ON l.user_id = u.id
        LEFT JOIN antrian a ON l.id = a.loket_id AND a.status = 'dipanggil' AND a.tanggal = CURDATE()
        LEFT JOIN poli p ON a.poli_id = p.id
        LEFT JOIN nasabah n ON a.nasabah_id = n.id
        LEFT JOIN bookings b ON a.booking_id = b.booking_id
        WHERE l.status = 'aktif'
        ORDER BY l.id";
$result = query($sql);
if ($result && num_rows($result) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Loket ID</th><th>Nama Loket</th><th>Jenis Layanan</th><th>Nomor Antrian</th><th>Nama Customer</th><th>Nama Poli</th></tr>";
    while ($row = fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['nama_loket'] . "</td>";
        echo "<td>" . ($row['jenis_layanan'] ?? 'NULL') . "</td>";
        echo "<td>" . ($row['nomor_antrian'] ?? 'NULL') . "</td>";
        echo "<td>" . ($row['nama_customer'] ?? 'NULL') . "</td>";
        echo "<td>" . ($row['nama_poli'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No display data found.</p>";
}

// Create sample data for testing
echo "<h2>5. Create Sample Data for Testing</h2>";
if (isset($_GET['create_sample'])) {
    // Insert sample antrian with status dipanggil
    $sql = "INSERT INTO antrian (nomor_antrian, tanggal, poli_id, jenis_layanan, loket_id, status, waktu_dipanggil, booking_dari) 
            VALUES ('T999', CURDATE(), 1, 'teller', 1, 'dipanggil', NOW(), 'lokal')";
    if (query($sql)) {
        echo "<p>Sample antrian created successfully!</p>";
        
        // Get the inserted ID
        $antrian_id = mysqli_insert_id($GLOBALS['conn']);
        
        // Create a sample nasabah
        $sql = "INSERT INTO nasabah (nama, no_identitas, email, password, no_hp, alamat, status_verifikasi) 
                VALUES ('Test Customer', '1234567890123456', '<EMAIL>', 'password', '081234567890', 'Test Address', 'sudah')
                ON DUPLICATE KEY UPDATE nama = 'Test Customer'";
        if (query($sql)) {
            $nasabah_id = mysqli_insert_id($GLOBALS['conn']);
            if ($nasabah_id == 0) {
                // Get existing nasabah ID
                $sql = "SELECT id FROM nasabah WHERE email = '<EMAIL>'";
                $result = query($sql);
                $nasabah = fetch_assoc($result);
                $nasabah_id = $nasabah['id'];
            }
            
            // Update antrian with nasabah_id
            $sql = "UPDATE antrian SET nasabah_id = $nasabah_id WHERE id = $antrian_id";
            query($sql);
            
            echo "<p>Sample nasabah linked successfully!</p>";
        }
    } else {
        echo "<p>Error creating sample data.</p>";
    }
} else {
    echo "<p><a href='?create_sample=1'>Click here to create sample data for testing</a></p>";
}
?>

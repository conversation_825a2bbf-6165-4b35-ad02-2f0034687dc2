<?php
/**
 * Confirm Bookings
 *
 * Halaman untuk mengkonfirmasi booking nasabah
 */

// Include required files
require_once '../includes/functions.php';
require_once '../includes/db.php';
require_once '../includes/queue_connector.php';

// Check if user is logged in and is admin
if (!is_logged_in() || !is_admin()) {
    redirect('../login.php');
}

// Set page title
$page_title = 'Konfirmasi Booking';

// Include header
include_once '../includes/header.php';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['confirm_booking'])) {
        $booking_id = $_POST['booking_id'];
        $status = $_POST['status'];

        // Update booking status
        $stmt = $conn->prepare("UPDATE bookings SET status = ?, updated_at = NOW() WHERE booking_id = ?");
        $stmt->bind_param("ss", $status, $booking_id);

        if ($stmt->execute()) {
            $_SESSION['success_message'] = "Status booking berhasil diperbarui.";
        } else {
            $_SESSION['error_message'] = "Gagal memperbarui status booking. Error: " . $stmt->error;
        }
    } elseif (isset($_POST['confirm_all'])) {
        // Confirm all pending bookings for today
        $stmt = $conn->prepare("UPDATE bookings SET status = 'confirmed', updated_at = NOW() WHERE status = 'pending' AND tanggal = CURDATE()");

        if ($stmt->execute()) {
            $_SESSION['success_message'] = "Semua booking hari ini berhasil dikonfirmasi.";
        } else {
            $_SESSION['error_message'] = "Gagal mengkonfirmasi semua booking. Error: " . $stmt->error;
        }
    }

    // Process bookings to create queue entries
    if (isset($_POST['process_bookings'])) {
        // Get all confirmed bookings that don't have antrian entries
        $sql = "SELECT b.*
                FROM bookings b
                LEFT JOIN antrian a ON b.booking_id = a.booking_id
                WHERE b.status = 'confirmed'
                AND a.id IS NULL
                ORDER BY b.created_at ASC";
        $result = $conn->query($sql);

        $processed = 0;
        $errors = [];

        while ($booking = $result->fetch_assoc()) {
            // Buat antrian dari booking
            $antrian_id = create_queue_from_booking(
                $booking['booking_id'],
                $booking['nomor_antrian'],
                $booking['tanggal'],
                $booking['nasabah_id'],
                $booking['layanan']
            );

            if ($antrian_id) {
                $processed++;
            } else {
                $errors[] = "Gagal memproses booking ID: " . $booking['booking_id'];
            }
        }

        if ($processed > 0) {
            $_SESSION['success_message'] = "Berhasil memproses $processed booking menjadi antrian.";
        } else if (count($errors) > 0) {
            $_SESSION['error_message'] = implode("<br>", $errors);
        } else {
            $_SESSION['info_message'] = "Tidak ada booking yang perlu diproses.";
        }
    }

    // Redirect to avoid form resubmission
    header("Location: confirm_bookings.php");
    exit;
}

// Get pending bookings
$sql = "SELECT b.*, n.nama, n.email, n.no_hp
        FROM bookings b
        JOIN nasabah n ON b.nasabah_id = n.id
        WHERE b.status = 'pending'
        AND b.tanggal >= CURDATE()
        ORDER BY b.tanggal ASC, b.created_at ASC";
$result = $conn->query($sql);
$pending_bookings = [];
while ($row = $result->fetch_assoc()) {
    $pending_bookings[] = $row;
}

// Get confirmed bookings for today
$sql = "SELECT b.*, n.nama, n.email, n.no_hp, a.id as antrian_id
        FROM bookings b
        JOIN nasabah n ON b.nasabah_id = n.id
        LEFT JOIN antrian a ON b.booking_id = a.booking_id
        WHERE b.status = 'confirmed'
        AND b.tanggal = CURDATE()
        ORDER BY b.created_at ASC";
$result = $conn->query($sql);
$confirmed_bookings = [];
while ($row = $result->fetch_assoc()) {
    $confirmed_bookings[] = $row;
}
?>

<div class="container-fluid px-4">
    <h1 class="mt-4 mb-4"><?php echo $page_title; ?></h1>

    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success">
            <?php
            echo $_SESSION['success_message'];
            unset($_SESSION['success_message']);
            ?>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-danger">
            <?php
            echo $_SESSION['error_message'];
            unset($_SESSION['error_message']);
            ?>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-12 mb-4">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-calendar-check mr-2"></i> Booking Hari Ini</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <form action="confirm_bookings.php" method="post" class="d-inline">
                            <button type="submit" name="confirm_all" class="btn btn-success mr-2">
                                <i class="fas fa-check-circle mr-1"></i> Konfirmasi Semua Booking Hari Ini
                            </button>
                        </form>

                        <form action="confirm_bookings.php" method="post" class="d-inline">
                            <button type="submit" name="process_bookings" class="btn btn-primary">
                                <i class="fas fa-sync mr-1"></i> Proses Booking Menjadi Antrian
                            </button>
                        </form>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID Booking</th>
                                    <th>Nomor Antrian</th>
                                    <th>Nama</th>
                                    <th>Layanan</th>
                                    <th>Tanggal</th>
                                    <th>Waktu</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (count($pending_bookings) > 0): ?>
                                    <?php foreach ($pending_bookings as $booking): ?>
                                        <?php if ($booking['tanggal'] == date('Y-m-d')): ?>
                                        <tr>
                                            <td><?php echo $booking['booking_id']; ?></td>
                                            <td><span class="badge badge-primary"><?php echo $booking['nomor_antrian']; ?></span></td>
                                            <td><?php echo $booking['nama']; ?></td>
                                            <td>
                                                <?php
                                                switch ($booking['layanan']) {
                                                    case 'teller':
                                                        echo 'Teller';
                                                        break;
                                                    case 'cs':
                                                        echo 'Customer Service';
                                                        break;
                                                    case 'kredit':
                                                        echo 'Kredit';
                                                        break;
                                                }
                                                ?>
                                            </td>
                                            <td><?php echo date('d/m/Y', strtotime($booking['tanggal'])); ?></td>
                                            <td><?php echo $booking['waktu']; ?></td>
                                            <td><span class="badge badge-warning">Menunggu Konfirmasi</span></td>
                                            <td>
                                                <form action="confirm_bookings.php" method="post">
                                                    <input type="hidden" name="booking_id" value="<?php echo $booking['booking_id']; ?>">
                                                    <input type="hidden" name="status" value="confirmed">
                                                    <button type="submit" name="confirm_booking" class="btn btn-sm btn-success">
                                                        <i class="fas fa-check mr-1"></i> Konfirmasi
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                <?php endif; ?>

                                <?php if (count($confirmed_bookings) > 0): ?>
                                    <?php foreach ($confirmed_bookings as $booking): ?>
                                        <tr>
                                            <td><?php echo $booking['booking_id']; ?></td>
                                            <td><span class="badge badge-primary"><?php echo $booking['nomor_antrian']; ?></span></td>
                                            <td><?php echo $booking['nama']; ?></td>
                                            <td>
                                                <?php
                                                switch ($booking['layanan']) {
                                                    case 'teller':
                                                        echo 'Teller';
                                                        break;
                                                    case 'cs':
                                                        echo 'Customer Service';
                                                        break;
                                                    case 'kredit':
                                                        echo 'Kredit';
                                                        break;
                                                }
                                                ?>
                                            </td>
                                            <td><?php echo date('d/m/Y', strtotime($booking['tanggal'])); ?></td>
                                            <td><?php echo $booking['waktu']; ?></td>
                                            <td>
                                                <span class="badge badge-success">Terkonfirmasi</span>
                                                <?php if ($booking['antrian_id']): ?>
                                                    <span class="badge badge-info ml-1">Dalam Antrian</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (!$booking['antrian_id']): ?>
                                                    <span class="text-muted">Menunggu diproses</span>
                                                <?php else: ?>
                                                    <span class="text-success">Sudah diproses</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>

                                <?php if (count($pending_bookings) == 0 && count($confirmed_bookings) == 0): ?>
                                    <tr>
                                        <td colspan="8" class="text-center py-3">Tidak ada booking untuk hari ini.</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>

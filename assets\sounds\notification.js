/**
 * Audio Notification System
 * 
 * This script provides a workaround for browsers that block autoplay of audio files.
 * It uses the Web Audio API to generate a notification sound.
 */

// Create AudioContext
let audioContext;

// Function to initialize AudioContext (must be called from a user gesture)
function initAudio() {
    if (!audioContext) {
        audioContext = new (window.AudioContext || window.webkitAudioContext)();
    }
    return audioContext;
}

// Function to play a beep sound
function playBeep() {
    try {
        const context = initAudio();
        
        // Create oscillator
        const oscillator = context.createOscillator();
        const gainNode = context.createGain();
        
        // Configure oscillator
        oscillator.type = 'sine';
        oscillator.frequency.setValueAtTime(880, context.currentTime); // A5
        
        // Configure gain (volume)
        gainNode.gain.setValueAtTime(0.5, context.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, context.currentTime + 1);
        
        // Connect nodes
        oscillator.connect(gainNode);
        gainNode.connect(context.destination);
        
        // Play sound
        oscillator.start(context.currentTime);
        oscillator.stop(context.currentTime + 1);
        
        console.log('Beep sound played successfully');
        return true;
    } catch (error) {
        console.error('Error playing beep sound:', error);
        return false;
    }
}

// Export functions
window.audioNotification = {
    initAudio: initAudio,
    playBeep: playBeep
};

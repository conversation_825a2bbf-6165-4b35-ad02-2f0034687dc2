<?php
/**
 * Latest Queue Page
 * 
 * This page displays the latest queue data from the database
 */

// Include database configuration
require_once 'config/database.php';
require_once 'includes/functions.php';

// Set page title
$page_title = 'Antrian Terbaru';
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Bank BJB</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/latest-queue.css">

    <style>
        body {
            background-color: #f8f9fa;
            padding: 20px 0;
        }
        
        .page-header {
            margin-bottom: 20px;
            padding: 15px 20px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        
        .page-header h1 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
            color: #1a6a83;
        }
        
        .page-header .breadcrumb {
            background: none;
            padding: 0;
            margin: 0;
        }
        
        .page-header .breadcrumb-item a {
            color: #1a6a83;
        }
        
        .page-header .breadcrumb-item.active {
            color: #6c757d;
        }
        
        .auto-refresh-info {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .refresh-button {
            background-color: #1a6a83;
            color: white;
            border: none;
            padding: 5px 15px;
            border-radius: 5px;
            font-size: 0.9rem;
            transition: all 0.3s;
        }
        
        .refresh-button:hover {
            background-color: #135e75;
            transform: translateY(-2px);
        }
        
        .refresh-button i {
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Page Header -->
        <div class="page-header d-flex justify-content-between align-items-center">
            <div>
                <h1><?php echo $page_title; ?></h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                        <li class="breadcrumb-item active" aria-current="page"><?php echo $page_title; ?></li>
                    </ol>
                </nav>
                <div class="auto-refresh-info">
                    <i class="fas fa-sync-alt fa-spin"></i> Data diperbarui secara otomatis setiap 30 detik
                </div>
            </div>
            <button id="refreshButton" class="refresh-button">
                <i class="fas fa-sync-alt"></i> Refresh
            </button>
        </div>
        
        <!-- Latest Queue Component -->
        <div id="latestQueueContainer">
            <?php include 'components/latest_queue.php'; ?>
        </div>
        
        <!-- Back Button -->
        <div class="text-center mt-4">
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left mr-2"></i> Kembali ke Halaman Utama
            </a>
        </div>
    </div>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Auto Refresh Script -->
    <script>
        $(document).ready(function() {
            // Function to refresh the latest queue data
            function refreshLatestQueue() {
                $.ajax({
                    url: 'components/latest_queue.php',
                    type: 'GET',
                    success: function(data) {
                        $('#latestQueueContainer').html(data);
                    }
                });
            }
            
            // Refresh data every 30 seconds
            setInterval(refreshLatestQueue, 30000);
            
            // Manual refresh button
            $('#refreshButton').on('click', function() {
                $(this).find('i').addClass('fa-spin');
                refreshLatestQueue();
                
                // Remove spin class after 1 second
                setTimeout(function() {
                    $('#refreshButton').find('i').removeClass('fa-spin');
                }, 1000);
            });
        });
    </script>
</body>
</html>

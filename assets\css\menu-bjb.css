/* Bank BJB Menu Styling - Consistent Menu Style */
:root {
    --primary-color: #1a6a83; /* BJB Blue */
    --secondary-color: #0e4b5e; /* BJB Dark Blue */
    --accent-color: #d4b45a; /* BJB Gold */
    --dark-color: #0d3c4e; /* Darker Blue */
    --light-color: #f8f9fa; /* Light Gray */
    --white-color: #ffffff; /* White */
}

/* Main Sidebar Container */
#sidebar {
    min-width: 250px !important;
    max-width: 250px !important;
    background: #0d3c4e !important;
    color: #fff !important;
    transition: all 0.3s !important;
    height: 100vh !important;
    position: fixed !important;
    z-index: 999 !important;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.2) !important;
    overflow-y: auto !important;
    padding: 0 !important;
}

/* Sidebar Header */
#sidebar .sidebar-header {
    padding: 20px;
    text-align: center;
    background-color: #0d3c4e;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    margin-bottom: 0;
}

/* Logo Container */
#sidebar .sidebar-header .logo-container {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    max-width: 200px;
    margin-left: auto;
    margin-right: auto;
}

/* Logo Image */
#sidebar .sidebar-header .logo-bjb {
    width: 100%;
    max-width: 180px;
    height: auto;
    object-fit: contain;
    display: block !important;
    margin: 0 auto;
}

/* Bank Name */
#sidebar .sidebar-header h4 {
    color: white;
    font-weight: bold;
    margin: 15px 0;
    font-size: 1.2rem;
    text-align: center;
    line-height: 1.4;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.5px;
}

/* Role Badge */
#sidebar .sidebar-header .badge {
    padding: 8px 15px;
    font-size: 0.9rem;
    border-radius: 30px;
    background-color: #0a2e3c;
    color: white;
    margin-top: 10px;
    display: inline-block;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Menu Items Container */
#sidebar ul.components {
    padding: 20px 0;
    margin-bottom: 0;
}

/* Menu Item */
#sidebar ul li {
    margin-bottom: 12px !important;
    padding: 0 15px !important;
    position: relative !important;
}

/* Menu Item Link */
#sidebar ul li a {
    padding: 12px 15px !important;
    font-size: 1rem !important;
    display: flex !important;
    align-items: center !important;
    color: rgba(255, 255, 255, 0.9) !important;
    text-decoration: none !important;
    transition: all 0.3s !important;
    border-radius: 10px !important;
    position: relative !important;
    overflow: hidden !important;
    border-left: 4px solid transparent !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    font-weight: 500 !important;
    letter-spacing: 0.3px !important;
}

/* Icon Circle */
.icon-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: white;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Icon Colors */
.bg-primary {
    background-color: #007bff !important;
}

.bg-warning {
    background-color: #ffc107 !important;
    color: #212529 !important;
}

.bg-info {
    background-color: #17a2b8 !important;
}

.bg-success {
    background-color: #28a745 !important;
}

.bg-danger {
    background-color: #dc3545 !important;
}

.bg-secondary {
    background-color: #6c757d !important;
}

/* Active Menu Item */
#sidebar ul li.active > a {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    border-left: 4px solid var(--accent-color);
    transform: translateX(5px);
}

/* Active Menu Item Icon */
#sidebar ul li.active > a .icon-circle {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    transform: scale(1.05);
}

/* Hover Effect */
#sidebar ul li a:hover {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

#sidebar ul li a:hover .icon-circle {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Logout Item */
#sidebar ul li:last-child {
    margin-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    padding-top: 20px;
}

/* Add a subtle divider between menu items */
#sidebar ul li:not(:last-child) {
    position: relative;
}

#sidebar ul li:not(:last-child)::after {
    content: '';
    position: absolute;
    bottom: -6px;
    left: 15px;
    right: 15px;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.1), transparent);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    #sidebar {
        margin-left: -250px;
    }
    #sidebar.active {
        margin-left: 0;
    }
    #content {
        margin-left: 0;
        width: 100%;
    }
    #content.active {
        margin-left: 250px;
        width: 100%;
    }
}

# Sistem Manajemen Antrian Bank BJB

Sistem Manajemen Antrian Nasabah menggunakan metode FIFO (First In First Out) berbasis SCRUM untuk Bank BJB Kantor Cabang Khusus Banten.

> **CATATAN PENTING**: Versi ini adalah versi mock yang berfungsi tanpa database untuk tujuan demonstrasi. Semua data yang ditampilkan adalah data contoh dan tidak disimpan secara permanen.

## Fitur

- Manajemen antrian dengan metode FIFO
- Booking antrian dari rumah dengan verifikasi KTP/Buku Tabungan
- Sistem blokir untuk booking palsu
- Estimasi waktu tunggu
- Multi-loket dan multi-layanan
- Dashboard admin untuk monitoring
- Laporan dan statistik

## Persyaratan Sistem

- PHP 7.2 atau lebih tinggi
- MySQL 5.7 atau lebih tinggi
- Web server (Apache/Nginx)

## Instalasi

1. Clone atau download repository ini
2. Buat database MySQL baru
3. Import file `database.sql` ke database yang telah dibuat
4. Konfigurasi koneksi database di `config/database.php`
5. Pastikan folder `uploads` dan subfolder-nya memiliki permission write (777)
6. Akses aplikasi melalui web browser

## Akun Default

### Admin
- Username: admin
- Password: admin123

### Petugas
- Username: petugas
- Password: petugas123

### Nasabah (Mock Version)
- Email: any email
- Password: any password (dalam versi mock, login akan berhasil dengan password apapun)

## Struktur Aplikasi

- `admin/` - Interface untuk administrator
- `staff/` - Interface untuk petugas loket
- `customer/` - Interface untuk nasabah
- `display/` - Interface untuk display antrian
- `config/` - File konfigurasi
- `includes/` - File fungsi dan komponen
- `assets/` - File statis (CSS, JS, gambar)
- `uploads/` - Folder untuk upload file

## Penggunaan

### Admin
1. Login dengan akun admin
2. Kelola loket, layanan, dan pengguna
3. Verifikasi identitas nasabah
4. Lihat laporan dan statistik

### Petugas Loket
1. Login dengan akun petugas
2. Pilih loket yang akan digunakan
3. Panggil antrian berikutnya
4. Selesaikan pelayanan

### Nasabah
1. Ambil nomor antrian di tempat atau booking dari rumah
2. Untuk booking dari rumah, upload foto identitas
3. Tunggu verifikasi (jika booking dari rumah)
4. Datang sesuai jadwal dan tunjukkan nomor antrian

## Pengembangan

Sistem ini dikembangkan menggunakan:
- PHP Native
- MySQL
- Bootstrap 4
- jQuery
- Font Awesome



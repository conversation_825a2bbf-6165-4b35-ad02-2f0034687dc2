/* Layout fixes for staff pages to prevent overlapping elements */

/* Basic layout fixes */
body {
    overflow-x: hidden;
    background-color: #f8f9fa;
    padding: 0;
    margin: 0;
}

/* Wrapper adjustments */
.wrapper {
    display: flex;
    width: 100%;
    align-items: stretch;
    position: relative;
}

/* Sidebar fixes */
#sidebar {
    min-width: 250px !important;
    max-width: 250px !important;
    height: 100vh !important;
    position: fixed !important;
    top: 0;
    left: 0;
    z-index: 999 !important;
    transition: all 0.3s;
    overflow-y: auto;
}

/* Content area adjustments */
#content {
    width: calc(100% - 250px);
    min-height: 100vh;
    transition: all 0.3s;
    position: relative;
    margin-left: 250px;
    padding: 0;
}

/* Navbar adjustments */
.navbar {
    padding: 15px 20px;
    width: 100%;
}

/* Card adjustments */
.card {
    margin-bottom: 20px;
    border: none;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Container adjustments */
.container-fluid {
    padding: 20px;
    width: 100%;
}

/* Fix for status cards */
.stats-card {
    height: 100%;
    margin-bottom: 15px;
}

/* Fix for tables */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #sidebar {
        margin-left: -250px;
    }
    
    #sidebar.active {
        margin-left: 0;
    }
    
    #content {
        width: 100%;
        margin-left: 0;
    }
    
    #content.active {
        margin-left: 250px;
        width: calc(100% - 250px);
    }
    
    .navbar-expand-lg .navbar-nav {
        flex-direction: row;
    }
}

/* Fix for header elements */
.page-title {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

/* Fix for dropdown menus */
.dropdown-menu {
    right: 0 !important;
    left: auto !important;
}

/* Fix for user dropdown */
.dropdown-toggle::after {
    vertical-align: middle;
}

/* Fix for date display */
.date-display {
    white-space: nowrap;
}

/* Fix for buttons */
.btn {
    white-space: nowrap;
}

/* Fix for icons in sidebar */
#sidebar ul li a i {
    min-width: 40px;
    text-align: center;
}

/* Fix for footer */
.footer {
    margin-top: 30px;
    padding: 15px 0;
    text-align: center;
    font-size: 0.9rem;
}

/* Fix for status cards */
.stats-card .card-body {
    padding: 15px;
}

/* Fix for filter card */
.filter-card {
    margin-bottom: 20px;
}

/* Fix for table headers */
.history-table th {
    white-space: nowrap;
}

/* Fix for long text in tables */
.table td {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Fix for status badges */
.badge {
    white-space: nowrap;
}

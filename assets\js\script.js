/**
 * Custom JavaScript for Bank Queue Management System
 */

$(document).ready(function() {
    // Sidebar is now permanently visible, no toggle needed
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();

    // Initialize popovers
    $('[data-toggle="popover"]').popover();

    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        $('.alert-dismissible').alert('close');
    }, 5000);

    // File input preview
    $('.custom-file-input').on('change', function() {
        var fileName = $(this).val().split('\\').pop();
        $(this).next('.custom-file-label').html(fileName);

        // Preview image if it's an image file
        if (this.files && this.files[0]) {
            var reader = new FileReader();
            reader.onload = function(e) {
                $('#imagePreview').attr('src', e.target.result).show();
            }
            reader.readAsDataURL(this.files[0]);
        }
    });

    // Confirm delete
    $('.btn-delete').on('click', function(e) {
        if (!confirm('A<PERSON>kah Anda yakin ingin menghapus data ini?')) {
            e.preventDefault();
        }
    });

    // Counter display clock
    function updateClock() {
        var now = new Date();
        var hours = now.getHours();
        var minutes = now.getMinutes();
        var seconds = now.getSeconds();

        // Add leading zeros
        hours = (hours < 10) ? "0" + hours : hours;
        minutes = (minutes < 10) ? "0" + minutes : minutes;
        seconds = (seconds < 10) ? "0" + seconds : seconds;

        // Display the time
        $('#clock').text(hours + ":" + minutes + ":" + seconds);

        // Update every second
        setTimeout(updateClock, 1000);
    }

    // Start the clock if it exists
    if ($('#clock').length) {
        updateClock();
    }

    // Auto refresh queue display
    function refreshQueueDisplay() {
        if ($('.queue-display').length) {
            $.ajax({
                url: 'refresh_display.php',
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    // Update counters
                    $.each(data.counters, function(index, counter) {
                        $('#counter-' + counter.id + ' .counter-number').text(counter.current_number);
                        $('#counter-' + counter.id + ' .counter-customer').text(counter.customer_name);
                    });
                },
                complete: function() {
                    // Refresh every 5 seconds
                    setTimeout(refreshQueueDisplay, 5000);
                }
            });
        }
    }

    // Start auto refresh if queue display exists
    if ($('.queue-display').length) {
        refreshQueueDisplay();
    }

    // Staff counter controls
    $('#btnNextCustomer').on('click', function() {
        var counterId = $(this).data('counter');

        $.ajax({
            url: 'next_customer.php',
            type: 'POST',
            data: {counter_id: counterId},
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Update current serving
                    $('.current-serving .number').text(response.queue.nomor_antrian);
                    $('.current-serving .customer').text(response.queue.nama_nasabah);

                    // Play notification sound
                    var audio = new Audio('../assets/sounds/notification.mp3');
                    audio.play();

                    // Refresh waiting list
                    refreshWaitingList();
                } else {
                    alert(response.message);
                }
            }
        });
    });

    $('#btnCompleteService').on('click', function() {
        var counterId = $(this).data('counter');
        var queueId = $('.current-serving').data('queue-id');

        if (!queueId) {
            alert('Tidak ada antrian yang sedang dilayani.');
            return;
        }

        $.ajax({
            url: 'complete_service.php',
            type: 'POST',
            data: {
                counter_id: counterId,
                queue_id: queueId
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Clear current serving
                    $('.current-serving .number').text('-');
                    $('.current-serving .customer').text('-');
                    $('.current-serving').data('queue-id', '');

                    // Refresh waiting list
                    refreshWaitingList();
                } else {
                    alert(response.message);
                }
            }
        });
    });

    $('#btnCallAgain').on('click', function() {
        var queueId = $('.current-serving').data('queue-id');

        if (!queueId) {
            alert('Tidak ada antrian yang sedang dilayani.');
            return;
        }

        // Play notification sound
        var audio = new Audio('../assets/sounds/notification.mp3');
        audio.play();
    });

    function refreshWaitingList() {
        var counterId = $('#btnNextCustomer').data('counter');

        $.ajax({
            url: 'waiting_list.php',
            type: 'GET',
            data: {counter_id: counterId},
            success: function(html) {
                $('.waiting-list').html(html);
            }
        });
    }

    // Refresh waiting list every 30 seconds
    if ($('.waiting-list').length) {
        setInterval(refreshWaitingList, 30000);
    }

    // Verification image preview
    $('.btn-preview-id').on('click', function() {
        var imageUrl = $(this).data('image');
        $('#idPreviewModal .modal-body img').attr('src', imageUrl);
        $('#idPreviewModal').modal('show');
    });

    // Verify customer
    $('.btn-verify').on('click', function() {
        var customerId = $(this).data('customer');
        var status = $(this).data('status');
        var reason = '';

        if (status === 'ditolak') {
            reason = prompt('Alasan penolakan:');
            if (reason === null) {
                return; // User cancelled
            }
        }

        $.ajax({
            url: 'verify_customer.php',
            type: 'POST',
            data: {
                customer_id: customerId,
                status: status,
                reason: reason
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.message);
                }
            }
        });
    });
});

<?php
// Refresh display API
header('Content-Type: application/json');
require_once '../includes/functions.php';

// Get active counters
$sql = "SELECT l.*, u.nama_lengkap
        FROM loket l
        LEFT JOIN users u ON l.user_id = u.id
        WHERE l.status = 'aktif'
        ORDER BY l.id";
$result = query($sql);
$counters = fetch_all($result);

$counter_data = [];
$play_sound = false;

// Get current queue for each counter
foreach ($counters as $counter) {
    $sql = "SELECT a.*,
            COALESCE(n.nama, b.nama, 'Customer') as nama_customer,
            p.nama_poli,
            TIMESTAMPDIFF(SECOND, a.waktu_dipanggil, NOW()) as seconds_since_call
            FROM antrian a
            LEFT JOIN nasabah n ON a.nasabah_id = n.id
            LEFT JOIN bookings b ON a.booking_id = b.booking_id
            LEFT JOIN poli p ON a.poli_id = p.id
            WHERE a.loket_id = {$counter['id']}
            AND a.status = 'dipanggil'
            AND a.tanggal = CURDATE()
            ORDER BY a.waktu_dipanggil DESC
            LIMIT 1";
    $result = query($sql);

    if (num_rows($result) > 0) {
        $queue = fetch_assoc($result);
        $nomor_antrian = $queue['nomor_antrian'];
        // Cek apakah nomor antrian mengandung tanda '-'
        if (strpos($nomor_antrian, '-') !== false) {
            $parts = explode('-', $nomor_antrian);
            $number = isset($parts[1]) ? $parts[1] : $nomor_antrian;
        } else {
            // Jika tidak ada tanda '-', tampilkan nomor antrian tanpa prefix
            $number = preg_replace('/[^0-9]/', '', $nomor_antrian);
        }
        $customer = $queue['nama_customer'];
        $service = $queue['nama_poli'];

        // Check if this is a recent call (less than 15 seconds ago)
        if ($queue['seconds_since_call'] < 15) {
            $play_sound = true;
        }
    } else {
        $number = '-';
        $customer = '-';
        $service = '-';
    }

    $counter_data[] = [
        'id' => $counter['id'],
        'name' => $counter['nama_loket'],
        'number' => $number,
        'customer' => $customer,
        'service' => $service
    ];
}

// Get next queues
$sql = "SELECT a.*,
        COALESCE(n.nama, b.nama, 'Customer') as nama_customer,
        p.nama_poli
        FROM antrian a
        LEFT JOIN nasabah n ON a.nasabah_id = n.id
        LEFT JOIN bookings b ON a.booking_id = b.booking_id
        LEFT JOIN poli p ON a.poli_id = p.id
        WHERE a.status = 'menunggu'
        AND a.tanggal = CURDATE()
        ORDER BY a.id
        LIMIT 5";
$result = query($sql);
$next_queues = fetch_all($result);

$queue_data = [];
foreach ($next_queues as $queue) {
    $queue_data[] = [
        'id' => $queue['id'],
        'number' => (function($nomor_antrian) {
            // Cek apakah nomor antrian mengandung tanda '-'
            if (strpos($nomor_antrian, '-') !== false) {
                $parts = explode('-', $nomor_antrian);
                return isset($parts[1]) ? $parts[1] : $nomor_antrian;
            } else {
                // Jika tidak ada tanda '-', tampilkan nomor antrian tanpa prefix
                return preg_replace('/[^0-9]/', '', $nomor_antrian);
            }
        })($queue['nomor_antrian']),
        'name' => $queue['nama_customer'],
        'service' => $queue['nama_poli']
    ];
}

// Return JSON response with all active counters
echo json_encode([
    'counters' => $counter_data,
    'next_queues' => $queue_data,
    'play_sound' => $play_sound,
    'timestamp' => date('Y-m-d H:i:s')
]);
?>

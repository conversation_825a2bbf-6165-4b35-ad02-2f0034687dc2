<?php
// Simple booking form for testing
require_once '../includes/functions.php';
require_once '../includes/db.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check for session - need either nasabah_id or user_id
if (!isset($_SESSION['nasabah_id']) && !isset($_SESSION['user_id'])) {
    echo '<div class="alert alert-danger">Please login first by visiting <a href="simple_login.php">Simple Login</a></div>';
    exit;
}

// Ensure both variables exist for compatibility
if (isset($_SESSION['nasabah_id']) && !isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = $_SESSION['nasabah_id'];
    $_SESSION['user_role'] = 'nasabah';
}

if (isset($_SESSION['user_id']) && !isset($_SESSION['nasabah_id'])) {
    $_SESSION['nasabah_id'] = $_SESSION['user_id'];
}

$success = false;
$error_message = '';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Collect form data
    $layanan = $_POST['layanan'] ?? '';
    $tanggal = $_POST['tanggal'] ?? '';
    $waktu = $_POST['waktu'] ?? '';

    // Basic validation
    if (empty($layanan) || empty($tanggal) || empty($waktu)) {
        $error_message = 'All fields are required';
    } else {
        try {
            // Generate booking ID
            $booking_id = 'BK' . date('Ymd') . rand(1000, 9999);
            
            // Generate queue number
            $prefix = '';
            switch ($layanan) {
                case 'teller':
                    $prefix = 'T';
                    break;
                case 'cs':
                    $prefix = 'C';
                    break;
                case 'kredit':
                    $prefix = 'K';
                    break;
            }
            
            $queue_number = $prefix . str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT);
            
            // Get user data
            $nasabah_id = $_SESSION['user_id'];
            $nama = $_SESSION['nasabah_nama'] ?? 'Test User';
            $email = $_SESSION['nasabah_email'] ?? '<EMAIL>';
            $no_identitas = '1234567890123456';
            $no_hp = '081234567890';
            $keterangan = 'Test booking from simple form';
            $file_identitas = 'test_file.jpg';
            
            // Insert into bookings table
            $stmt = $conn->prepare("INSERT INTO bookings (booking_id, nasabah_id, nama, no_identitas, email, no_hp, layanan, tanggal, waktu, keterangan, file_identitas, nomor_antrian, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', NOW())");
            $stmt->bind_param("sissssssssss", $booking_id, $nasabah_id, $nama, $no_identitas, $email, $no_hp, $layanan, $tanggal, $waktu, $keterangan, $file_identitas, $queue_number);
            
            if ($stmt->execute()) {
                // Create queue entry
                require_once '../includes/queue_connector.php';
                $queue_id = create_queue_from_booking($booking_id, $queue_number, $tanggal, $nasabah_id, $layanan);
                
                if ($queue_id) {
                    $success = true;
                    $_SESSION['booking_id'] = $booking_id;
                    $_SESSION['nomor_antrian'] = $queue_number;
                } else {
                    $error_message = 'Failed to create queue entry';
                }
            } else {
                $error_message = 'Failed to create booking: ' . $stmt->error;
            }
        } catch (Exception $e) {
            $error_message = 'Error: ' . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Booking</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        Simple Booking Form
                    </div>
                    <div class="card-body">
                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <h4>Booking Successful!</h4>
                                <p>Booking ID: <strong><?php echo $_SESSION['booking_id']; ?></strong></p>
                                <p>Queue Number: <strong><?php echo $_SESSION['nomor_antrian']; ?></strong></p>
                                <hr>
                                <a href="simple_booking.php" class="btn btn-primary">Make Another Booking</a>
                                <a href="debug_booking.php" class="btn btn-info ml-2">Debug Booking</a>
                            </div>
                        <?php else: ?>
                            <?php if (!empty($error_message)): ?>
                                <div class="alert alert-danger"><?php echo $error_message; ?></div>
                            <?php endif; ?>
                            
                            <p>This is a simplified booking form for testing purposes.</p>
                            
                            <form method="post">
                                <div class="form-group">
                                    <label>Service Type</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="layanan" id="teller" value="teller" checked>
                                        <label class="form-check-label" for="teller">
                                            Teller
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="layanan" id="cs" value="cs">
                                        <label class="form-check-label" for="cs">
                                            Customer Service
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="layanan" id="kredit" value="kredit">
                                        <label class="form-check-label" for="kredit">
                                            Kredit
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="tanggal">Visit Date</label>
                                    <input type="date" class="form-control" id="tanggal" name="tanggal" 
                                           value="<?php echo date('Y-m-d'); ?>" min="<?php echo date('Y-m-d'); ?>" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="waktu">Visit Time</label>
                                    <select class="form-control" id="waktu" name="waktu" required>
                                        <option value="08:00">08:00 - 09:00</option>
                                        <option value="09:00">09:00 - 10:00</option>
                                        <option value="10:00">10:00 - 11:00</option>
                                        <option value="11:00">11:00 - 12:00</option>
                                        <option value="13:00">13:00 - 14:00</option>
                                        <option value="14:00">14:00 - 15:00</option>
                                        <option value="15:00">15:00 - 16:00</option>
                                    </select>
                                </div>
                                
                                <button type="submit" class="btn btn-success btn-block">Book Now</button>
                            </form>
                            
                            <div class="mt-3">
                                <a href="debug_booking.php" class="btn btn-info btn-sm">Debug Booking</a>
                                <a href="simple_login.php" class="btn btn-secondary btn-sm ml-2">Back to Login</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
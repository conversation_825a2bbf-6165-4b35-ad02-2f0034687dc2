<?php
// <PERSON><PERSON><PERSON> to add alamat column to nasabah table
require_once '../includes/db.php';

// Check if alamat column already exists
$result = $conn->query("SHOW COLUMNS FROM nasabah LIKE 'alamat'");
$exists = ($result->num_rows > 0);

if (!$exists) {
    // Add alamat column to nasabah table
    $sql = "ALTER TABLE nasabah ADD COLUMN alamat TEXT AFTER no_hp";
    
    if ($conn->query($sql) === TRUE) {
        echo "Alamat column added successfully to nasabah table.";
    } else {
        echo "Error adding alamat column: " . $conn->error;
    }
} else {
    echo "Alamat column already exists in nasabah table.";
}

echo "<br><br><a href='register.php'>Go to Registration Page</a>";
?>

/* Custom CSS for Bank Queue Management System */

/* General Styles */
body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Bank BJB Theme Colors */
:root {
    --primary-color: #1a6a83; /* BJB Blue */
    --secondary-color: #0e4b5e; /* BJB Dark Blue */
    --accent-color: #d4b45a; /* BJB Gold */
    --success-color: #28a745; /* Green */
    --danger-color: #dc3545; /* Red */
    --warning-color: #ffc107; /* Yellow */
    --info-color: #17a2b8; /* Cyan */
    --dark-color: #343a40; /* Dark Gray */
    --light-color: #f8f9fa; /* Light Gray */
    --white-color: #ffffff; /* White */
    --loket1-color: #1a6a83; /* BJB Blue for Loket 1 */
    --loket2-color: #d4b45a; /* BJB <PERSON> for Loket 2 */
    --loket3-color: #20c997; /* Teal for Loket 3 */
    --loket4-color: #17a2b8; /* <PERSON>an for Loket 4 */
}

/* Bank BJB Background */
.bg-xbx {
    background: linear-gradient(135deg, #1a6a83 0%, #0e4b5e 100%);
    color: white;
}

.btn-xbx {
    background: linear-gradient(135deg, #1a6a83 0%, #0e4b5e 100%);
    color: white;
    border: none;
    box-shadow: 0 4px 6px rgba(26, 106, 131, 0.2);
}

.btn-xbx:hover, .btn-xbx:focus, .btn-xbx:active {
    background: linear-gradient(135deg, #155a70 0%, #0a3a4a 100%);
    color: white;
    box-shadow: 0 5px 8px rgba(26, 106, 131, 0.3);
}

.bg-loket1 {
    background-color: var(--loket1-color);
    color: white;
}

.bg-loket2 {
    background-color: var(--loket2-color);
    color: white;
}

.bg-loket3 {
    background-color: var(--loket3-color);
    color: white;
}

.bg-loket4 {
    background-color: var(--loket4-color);
    color: white;
}

/* Sidebar Styles */
.wrapper {
    display: flex;
    width: 100%;
    align-items: stretch;
}

#sidebar {
    min-width: 250px;
    max-width: 250px;
    background: #2d3436;
    color: #fff;
    transition: all 0.3s;
    height: 100vh;
    position: fixed;
    z-index: 999;
}

#sidebar.active {
    margin-left: -250px;
}

#sidebar .sidebar-header {
    padding: 20px;
    background: #212529;
    text-align: center;
}

#sidebar .sidebar-header img {
    height: 80px;
    margin-bottom: 10px;
}

#sidebar .sidebar-header h4 {
    color: white;
    font-weight: bold;
    margin-bottom: 5px;
}

#sidebar ul.components {
    padding: 20px 0;
}

#sidebar .menu-title {
    color: #adb5bd;
    text-transform: uppercase;
    font-size: 0.8em;
    font-weight: bold;
    padding: 15px 20px 5px;
    letter-spacing: 1px;
}

#sidebar ul li a {
    padding: 12px 20px;
    font-size: 1em;
    display: block;
    color: #dfe6e9;
    text-decoration: none;
    transition: all 0.3s;
    border-left: 3px solid transparent;
}

#sidebar ul li a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

#sidebar ul li a:hover {
    color: #fff;
    background: #3d4852;
    border-left: 3px solid var(--primary-color);
}

#sidebar ul li.active > a {
    color: #fff;
    background: #3d4852;
    border-left: 3px solid var(--primary-color);
}

#sidebar ul li.active > a:hover {
    background: #4b5563;
}

#content {
    width: 100%;
    padding: 20px;
    min-height: 100vh;
    transition: all 0.3s;
    margin-left: 250px;
    padding-bottom: 60px; /* Memberikan ruang untuk footer */
    position: relative;
}

#content.active {
    margin-left: 0;
}

#sidebarCollapse {
    display: block !important;
    background: #f8f9fa !important;
    color: var(--primary-color) !important;
    border: 1px solid #ddd !important;
    font-size: 1.5rem !important;
    transition: all 0.3s !important;
    padding: 8px 12px !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    z-index: 1050 !important;
    position: relative !important;
}

#sidebarCollapse:hover {
    color: var(--accent-color) !important;
    transform: rotate(90deg) !important;
    background: #e9ecef !important;
}

@media (max-width: 768px) {
    #sidebar {
        margin-left: 0;
        width: 250px;
        min-width: 250px;
        max-width: 250px;
    }
    #content {
        margin-left: 250px;
        width: calc(100% - 250px);
    }
}

.bg-gradient {
    background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
    color: white;
}

.card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    border: none;
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
    font-weight: bold;
}

/* Login Page */
.login-page {
    background: linear-gradient(135deg, #8a56e2 0%, #6a11cb 100%);
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-box {
    background: white;
    border-radius: 8px;
    padding: 30px;
    width: 100%;
    max-width: 400px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.login-logo {
    text-align: center;
    margin-bottom: 30px;
}

.login-logo img {
    height: 100px;
    margin-bottom: 15px;
}

.login-logo h3 {
    color: #333;
    font-weight: 600;
    margin-bottom: 5px;
}

.login-logo p {
    color: #777;
    font-size: 0.9rem;
}

.login-form .form-control {
    height: 45px;
    border-radius: 4px;
    border: 1px solid #ddd;
    padding-left: 15px;
}

.login-form .input-group-text {
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-right: none;
}

.login-form .btn-login {
    height: 45px;
    border-radius: 4px;
    font-weight: 600;
    background: linear-gradient(135deg, #8a56e2 0%, #6a11cb 100%);
    border: none;
    box-shadow: 0 4px 6px rgba(106, 17, 203, 0.2);
}

.login-form .btn-login:hover {
    background: linear-gradient(135deg, #7d4fd0 0%, #5f0fb6 100%);
    box-shadow: 0 5px 8px rgba(106, 17, 203, 0.3);
}

.login-footer {
    text-align: center;
    margin-top: 20px;
    font-size: 0.8rem;
    color: #777;
}

/* Dashboard Cards */
.dashboard-card {
    border-radius: 10px;
    overflow: hidden;
    transition: transform 0.3s;
}

.dashboard-card:hover {
    transform: translateY(-5px);
}

.dashboard-card .card-body {
    padding: 20px;
}

.dashboard-card .icon {
    font-size: 48px;
    margin-bottom: 15px;
}

.dashboard-card .count {
    font-size: 36px;
    font-weight: bold;
}

.dashboard-card .title {
    font-size: 18px;
    color: #6c757d;
}

/* Queue Display */
.queue-display {
    background-color: #fff;
    color: #333;
    padding: 25px;
    border-radius: 8px;
    margin-bottom: 25px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s;
}

.queue-display:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
}

.queue-number {
    font-size: 72px;
    font-weight: bold;
    text-align: center;
    margin: 20px 0;
    color: var(--primary-color);
}

.queue-info {
    font-size: 24px;
    text-align: center;
    color: #555;
}

.counter-box {
    background: linear-gradient(135deg, #8a56e2 0%, #6a11cb 100%);
    color: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(106, 17, 203, 0.2);
    transition: all 0.3s;
}

.counter-box:hover {
    box-shadow: 0 8px 25px rgba(106, 17, 203, 0.3);
    transform: translateY(-5px);
}

.counter-box h3 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
}

.counter-number {
    font-size: 48px;
    font-weight: bold;
    margin: 15px 0;
}

/* Queue Form */
.queue-form {
    max-width: 500px;
    margin: 0 auto;
}

.queue-form .form-control {
    height: 45px;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.queue-form .btn-primary {
    height: 45px;
    border-radius: 4px;
    font-weight: 600;
    background: linear-gradient(135deg, #8a56e2 0%, #6a11cb 100%);
    border: none;
    box-shadow: 0 4px 6px rgba(106, 17, 203, 0.2);
}

.queue-form .btn-primary:hover {
    background: linear-gradient(135deg, #7d4fd0 0%, #5f0fb6 100%);
    box-shadow: 0 5px 8px rgba(106, 17, 203, 0.3);
}

.queue-ticket {
    background-color: white;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    text-align: center;
    margin-top: 30px;
    transition: all 0.3s;
}

.queue-ticket:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
}

.queue-ticket .number {
    font-size: 60px;
    font-weight: bold;
    margin: 20px 0;
    color: var(--primary-color);
}

.queue-ticket .info {
    margin-bottom: 8px;
    color: #555;
    font-size: 16px;
}

.queue-ticket .estimate {
    margin-top: 20px;
    font-weight: bold;
    color: var(--success-color);
    font-size: 18px;
}

/* XBX TEAM Queue */
.xbx-queue-container {
    max-width: 500px;
    margin: 0 auto;
    padding: 30px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.xbx-queue-header {
    text-align: center;
    margin-bottom: 30px;
}

.xbx-queue-header img {
    height: 80px;
    margin-bottom: 15px;
}

.xbx-queue-header h3 {
    color: #333;
    font-weight: 600;
    margin-bottom: 5px;
}

.xbx-queue-header p {
    color: #777;
    font-size: 0.9rem;
}

.xbx-queue-form .form-control {
    height: 45px;
    border-radius: 4px;
    border: 1px solid #ddd;
    padding-left: 15px;
}

.xbx-queue-form .btn-primary {
    height: 45px;
    border-radius: 4px;
    font-weight: 600;
    background: linear-gradient(135deg, #8a56e2 0%, #6a11cb 100%);
    border: none;
    box-shadow: 0 4px 6px rgba(106, 17, 203, 0.2);
    width: 100%;
    margin-top: 15px;
}

.xbx-queue-form .btn-primary:hover {
    background: linear-gradient(135deg, #7d4fd0 0%, #5f0fb6 100%);
    box-shadow: 0 5px 8px rgba(106, 17, 203, 0.3);
}

.xbx-queue-number {
    text-align: center;
    margin-top: 30px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.xbx-queue-number h4 {
    color: #555;
    margin-bottom: 15px;
}

.xbx-queue-number .number {
    font-size: 60px;
    font-weight: bold;
    color: var(--primary-color);
    margin: 15px 0;
}

.xbx-queue-alert {
    background-color: rgba(138, 86, 226, 0.1);
    color: #8a56e2;
    border-color: rgba(138, 86, 226, 0.2);
    border-radius: 8px;
    padding: 15px 20px;
    margin-top: 20px;
    text-align: center;
}

/* Staff Counter */
.counter-control {
    text-align: center;
    margin-bottom: 30px;
}

.counter-control .btn {
    margin: 0 10px;
    padding: 10px 20px;
    font-size: 18px;
}

.current-serving {
    background-color: #28a745;
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    margin-bottom: 30px;
}

.current-serving .number {
    font-size: 48px;
    font-weight: bold;
    margin: 10px 0;
}

.waiting-list {
    max-height: 400px;
    overflow-y: auto;
}

/* Customer Verification */
.verification-status {
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 15px;
    font-weight: bold;
}

.verification-status.pending {
    background-color: #ffc107;
    color: #212529;
}

.verification-status.verified {
    background-color: #28a745;
    color: white;
}

.verification-status.rejected {
    background-color: #dc3545;
    color: white;
}

.id-preview {
    max-width: 100%;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    margin-top: 10px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .queue-number {
        font-size: 48px;
    }

    .queue-info {
        font-size: 18px;
    }

    .counter-number {
        font-size: 36px;
    }

    .dashboard-card .icon {
        font-size: 36px;
    }

    .dashboard-card .count {
        font-size: 28px;
    }

    /* Footer adjustments for mobile */
    footer {
        height: 30px !important;
    }

    footer span {
        font-size: 10px !important;
    }
}

/* Footer styles */
footer.bg-xbx {
    font-size: 11px;
    background-color: #1e2124 !important;
    border-top: 1px solid #2c3035;
}

/* Ensure content doesn't get hidden behind footer */
#content {
    padding-bottom: 30px !important;
}

/* Adjust sidebar to not overlap with footer */
#sidebar {
    padding-bottom: 30px;
}

/* Fix footer text alignment */
footer .text-left {
    text-align: left !important;
}

footer .text-right {
    text-align: right !important;
}

/* Custom colors for different service types */
.bg-teller {
    background-color: #dc3545 !important;
}

.bg-cs {
    background-color: #007bff !important;
}

.bg-kredit {
    background-color: #28a745 !important;
}

/* Main menu cards */
.menu-card {
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s;
    height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    color: white;
    text-decoration: none;
}

.menu-card:hover {
    transform: scale(1.05);
    text-decoration: none;
    color: white;
}

.menu-card h3 {
    font-size: 24px;
    font-weight: bold;
    text-align: center;
}

.menu-card.nomor {
    background-color: #dc3545;
}

.menu-card.dashboard {
    background-color: #007bff;
}

.menu-card.petugas {
    background-color: #ffc107;
    color: #212529;
}

/* Display screen */
.display-screen {
    background-color: #000;
    color: white;
    min-height: 100vh;
    padding: 20px;
}

.display-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 20px;
}

.display-logo img {
    height: 60px;
}

.display-time {
    font-size: 28px;
    font-weight: bold;
}

.display-content {
    display: flex;
    flex-wrap: wrap;
}

.display-counters {
    flex: 2;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding-right: 20px;
}

.display-counter {
    width: 48%;
    margin-bottom: 20px;
    border-radius: 8px;
    overflow: hidden;
}

.display-counter-header {
    padding: 15px;
    text-align: center;
    font-size: 24px;
    font-weight: bold;
}

.display-counter-body {
    padding: 25px;
    background-color: white;
    color: #212529;
    text-align: center;
}

.display-counter-number {
    font-size: 72px;
    font-weight: bold;
    margin: 20px 0;
}

.display-counter-customer {
    font-size: 24px;
    margin-bottom: 10px;
    color: #555;
}

.display-media {
    flex: 1;
    padding-left: 20px;
}

.display-video {
    margin-top: 20px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.display-video iframe {
    width: 100%;
    height: 300px;
    border: none;
}

/* XBX TEAM Display */
.xbx-display {
    background-color: #000;
    color: white;
    min-height: 100vh;
    padding: 20px;
}

.xbx-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 20px;
}

.xbx-logo img {
    height: 60px;
}

.xbx-time {
    font-size: 28px;
    font-weight: bold;
}

.xbx-content {
    display: flex;
    flex-wrap: wrap;
}

.xbx-counters {
    flex: 2;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.xbx-counter {
    width: 48%;
    margin-bottom: 20px;
    border-radius: 8px;
    overflow: hidden;
}

.xbx-counter-header {
    padding: 15px;
    text-align: center;
    font-size: 24px;
    font-weight: bold;
}

.xbx-counter-body {
    padding: 25px;
    background-color: white;
    color: #212529;
    text-align: center;
}

.xbx-counter-number {
    font-size: 72px;
    font-weight: bold;
    margin: 20px 0;
}

.xbx-counter-customer {
    font-size: 24px;
    margin-bottom: 10px;
    color: #555;
}

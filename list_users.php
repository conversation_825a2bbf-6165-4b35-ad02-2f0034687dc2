<?php
/**
 * List Users Script
 * 
 * This script displays all users in the system.
 * For security reasons, this file should be deleted after use.
 */

// Include database configuration
require_once 'config/database.php';

// Get all users
$sql = "SELECT id, username, nama_lengkap, role FROM users ORDER BY id";
$result = query($sql);
$users = fetch_all($result);
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daftar User - Sistem Antrian Bank</title>
    
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    
    <style>
        body {
            background-color: #f8f9fa;
            padding: 50px 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Daftar User</h1>
            <p class="text-muted">Semua user yang terdaftar dalam sistem.</p>
        </div>
        
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Username</th>
                        <th>Nama Lengkap</th>
                        <th>Role</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if(count($users) > 0): ?>
                    <?php foreach($users as $user): ?>
                    <tr>
                        <td><?php echo $user['id']; ?></td>
                        <td><?php echo $user['username']; ?></td>
                        <td><?php echo $user['nama_lengkap']; ?></td>
                        <td>
                            <?php if($user['role'] == 'admin'): ?>
                            <span class="badge badge-primary">Admin</span>
                            <?php else: ?>
                            <span class="badge badge-info">Staff</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                    <?php else: ?>
                    <tr>
                        <td colspan="4" class="text-center">Tidak ada data user.</td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        
        <div class="text-center mt-4">
            <a href="add_user.php" class="btn btn-primary">
                <i class="fas fa-user-plus"></i> Tambah User Baru
            </a>
            <a href="login.php" class="btn btn-link">
                <i class="fas fa-arrow-left"></i> Kembali ke Halaman Login
            </a>
        </div>
        
        <div class="alert alert-warning mt-4">
            <i class="fas fa-exclamation-triangle"></i> <strong>Peringatan:</strong> Untuk alasan keamanan, hapus file ini setelah digunakan.
        </div>
    </div>
    
    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

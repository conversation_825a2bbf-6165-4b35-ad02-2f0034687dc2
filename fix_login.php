<?php
/**
 * Fix Login Script
 * 
 * This script resets the admin and staff passwords to known values.
 * For security reasons, delete this file after use.
 */

// Include database configuration
require_once 'config/database.php';

// Set new passwords
$admin_password = 'admin123';
$staff_password = 'petugas123';

// Hash passwords
$admin_hash = password_hash($admin_password, PASSWORD_DEFAULT);
$staff_hash = password_hash($staff_password, PASSWORD_DEFAULT);

// Update admin password
$sql = "UPDATE users SET password = '$admin_hash' WHERE username = 'admin'";
$admin_result = query($sql);

// Check if staff user exists
$sql = "SELECT * FROM users WHERE username = 'petugas'";
$result = query($sql);

if (num_rows($result) > 0) {
    // Update staff password
    $sql = "UPDATE users SET password = '$staff_hash' WHERE username = 'petugas'";
    $staff_result = query($sql);
} else {
    // Create staff user
    $sql = "INSERT INTO users (username, password, nama_lengkap, role) 
            VALUES ('petugas', '$staff_hash', 'Petugas Loket', 'staff')";
    $staff_result = query($sql);
}

// Create a test user with simple password
$test_password = '123456';
$test_hash = password_hash($test_password, PASSWORD_DEFAULT);
$sql = "INSERT INTO users (username, password, nama_lengkap, role) 
        VALUES ('test', '$test_hash', 'Test User', 'staff')";
$test_result = query($sql);

// Check results
$success = $admin_result && $staff_result;

// Get all users
$sql = "SELECT id, username, nama_lengkap, role FROM users ORDER BY id";
$result = query($sql);
$users = fetch_all($result);
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Login - Sistem Antrian Bank</title>
    
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    
    <style>
        body {
            background-color: #f8f9fa;
            padding: 50px 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Fix Login</h1>
            <p class="text-muted">Memperbaiki masalah login dengan mereset password.</p>
        </div>
        
        <?php if($success): ?>
        <div class="alert alert-success">
            <h4 class="alert-heading">Password Berhasil Direset!</h4>
            <p>Password untuk akun admin dan petugas telah berhasil direset.</p>
            <hr>
            <p>Anda sekarang dapat login dengan kredensial berikut:</p>
            <ul>
                <li><strong>Admin:</strong> username: admin, password: admin123</li>
                <li><strong>Petugas:</strong> username: petugas, password: petugas123</li>
                <li><strong>Test:</strong> username: test, password: 123456</li>
            </ul>
        </div>
        <?php else: ?>
        <div class="alert alert-danger">
            <h4 class="alert-heading">Gagal Mereset Password!</h4>
            <p>Terjadi kesalahan saat mencoba mereset password. Silahkan coba lagi.</p>
        </div>
        <?php endif; ?>
        
        <div class="card mt-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-users"></i> Daftar User
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Username</th>
                                <th>Nama Lengkap</th>
                                <th>Role</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if(count($users) > 0): ?>
                            <?php foreach($users as $user): ?>
                            <tr>
                                <td><?php echo $user['id']; ?></td>
                                <td><?php echo $user['username']; ?></td>
                                <td><?php echo $user['nama_lengkap']; ?></td>
                                <td>
                                    <?php if($user['role'] == 'admin'): ?>
                                    <span class="badge badge-primary">Admin</span>
                                    <?php else: ?>
                                    <span class="badge badge-info">Staff</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            <?php else: ?>
                            <tr>
                                <td colspan="4" class="text-center">Tidak ada data user.</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-4">
            <a href="login.php" class="btn btn-primary">
                <i class="fas fa-sign-in-alt"></i> Login Sekarang
            </a>
        </div>
        
        <div class="alert alert-warning mt-4">
            <i class="fas fa-exclamation-triangle"></i> <strong>Peringatan:</strong> Untuk alasan keamanan, hapus file ini setelah digunakan.
        </div>
    </div>
    
    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

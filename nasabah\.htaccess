# Enable URL rewriting
RewriteEngine On

# Set base directory
RewriteBase /antrian_bank/nasabah/

# Redirect to index.php if file or directory doesn't exist
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php?url=$1 [QSA,L]

# Prevent directory listing
Options -Indexes

# Set default character set
AddDefaultCharset UTF-8

# Set default document
DirectoryIndex index.php

# Protect .htaccess file
<Files .htaccess>
    Order Allow,Deny
    Deny from all
</Files>

# Protect sensitive files
<FilesMatch "^(config\.php|functions\.php|db\.php)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

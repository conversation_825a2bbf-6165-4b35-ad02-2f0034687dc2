<?php
// <PERSON><PERSON><PERSON> to fix nasabah table structure
require_once '../includes/db.php';

// Check if table exists
$result = $conn->query("SHOW TABLES LIKE 'nasabah'");
$table_exists = ($result->num_rows > 0);

if (!$table_exists) {
    // Create nasabah table if it doesn't exist
    $sql = "CREATE TABLE nasabah (
        id INT(11) NOT NULL AUTO_INCREMENT,
        nama VARCHAR(100) NOT NULL,
        no_identitas VARCHAR(20) NOT NULL,
        jenis_identitas ENUM('ktp', 'sim', 'passport') NOT NULL DEFAULT 'ktp',
        email VARCHAR(100) NOT NULL,
        no_hp VARCHAR(15) NOT NULL,
        password VARCHAR(255) NOT NULL,
        alamat TEXT NOT NULL,
        status_verifikasi ENUM('belum', 'sudah', 'ditolak') DEFAULT 'belum',
        is_blocked TINYINT(1) DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY (email),
        UNIQUE KEY (no_identitas)
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "Table 'nasabah' created successfully.<br>";
    } else {
        echo "Error creating table 'nasabah': " . $conn->error . "<br>";
    }
} else {
    echo "Table 'nasabah' already exists.<br>";
    
    // Check and add required columns
    $required_columns = [
        ['nama', 'VARCHAR(100) NOT NULL'],
        ['no_identitas', 'VARCHAR(20) NOT NULL'],
        ['jenis_identitas', "ENUM('ktp', 'sim', 'passport') NOT NULL DEFAULT 'ktp'"],
        ['email', 'VARCHAR(100) NOT NULL'],
        ['no_hp', 'VARCHAR(15) NOT NULL'],
        ['password', 'VARCHAR(255) NOT NULL'],
        ['alamat', 'TEXT NOT NULL'],
        ['status_verifikasi', "ENUM('belum', 'sudah', 'ditolak') DEFAULT 'belum'"],
        ['is_blocked', 'TINYINT(1) DEFAULT 0'],
        ['created_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP'],
        ['updated_at', 'DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP']
    ];
    
    foreach ($required_columns as $column) {
        $column_name = $column[0];
        $column_def = $column[1];
        
        $result = $conn->query("SHOW COLUMNS FROM nasabah LIKE '$column_name'");
        $column_exists = ($result->num_rows > 0);
        
        if (!$column_exists) {
            $sql = "ALTER TABLE nasabah ADD COLUMN $column_name $column_def";
            if ($conn->query($sql) === TRUE) {
                echo "Column '$column_name' added successfully.<br>";
            } else {
                echo "Error adding column '$column_name': " . $conn->error . "<br>";
            }
        } else {
            echo "Column '$column_name' already exists.<br>";
        }
    }
    
    // Check and add unique keys
    $result = $conn->query("SHOW INDEX FROM nasabah WHERE Key_name = 'email'");
    $email_key_exists = ($result->num_rows > 0);
    
    if (!$email_key_exists) {
        $sql = "ALTER TABLE nasabah ADD UNIQUE KEY (email)";
        if ($conn->query($sql) === TRUE) {
            echo "Unique key for 'email' added successfully.<br>";
        } else {
            echo "Error adding unique key for 'email': " . $conn->error . "<br>";
        }
    } else {
        echo "Unique key for 'email' already exists.<br>";
    }
    
    $result = $conn->query("SHOW INDEX FROM nasabah WHERE Key_name = 'no_identitas'");
    $no_identitas_key_exists = ($result->num_rows > 0);
    
    if (!$no_identitas_key_exists) {
        $sql = "ALTER TABLE nasabah ADD UNIQUE KEY (no_identitas)";
        if ($conn->query($sql) === TRUE) {
            echo "Unique key for 'no_identitas' added successfully.<br>";
        } else {
            echo "Error adding unique key for 'no_identitas': " . $conn->error . "<br>";
        }
    } else {
        echo "Unique key for 'no_identitas' already exists.<br>";
    }
}

echo "<br><h3>Nasabah table structure has been fixed.</h3>";
echo "<a href='register.php' class='btn btn-primary'>Go to Registration Page</a>";
?>

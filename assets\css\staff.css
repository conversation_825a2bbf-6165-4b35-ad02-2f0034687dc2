/* Custom CSS for Staff Interface */

/* Bank BJB Theme Colors */
:root {
    --primary-color: #1a6a83; /* BJB Blue */
    --secondary-color: #0e4b5e; /* BJB Dark Blue */
    --accent-color: #d4b45a; /* BJB Gold */
    --success-color: #28a745; /* Green */
    --danger-color: #dc3545; /* Red */
    --warning-color: #ffc107; /* Yellow */
    --info-color: #17a2b8; /* Cyan */
    --dark-color: #343a40; /* Dark Gray */
    --light-color: #f8f9fa; /* Light Gray */
    --white-color: #ffffff; /* White */
}

/* Gradient Backgrounds */
.bg-gradient-primary {
    background: linear-gradient(135deg, #1a6a83 0%, #0e4b5e 100%);
    color: white;
}

.bg-gradient-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    color: white;
}

.bg-gradient-info {
    background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
    color: white;
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #ffc107 0%, #d39e00 100%);
    color: #212529;
}

.bg-gradient-danger {
    background: linear-gradient(135deg, #dc3545 0%, #bd2130 100%);
    color: white;
}

/* Card Styling */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
}

.card-header {
    padding: 15px 20px;
    font-weight: 600;
    border-bottom: none;
}

.card-body {
    padding: 20px;
}

/* Current Serving Box */
.current-serving {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.current-serving:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.current-serving .number {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin: 10px 0;
}

.current-serving .customer,
.current-serving .service {
    font-size: 1.5rem;
    font-weight: 500;
    margin-bottom: 10px;
}

/* Button Styling */
.btn {
    border-radius: 5px;
    font-weight: 500;
    padding: 10px 20px;
    transition: all 0.3s ease;
}

.btn-lg {
    padding: 12px 24px;
    font-size: 1rem;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    box-shadow: 0 4px 8px rgba(26, 106, 131, 0.3);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
    color: #212529;
}

.btn-warning:hover {
    background-color: #e0a800;
    border-color: #d39e00;
    box-shadow: 0 4px 8px rgba(255, 193, 7, 0.3);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.btn-info {
    background-color: var(--info-color);
    border-color: var(--info-color);
}

.btn-info:hover {
    background-color: #138496;
    border-color: #117a8b;
    box-shadow: 0 4px 8px rgba(23, 162, 184, 0.3);
}

/* Table Styling */
.table {
    margin-bottom: 0;
}

.table thead th {
    background-color: #f8f9fa;
    border-top: none;
    border-bottom: 2px solid #e9ecef;
    color: #495057;
    font-weight: 600;
    padding: 12px 15px;
}

.table tbody td {
    padding: 12px 15px;
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: rgba(26, 106, 131, 0.05);
}

/* Badge Styling */
.badge {
    font-weight: 500;
    padding: 6px 10px;
    border-radius: 4px;
}

.badge-primary {
    background-color: var(--primary-color);
}

/* Info Box Styling */
.info-box {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.info-box-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 1.5rem;
}

.info-box-content {
    flex: 1;
}

.info-box-label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 2px;
}

.info-box-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #212529;
}

/* Clock Styling */
#clock {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    font-size: 1.2rem;
    color: var(--primary-color);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .current-serving .number {
        font-size: 2.5rem;
    }

    .current-serving .customer,
    .current-serving .service {
        font-size: 1.2rem;
    }

    .btn-lg {
        padding: 10px 16px;
        font-size: 0.9rem;
    }
}

/* Shadow Effects */
.shadow {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

/* Animation */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* Counter Control Buttons */
.counter-control {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.counter-control .btn {
    flex: 1;
    max-width: 200px;
}

/* Sidebar Styling */
#sidebar {
    min-width: 250px;
    max-width: 250px;
    background: linear-gradient(180deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: #fff;
    transition: all 0.3s;
    height: 100vh;
    position: fixed;
    z-index: 999;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
}

#sidebar.active {
    margin-left: -250px;
}

#sidebar .sidebar-header {
    padding: 20px 15px;
    background: rgba(0, 0, 0, 0.2);
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

#sidebar .sidebar-header .logo-container {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 15px;
    padding: 5px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    min-height: 120px;
}

#sidebar .sidebar-header .logo-bjb {
    width: 100%;
    max-width: 180px;
    height: auto;
    border-radius: 10px;
    padding: 8px;
    background-color: white;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    object-fit: contain;
    display: block !important;
    margin: 0 auto;
    max-height: 100px;
}

#sidebar .sidebar-header .logo-bjb:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
}

#sidebar .sidebar-header h4 {
    color: white;
    font-weight: bold;
    margin-bottom: 10px;
    font-size: 1.2rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
    line-height: 1.3;
    letter-spacing: 0.5px;
}

#sidebar .sidebar-header p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0;
    font-size: 0.9rem;
    background-color: rgba(0, 0, 0, 0.2);
    padding: 5px 10px;
    border-radius: 20px;
    display: inline-block;
}

#sidebar ul.components {
    padding: 20px 0;
}

#sidebar ul li {
    margin-bottom: 5px;
}

#sidebar ul li a {
    padding: 12px 20px;
    font-size: 1.1em;
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s;
    border-radius: 8px;
    margin: 0 10px 8px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

#sidebar ul li a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: all 0.5s;
}

#sidebar ul li a:hover::before {
    left: 100%;
}

#sidebar ul li a i {
    margin-right: 15px;
    width: 36px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transition: all 0.3s;
    font-size: 1.1rem;
}

#sidebar ul li a:hover {
    color: #fff;
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

#sidebar ul li a:hover i {
    background-color: var(--accent-color);
    color: var(--dark-color);
}

#sidebar ul li.active > a {
    color: #fff;
    background: rgba(255, 255, 255, 0.2);
    border-right: 3px solid var(--accent-color);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    transform: translateX(5px);
}

#sidebar ul li.active > a i {
    background-color: var(--accent-color);
    color: var(--dark-color);
    box-shadow: 0 4px 8px rgba(212, 180, 90, 0.3);
}

/* Content Area Styling */
#content {
    width: calc(100% - 250px);
    padding: 20px;
    min-height: 100vh;
    transition: all 0.3s;
    margin-left: 250px;
    padding-bottom: 60px;
    position: relative;
    background-color: #f8f9fa;
    overflow-x: hidden;
    max-width: 100vw;
}

#content.active {
    width: 100%;
    margin-left: 0;
}

/* Navbar Styling */
.navbar {
    padding: 15px 20px;
    background: #fff;
    border: none;
    border-radius: 0;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 40px;
}

/* Container width control */
.container-fluid {
    max-width: 100%;
    padding-left: 15px;
    padding-right: 15px;
}

/* Card sizing */
.card {
    width: 100%;
    overflow: hidden;
}

/* Table responsiveness */
.table-responsive {
    width: 100%;
    overflow-x: auto;
}

#sidebarCollapse {
    display: block !important;
    background: #f8f9fa !important;
    color: var(--primary-color) !important;
    border: 1px solid #ddd !important;
    font-size: 1.5rem !important;
    transition: all 0.3s !important;
    padding: 8px 12px !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    z-index: 1050 !important;
    position: relative !important;
}

#sidebarCollapse:hover {
    color: var(--accent-color) !important;
    transform: rotate(90deg) !important;
    background: #e9ecef !important;
}

@media (max-width: 768px) {
    #sidebar {
        margin-left: -250px;
    }
    #sidebar.active {
        margin-left: 0;
    }
    #content {
        margin-left: 0;
        width: 100%;
    }
    #content.active {
        margin-left: 250px;
        width: 100%;
    }
    #sidebarCollapse {
        display: block;
    }

    .table-responsive {
        font-size: 0.9rem;
    }

    .card-body {
        padding: 1rem;
    }
}

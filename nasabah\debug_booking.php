<?php
// Debug Booking
// Script untuk debugging nasabah booking

require_once '../includes/functions.php';
require_once '../includes/db.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Create a debug function
function debug_output($title, $data) {
    echo "<h3>$title</h3>";
    echo "<pre>";
    print_r($data);
    echo "</pre>";
    echo "<hr>";
}

// If POST request, try to fix session issues
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['fix_session'])) {
        // Fix the session
        if (isset($_SESSION['nasabah_id']) && !isset($_SESSION['user_id'])) {
            $_SESSION['user_id'] = $_SESSION['nasabah_id'];
            $_SESSION['user_role'] = 'nasabah';
            echo "<div class='alert alert-success'>Session fixed! user_id and user_role set from nasabah_id.</div>";
        } else {
            echo "<div class='alert alert-warning'>No fix needed or session can't be fixed automatically.</div>";
        }
    } else if (isset($_POST['test_connection'])) {
        // Test database connection
        try {
            $test_query = "SELECT 1";
            $result = $conn->query($test_query);
            if ($result) {
                echo "<div class='alert alert-success'>Database connection successful!</div>";
            } else {
                echo "<div class='alert alert-danger'>Database connection failed: " . $conn->error . "</div>";
            }
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>Database connection error: " . $e->getMessage() . "</div>";
        }
    } else if (isset($_POST['test_create_queue'])) {
        // Test create queue from booking
        require_once '../includes/queue_connector.php';
        try {
            // Create test booking ID
            $test_booking_id = 'TEST' . date('YmdHis');
            $test_queue_number = 'T999';
            $test_date = date('Y-m-d');
            $nasabah_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : (isset($_SESSION['nasabah_id']) ? $_SESSION['nasabah_id'] : 1);
            
            $queue_id = create_queue_from_booking($test_booking_id, $test_queue_number, $test_date, $nasabah_id, 'teller');
            
            if ($queue_id) {
                echo "<div class='alert alert-success'>Queue creation successful! Queue ID: $queue_id</div>";
                // Remove test entry
                $conn->query("DELETE FROM antrian WHERE booking_id = '$test_booking_id'");
            } else {
                echo "<div class='alert alert-danger'>Queue creation failed.</div>";
            }
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>Queue creation error: " . $e->getMessage() . "</div>";
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Booking</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
</head>
<body>
    <div class="container mt-5">
        <h1 class="mb-4">Debug Booking</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        Session Data
                    </div>
                    <div class="card-body">
                        <?php debug_output("Session Variables", $_SESSION); ?>
                        
                        <form method="post">
                            <button type="submit" name="fix_session" class="btn btn-warning">Fix Session</button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        Database Connection
                    </div>
                    <div class="card-body">
                        <form method="post" class="mb-3">
                            <button type="submit" name="test_connection" class="btn btn-info">Test Database Connection</button>
                        </form>
                        
                        <?php
                        try {
                            // Check if antrian table exists
                            $table_check = "SHOW TABLES LIKE 'antrian'";
                            $result = $conn->query($table_check);
                            
                            if ($result->num_rows > 0) {
                                echo "<div class='alert alert-success'>Table 'antrian' exists.</div>";
                                
                                // Check structure
                                $structure_check = "DESCRIBE antrian";
                                $structure_result = $conn->query($structure_check);
                                
                                echo "<h5>Antrian Table Structure:</h5>";
                                echo "<ul>";
                                while ($row = $structure_result->fetch_assoc()) {
                                    echo "<li><strong>{$row['Field']}</strong> - {$row['Type']} {$row['Null']}</li>";
                                }
                                echo "</ul>";
                            } else {
                                echo "<div class='alert alert-danger'>Table 'antrian' does not exist!</div>";
                            }
                            
                            // Check if bookings table exists
                            $table_check = "SHOW TABLES LIKE 'bookings'";
                            $result = $conn->query($table_check);
                            
                            if ($result->num_rows > 0) {
                                echo "<div class='alert alert-success'>Table 'bookings' exists.</div>";
                                
                                // Check structure
                                $structure_check = "DESCRIBE bookings";
                                $structure_result = $conn->query($structure_check);
                                
                                echo "<h5>Bookings Table Structure:</h5>";
                                echo "<ul>";
                                while ($row = $structure_result->fetch_assoc()) {
                                    echo "<li><strong>{$row['Field']}</strong> - {$row['Type']} {$row['Null']}</li>";
                                }
                                echo "</ul>";
                            } else {
                                echo "<div class='alert alert-danger'>Table 'bookings' does not exist!</div>";
                            }
                        } catch (Exception $e) {
                            echo "<div class='alert alert-danger'>Database error: " . $e->getMessage() . "</div>";
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                Queue Connector Test
            </div>
            <div class="card-body">
                <form method="post">
                    <button type="submit" name="test_create_queue" class="btn btn-success">Test Queue Creation</button>
                </form>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                Recent Bookings
            </div>
            <div class="card-body">
                <?php
                try {
                    // Get recent bookings
                    $bookings_query = "SELECT * FROM bookings ORDER BY created_at DESC LIMIT 5";
                    $bookings_result = $conn->query($bookings_query);
                    
                    if ($bookings_result && $bookings_result->num_rows > 0) {
                        echo "<h5>Recent Bookings:</h5>";
                        echo "<table class='table table-striped'>";
                        echo "<thead><tr><th>ID</th><th>Booking ID</th><th>Nasabah ID</th><th>Nama</th><th>Layanan</th><th>Status</th><th>Created At</th></tr></thead>";
                        echo "<tbody>";
                        
                        while ($row = $bookings_result->fetch_assoc()) {
                            echo "<tr>";
                            echo "<td>{$row['id']}</td>";
                            echo "<td>{$row['booking_id']}</td>";
                            echo "<td>{$row['nasabah_id']}</td>";
                            echo "<td>{$row['nama']}</td>";
                            echo "<td>{$row['layanan']}</td>";
                            echo "<td>{$row['status']}</td>";
                            echo "<td>{$row['created_at']}</td>";
                            echo "</tr>";
                        }
                        
                        echo "</tbody></table>";
                    } else {
                        echo "<div class='alert alert-warning'>No recent bookings found.</div>";
                    }
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>Error retrieving bookings: " . $e->getMessage() . "</div>";
                }
                ?>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                Recent Queue Entries
            </div>
            <div class="card-body">
                <?php
                try {
                    // Get recent queue entries
                    $queue_query = "SELECT * FROM antrian ORDER BY created_at DESC LIMIT 5";
                    $queue_result = $conn->query($queue_query);
                    
                    if ($queue_result && $queue_result->num_rows > 0) {
                        echo "<h5>Recent Queue Entries:</h5>";
                        echo "<table class='table table-striped'>";
                        echo "<thead><tr><th>ID</th><th>Booking ID</th><th>Nomor Antrian</th><th>Nasabah ID</th><th>Status</th><th>Created At</th></tr></thead>";
                        echo "<tbody>";
                        
                        while ($row = $queue_result->fetch_assoc()) {
                            echo "<tr>";
                            echo "<td>{$row['id']}</td>";
                            echo "<td>{$row['booking_id']}</td>";
                            echo "<td>{$row['nomor_antrian']}</td>";
                            echo "<td>{$row['nasabah_id']}</td>";
                            echo "<td>{$row['status']}</td>";
                            echo "<td>{$row['created_at']}</td>";
                            echo "</tr>";
                        }
                        
                        echo "</tbody></table>";
                    } else {
                        echo "<div class='alert alert-warning'>No recent queue entries found.</div>";
                    }
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>Error retrieving queue entries: " . $e->getMessage() . "</div>";
                }
                ?>
            </div>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
<?php
// AJAX handler for changing counter
header('Content-Type: application/json');
session_start();

// Include database configuration
require_once '../config/database.php';
require_once '../includes/functions_mock.php';

// Check if user is logged in and is staff
if (!is_logged_in() || !is_staff()) {
    echo json_encode([
        'success' => false,
        'message' => 'Anda tidak memiliki akses.'
    ]);
    exit;
}

// Get user ID
$user_id = $_SESSION['user_id'];

// Process counter selection
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['counter_id'])) {
    $counter_id = (int)$_POST['counter_id'];

    // Validate counter
    $sql = "SELECT * FROM loket WHERE id = $counter_id AND status = 'aktif'";
    $result = query($sql);

    if (num_rows($result) == 1) {
        // Check if there's any active queue for current counter
        $sql = "SELECT l.id FROM loket l
                JOIN antrian a ON l.id = a.loket_id
                WHERE l.user_id = $user_id
                AND a.status = 'dipanggil'
                AND a.tanggal = CURDATE()";
        $result = query($sql);

        if (num_rows($result) > 0) {
            echo json_encode([
                'success' => false,
                'message' => 'Anda masih memiliki antrian yang sedang dilayani. Silahkan selesaikan terlebih dahulu.'
            ]);
            exit;
        } else {
            // Get current counter's jenis_layanan
            $sql = "SELECT jenis_layanan FROM loket WHERE id = $counter_id";
            $result = query($sql);
            $counter = fetch_assoc($result);
            $jenis_layanan = $counter['jenis_layanan'];

            // Check if user_id exists in users table
            $sql_check_user = "SELECT id FROM users WHERE id = $user_id";
            $result_check_user = query($sql_check_user);

            if (num_rows($result_check_user) == 0) {
                echo json_encode([
                    'success' => false,
                    'message' => 'User ID tidak valid. Silakan login ulang.'
                ]);
                exit;
            }

            // Update counter assignment
            $sql = "UPDATE loket SET user_id = NULL WHERE user_id = $user_id";
            query($sql);

            $sql = "UPDATE loket SET user_id = $user_id WHERE id = $counter_id";
            query($sql);

            echo json_encode([
                'success' => true,
                'message' => 'Loket berhasil diganti.'
            ]);
            exit;
        }
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Loket tidak valid.'
        ]);
        exit;
    }
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Permintaan tidak valid.'
    ]);
    exit;
}
?>

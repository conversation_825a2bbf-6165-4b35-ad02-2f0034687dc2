<?php
// Fix database connection and setup real database
echo "<h1>Database Connection Fix</h1>";

// Database credentials
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'antrian_bank';

echo "<h2>1. Testing MySQL Connection</h2>";

// Test direct MySQL connection
try {
    $test_conn = new mysqli($host, $username, $password);

    if ($test_conn->connect_error) {
        echo "<p>❌ MySQL connection failed: " . $test_conn->connect_error . "</p>";
        echo "<p>Please make sure:</p>";
        echo "<ul>";
        echo "<li>XAMPP/Laragon is running</li>";
        echo "<li>MySQL service is started</li>";
        echo "<li>Port 3306 is not blocked</li>";
        echo "</ul>";
        exit;
    } else {
        echo "<p>✅ MySQL connection successful</p>";
    }

    // Create database if not exists
    echo "<h2>2. Creating Database</h2>";
    $sql = "CREATE DATABASE IF NOT EXISTS $database";
    if ($test_conn->query($sql)) {
        echo "<p>✅ Database '$database' created/verified</p>";
    } else {
        echo "<p>❌ Failed to create database: " . $test_conn->error . "</p>";
    }

    // Select database
    $test_conn->select_db($database);

    echo "<h2>3. Creating Tables</h2>";

    // Create users table
    $sql_users = "CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        nama_lengkap VARCHAR(100) NOT NULL,
        email VARCHAR(100),
        role ENUM('admin', 'staff') NOT NULL DEFAULT 'staff',
        status ENUM('aktif', 'nonaktif') NOT NULL DEFAULT 'aktif',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";

    if ($test_conn->query($sql_users)) {
        echo "<p>✅ Users table created/verified</p>";
    } else {
        echo "<p>❌ Failed to create users table: " . $test_conn->error . "</p>";
    }

    // Create loket table
    $sql_loket = "CREATE TABLE IF NOT EXISTS loket (
        id INT AUTO_INCREMENT PRIMARY KEY,
        nama_loket VARCHAR(50) NOT NULL,
        jenis_layanan ENUM('teller', 'cs', 'kredit') NOT NULL,
        status ENUM('aktif', 'nonaktif') NOT NULL DEFAULT 'aktif',
        user_id INT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";

    if ($test_conn->query($sql_loket)) {
        echo "<p>✅ Loket table created/verified</p>";
    } else {
        echo "<p>❌ Failed to create loket table: " . $test_conn->error . "</p>";
    }

    // Create nasabah table
    $sql_nasabah = "CREATE TABLE IF NOT EXISTS nasabah (
        id INT AUTO_INCREMENT PRIMARY KEY,
        nama VARCHAR(100) NOT NULL,
        no_identitas VARCHAR(20) NOT NULL,
        jenis_identitas ENUM('ktp', 'buku_tabungan') NOT NULL DEFAULT 'ktp',
        email VARCHAR(100),
        no_hp VARCHAR(20),
        password VARCHAR(255),
        alamat TEXT,
        file_identitas VARCHAR(255),
        status_verifikasi ENUM('belum', 'sudah', 'ditolak') NOT NULL DEFAULT 'belum',
        alasan_penolakan TEXT,
        is_blocked TINYINT(1) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";

    if ($test_conn->query($sql_nasabah)) {
        echo "<p>✅ Nasabah table created/verified</p>";
    } else {
        echo "<p>❌ Failed to create nasabah table: " . $test_conn->error . "</p>";
    }

    // Create antrian table
    $sql_antrian = "CREATE TABLE IF NOT EXISTS antrian (
        id INT AUTO_INCREMENT PRIMARY KEY,
        nomor_antrian VARCHAR(10) NOT NULL,
        nasabah_id INT,
        nama VARCHAR(100) NOT NULL,
        no_identitas VARCHAR(20),
        layanan ENUM('teller', 'cs', 'kredit') NOT NULL,
        jenis_layanan ENUM('teller', 'cs', 'kredit') NOT NULL,
        tanggal DATE NOT NULL,
        waktu TIME,
        status ENUM('menunggu', 'dipanggil', 'selesai', 'batal') NOT NULL DEFAULT 'menunggu',
        loket_id INT,
        booking_dari ENUM('online', 'langsung') DEFAULT 'langsung',
        keterangan TEXT,
        waktu_booking TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        waktu_dipanggil TIMESTAMP NULL,
        waktu_selesai TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (nasabah_id) REFERENCES nasabah(id) ON DELETE SET NULL,
        FOREIGN KEY (loket_id) REFERENCES loket(id) ON DELETE SET NULL
    )";

    if ($test_conn->query($sql_antrian)) {
        echo "<p>✅ Antrian table created/verified</p>";
    } else {
        echo "<p>❌ Failed to create antrian table: " . $test_conn->error . "</p>";
    }

    echo "<h2>4. Adding Default Data</h2>";

    // Check if admin exists
    $result = $test_conn->query("SELECT COUNT(*) as count FROM users WHERE role = 'admin'");
    $row = $result->fetch_assoc();

    if ($row['count'] == 0) {
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $sql = "INSERT INTO users (username, password, nama_lengkap, email, role, status)
                VALUES ('admin', '$admin_password', 'Administrator', '<EMAIL>', 'admin', 'aktif')";

        if ($test_conn->query($sql)) {
            echo "<p>✅ Admin user created (username: admin, password: admin123)</p>";
        } else {
            echo "<p>❌ Failed to create admin user: " . $test_conn->error . "</p>";
        }
    } else {
        echo "<p>ℹ️ Admin user already exists</p>";
    }

    // Check if staff exists
    $result = $test_conn->query("SELECT COUNT(*) as count FROM users WHERE role = 'staff'");
    $row = $result->fetch_assoc();

    if ($row['count'] == 0) {
        echo "<p>Adding default staff...</p>";

        $staff_data = [
            ['username' => 'petugas1', 'nama' => 'Petugas Teller 1'],
            ['username' => 'petugas2', 'nama' => 'Petugas Teller 2'],
            ['username' => 'petugas3', 'nama' => 'Petugas CS 1'],
            ['username' => 'petugas4', 'nama' => 'Petugas CS 2'],
            ['username' => 'petugas5', 'nama' => 'Petugas Kredit 1']
        ];

        foreach ($staff_data as $staff) {
            $password = password_hash('petugas123', PASSWORD_DEFAULT);
            $sql = "INSERT INTO users (username, password, nama_lengkap, email, role, status)
                    VALUES ('{$staff['username']}', '$password', '{$staff['nama']}',
                            '{$staff['username']}@bankbjb.com', 'staff', 'aktif')";

            if ($test_conn->query($sql)) {
                echo "<p>✅ Added staff: {$staff['nama']}</p>";
            } else {
                echo "<p>❌ Failed to add staff: {$staff['nama']} - " . $test_conn->error . "</p>";
            }
        }
    } else {
        echo "<p>ℹ️ Staff users already exist ({$row['count']} found)</p>";
    }

    // Check if loket data exists
    $result = $test_conn->query("SELECT COUNT(*) as count FROM loket");
    $row = $result->fetch_assoc();

    if ($row['count'] == 0) {
        echo "<p>Adding default loket...</p>";

        $loket_data = [
            ['id' => 1, 'nama' => 'Loket 1', 'jenis' => 'teller'],
            ['id' => 2, 'nama' => 'Loket 2', 'jenis' => 'teller'],
            ['id' => 3, 'nama' => 'Loket 3', 'jenis' => 'teller'],
            ['id' => 4, 'nama' => 'Loket 4', 'jenis' => 'cs'],
            ['id' => 5, 'nama' => 'Loket 5', 'jenis' => 'cs'],
            ['id' => 6, 'nama' => 'Loket 6', 'jenis' => 'kredit'],
            ['id' => 7, 'nama' => 'Loket 7', 'jenis' => 'kredit']
        ];

        foreach ($loket_data as $loket) {
            $sql = "INSERT INTO loket (id, nama_loket, jenis_layanan, status)
                    VALUES ({$loket['id']}, '{$loket['nama']}', '{$loket['jenis']}', 'aktif')";

            if ($test_conn->query($sql)) {
                echo "<p>✅ Added loket: {$loket['nama']}</p>";
            } else {
                echo "<p>❌ Failed to add loket: {$loket['nama']} - " . $test_conn->error . "</p>";
            }
        }
    } else {
        echo "<p>ℹ️ Loket data already exists ({$row['count']} found)</p>";
    }

    // Add sample antrian data
    $result = $test_conn->query("SELECT COUNT(*) as count FROM antrian WHERE tanggal = CURDATE()");
    $row = $result->fetch_assoc();

    if ($row['count'] == 0) {
        echo "<p>Adding sample antrian data for today...</p>";

        $antrian_data = [
            ['nomor' => 'T001', 'nama' => 'Ahmad Fauzi', 'layanan' => 'teller', 'status' => 'selesai', 'loket_id' => 1],
            ['nomor' => 'C001', 'nama' => 'Siti Nurhaliza', 'layanan' => 'cs', 'status' => 'selesai', 'loket_id' => 4],
            ['nomor' => 'K001', 'nama' => 'Budi Santoso', 'layanan' => 'kredit', 'status' => 'selesai', 'loket_id' => 6],
            ['nomor' => 'T002', 'nama' => 'Dewi Lestari', 'layanan' => 'teller', 'status' => 'dipanggil', 'loket_id' => 1],
            ['nomor' => 'C002', 'nama' => 'Rudi Hartono', 'layanan' => 'cs', 'status' => 'menunggu', 'loket_id' => null]
        ];

        foreach ($antrian_data as $antrian) {
            $loket_part = $antrian['loket_id'] ? $antrian['loket_id'] : 'NULL';
            $sql = "INSERT INTO antrian (nomor_antrian, nama, layanan, jenis_layanan, tanggal, status, loket_id, waktu_booking)
                    VALUES ('{$antrian['nomor']}', '{$antrian['nama']}', '{$antrian['layanan']}', '{$antrian['layanan']}',
                            CURDATE(), '{$antrian['status']}', $loket_part, NOW())";

            if ($test_conn->query($sql)) {
                echo "<p>✅ Added antrian: {$antrian['nomor']} - {$antrian['nama']}</p>";
            } else {
                echo "<p>❌ Failed to add antrian: {$antrian['nomor']} - " . $test_conn->error . "</p>";
            }
        }
    } else {
        echo "<p>ℹ️ Antrian data already exists for today ({$row['count']} found)</p>";
    }

    echo "<h2>5. Verification</h2>";

    // Verify staff data
    $result = $test_conn->query("SELECT * FROM users WHERE role = 'staff'");
    if ($result && $result->num_rows > 0) {
        echo "<p>✅ Found " . $result->num_rows . " staff members in database</p>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Username</th><th>Nama Lengkap</th><th>Role</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . $row['username'] . "</td>";
            echo "<td>" . $row['nama_lengkap'] . "</td>";
            echo "<td>" . $row['role'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>❌ No staff found in database</p>";
    }

    $test_conn->close();

    echo "<h2>✅ Database Setup Complete!</h2>";
    echo "<p>Now the system should use real database instead of mock connection.</p>";
    echo "<p><a href='admin/petugas.php'>Go to Petugas Management</a></p>";

} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}
?>

<?php
// Nasabah page
$page_title = 'Menu Nasabah';
require_once 'includes/functions.php';
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Bank BJB</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">

    <style>
        body {
            background: linear-gradient(135deg, #e91e63 0%, #d81b60 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px 0;
        }
        .notification-bar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 10px 20px;
            text-align: center;
            z-index: 1000;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
        }
        .menu-container {
            background-color: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            width: 100%;
            max-width: 600px;
        }
        .menu-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .menu-header img {
            height: 80px;
            margin-bottom: 15px;
        }
        .menu-button {
            display: block;
            background: linear-gradient(135deg, #e91e63 0%, #d81b60 100%);
            color: white;
            border: none;
            border-radius: 50px;
            padding: 15px 20px;
            margin-bottom: 15px;
            text-align: center;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s;
            text-decoration: none;
        }
        .menu-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(233, 30, 99, 0.3);
            color: white;
            text-decoration: none;
        }
        .menu-button i {
            margin-right: 10px;
        }
        .back-button {
            display: inline-block;
            color: #e91e63;
            margin-top: 20px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s;
        }
        .back-button:hover {
            transform: translateX(-5px);
            text-decoration: none;
            color: #c2185b;
        }
        .welcome-alert {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            border-radius: 10px;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
        }
    </style>
</head>
<body>
    <!-- Notification Bar -->
    <div class="notification-bar">
        <i class="fas fa-info-circle"></i> Selamat Datang di Menu Nasabah Bank BJB
    </div>

    <!-- Main Content -->
    <div class="container">
        <?php if(isset($_SESSION['success_message'])): ?>
        <div class="alert welcome-alert alert-dismissible fade show mb-4" role="alert">
            <?php echo $_SESSION['success_message']; ?>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <?php unset($_SESSION['success_message']); ?>
        <?php endif; ?>

        <?php if(isset($_SESSION['error_message'])): ?>
        <div class="alert welcome-alert alert-dismissible fade show mb-4" role="alert">
            <?php echo $_SESSION['error_message']; ?>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <?php unset($_SESSION['error_message']); ?>
        <?php endif; ?>

        <div class="menu-container">
            <div class="menu-header">
                <img src="assets/img/logo.jpeg" alt="Bank BJB">
                <h2>Menu Nasabah</h2>
                <p class="text-muted">Silahkan pilih layanan yang Anda butuhkan</p>
            </div>

            <a href="customer/index.php" class="menu-button">
                <i class="fas fa-ticket-alt"></i> Ambil Nomor Antrian
            </a>
            
            <a href="customer/booking.php" class="menu-button">
                <i class="fas fa-calendar-check"></i> Booking dari Rumah
            </a>
            
            <a href="customer/check_status.php" class="menu-button">
                <i class="fas fa-search"></i> Cek Status Antrian
            </a>
            
            <a href="customer/register.php" class="menu-button">
                <i class="fas fa-user-plus"></i> Registrasi Akun
            </a>

            <div class="text-center">
                <a href="index.php" class="back-button">
                    <i class="fas fa-arrow-left"></i> Kembali ke Halaman Utama
                </a>
            </div>
        </div>
    </div>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
// Display index_mock.php tidak lagi menggunakan data mock/fake.
// Silakan gunakan display/index.php untuk tampilan antrian asli dari database.
echo '<div style="padding:40px;text-align:center;"><h2>File ini tidak lagi digunakan.<br><PERSON><PERSON><PERSON> aks<PERSON> <a href="index.php">Display Antrian</a> untuk data asli.</h2></div>';
exit;
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Bank BJB</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        /* Override footer styles for display page */
        footer {
            background-color: #000 !important;
            border-top: 1px solid #333 !important;
            box-shadow: none !important;
            height: 25px !important;
        }

        footer span {
            color: #888 !important;
            font-size: 10px !important;
        }

        footer .col-md-6.text-left {
            padding-left: 20px !important;
        }

        /* Add padding to container to prevent content from being hidden by footer */
        .container {
            padding-bottom: 30px;
        }

        /* Improved display styles for TV */
        body.xbx-display {
            background: linear-gradient(135deg, #9c27b0 0%, #673ab7 100%);
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow-x: hidden;
            margin: 0;
            padding: 0;
        }

        .xbx-header {
            background-color: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 10px 0;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .xbx-logo img {
            height: 50px;
            margin-left: 10px;
        }

        .xbx-time {
            text-align: right;
            padding-right: 15px;
        }

        #clock {
            font-size: 32px;
            font-weight: bold;
            color: #ffffff;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
            display: block;
        }

        .xbx-counter {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
            margin-bottom: 25px;
            border: 1px solid rgba(255,255,255,0.1);
        }

        .xbx-counter:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.4);
        }

        .xbx-counter-header {
            padding: 12px;
            text-align: center;
            font-size: 22px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .bg-loket1 { background: linear-gradient(135deg, #00bcd4 0%, #0097a7 100%); }
        .bg-loket2 { background: linear-gradient(135deg, #ffc107 0%, #ffa000 100%); }
        .bg-loket3 { background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%); }
        .bg-loket4 { background: linear-gradient(135deg, #e91e63 0%, #d81b60 100%); }

        .xbx-counter-body {
            padding: 20px;
            background-color: rgba(0, 0, 0, 0.5);
            text-align: center;
            border-top: 1px solid rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .xbx-counter-number {
            font-size: 80px;
            font-weight: bold;
            margin: 15px 0;
            color: #fff;
            text-shadow: 0 0 10px rgba(255,255,255,0.3);
        }

        .xbx-counter-customer {
            font-size: 22px;
            margin-bottom: 10px;
            color: #fff;
            background-color: rgba(255,255,255,0.2);
            padding: 8px;
            border-radius: 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .card.bg-dark {
            background-color: rgba(0, 0, 0, 0.5) !important;
            border: 1px solid rgba(255,255,255,0.1);
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .card-header {
            border-bottom: 1px solid rgba(255,255,255,0.1);
            padding: 15px;
        }

        .table-dark {
            background-color: transparent;
            color: #ddd;
        }

        .table-dark thead th {
            border-bottom: 2px solid rgba(255,255,255,0.1);
            text-transform: uppercase;
            font-size: 14px;
            letter-spacing: 1px;
            color: #999;
        }

        .table-dark td, .table-dark th {
            border-top: 1px solid rgba(255,255,255,0.05);
            padding: 12px 15px;
        }

        .badge {
            font-size: 18px;
            padding: 8px 15px;
            border-radius: 5px;
        }

        .video-container {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.4);
        }

        iframe {
            display: block;
        }

        /* Highlight animation */
        .highlight {
            animation: highlight 3s ease-in-out;
        }

        @keyframes highlight {
            0% { box-shadow: 0 5px 15px rgba(0,0,0,0.3); }
            25% { box-shadow: 0 5px 30px rgba(255,255,255,0.5); }
            50% { box-shadow: 0 5px 15px rgba(0,0,0,0.3); }
            75% { box-shadow: 0 5px 30px rgba(255,255,255,0.5); }
            100% { box-shadow: 0 5px 15px rgba(0,0,0,0.3); }
        }
    </style>
</head>
<body class="xbx-display">
    <div class="xbx-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="xbx-logo d-flex align-items-center">
                        <img src="../assets/img/logo.jpeg" alt="Bank BJB" class="mr-3">
                        <h2 class="mb-0" style="color: #fff; font-weight: 600; letter-spacing: 1px;">SISTEM ANTRIAN</h2>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="xbx-time text-right">
                        <span id="clock">22:40:39</span>
                        <div style="color: #ccc; font-size: 18px;"><?php echo tanggal_indonesia(); ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <div class="xbx-content">
            <div class="row">
                <div class="col-md-8">
                    <div class="row">
                        <?php foreach($counters as $counter): ?>
                        <div class="col-md-6 mb-4">
                            <div class="xbx-counter">
                                <div class="xbx-counter-header bg-loket<?php echo ($counter['id'] % 4) + 1; ?>">
                                    <i class="fas fa-user-tie mr-2"></i><?php echo $counter['nama_loket']; ?>
                                </div>
                                <div class="xbx-counter-body">
                                    <div class="xbx-counter-number" id="counter-<?php echo $counter['id']; ?>">
                                        <?php
                                        if ($counter['current_queue']) {
                                            $nomor_antrian = $counter['current_queue']['nomor_antrian'];
                                            // Cek apakah nomor antrian mengandung tanda '-'
                                            if (strpos($nomor_antrian, '-') !== false) {
                                                $parts = explode('-', $nomor_antrian);
                                                echo isset($parts[1]) ? $parts[1] : $nomor_antrian;
                                            } else {
                                                // Jika tidak ada tanda '-', tampilkan nomor antrian tanpa prefix
                                                echo preg_replace('/[^0-9]/', '', $nomor_antrian);
                                            }
                                        } else {
                                            echo '---';
                                        }
                                        ?>
                                    </div>
                                    <div class="xbx-counter-customer">
                                        <?php
                                        if ($counter['current_queue']) {
                                            echo $counter['current_queue']['nama'];
                                        } else {
                                            echo 'Belum ada antrian';
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card bg-dark text-white">
                                <div class="card-header" style="background: linear-gradient(135deg, #00bcd4 0%, #0097a7 100%);">
                                    <h5 class="mb-0"><i class="fas fa-list-ol mr-2"></i> Antrian Berikutnya</h5>
                                </div>
                                <div class="card-body">
                                    <?php if(count($next_queues) > 0): ?>
                                    <div class="table-responsive">
                                        <table class="table table-dark table-hover">
                                            <thead>
                                                <tr>
                                                    <th width="20%">Nomor</th>
                                                    <th width="40%">Nama</th>
                                                    <th width="40%">Layanan</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach($next_queues as $queue): ?>
                                                <tr>
                                                    <td>
                                                        <span class="badge" style="background: linear-gradient(135deg, #00bcd4 0%, #0097a7 100%);">
                                                            <?php
                                                                $nomor_antrian = $queue['nomor_antrian'];
                                                                // Cek apakah nomor antrian mengandung tanda '-'
                                                                if (strpos($nomor_antrian, '-') !== false) {
                                                                    $parts = explode('-', $nomor_antrian);
                                                                    echo isset($parts[1]) ? $parts[1] : $nomor_antrian;
                                                                } else {
                                                                    // Jika tidak ada tanda '-', tampilkan nomor antrian tanpa prefix
                                                                    echo preg_replace('/[^0-9]/', '', $nomor_antrian);
                                                                }
                                                            ?>
                                                        </span>
                                                    </td>
                                                    <td><?php echo $queue['nama']; ?></td>
                                                    <td><i class="fas fa-tag mr-1"></i> <?php echo $queue['nama_poli']; ?></td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                    <?php else: ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-info-circle fa-3x mb-3" style="color: #2c7873;"></i>
                                        <h5>Tidak ada antrian yang menunggu</h5>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card bg-dark text-white h-100">
                        <div class="card-body">
                            <div class="video-container mb-4">
                                <iframe width="100%" height="250" src="https://www.youtube.com/embed/QkPv-tkRflI" frameborder="0" allow="autoplay; encrypted-media" allowfullscreen></iframe>
                            </div>

                            <div class="card text-white mb-4" style="background: linear-gradient(135deg, #00bcd4 0%, #0097a7 100%); border-radius: 10px;">
                                <div class="card-body text-center py-4">
                                    <i class="fas fa-university fa-3x mb-3"></i>
                                    <h4 style="font-weight: 600;">Selamat Datang di Bank BJB</h4>
                                    <p class="mb-0" style="font-size: 16px;">Terima kasih telah menggunakan layanan kami</p>
                                </div>
                            </div>

                            <div class="text-center mt-4">
                                <img src="../assets/img/logo.jpeg" alt="Bank BJB" class="img-fluid" style="max-width: 180px;">
                                <p class="mt-3" style="color: #999; font-size: 14px;">Melayani dengan sepenuh hati</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        $(document).ready(function() {
            // Update clock with animation
            function updateClock() {
                var now = new Date();
                var hours = now.getHours();
                var minutes = now.getMinutes();
                var seconds = now.getSeconds();

                // Add leading zeros
                hours = (hours < 10) ? "0" + hours : hours;
                minutes = (minutes < 10) ? "0" + minutes : minutes;
                seconds = (seconds < 10) ? "0" + seconds : seconds;

                // Display the time with animation
                var timeString = hours + ":" + minutes + ":" + seconds;
                if ($('#clock').text() !== timeString) {
                    $('#clock').fadeOut(200, function() {
                        $(this).text(timeString).fadeIn(200);
                    });
                }

                // Update every second
                setTimeout(updateClock, 1000);
            }

            updateClock();

            // Mock data for refresh display
            function refreshDisplay() {
                // Simulate AJAX response with mock data
                var mockData = {
                    counters: [
                        { id: 1, number: '001', customer: 'Ahmad Fauzi' },
                        { id: 2, number: '---', customer: 'Belum ada antrian' },
                        { id: 3, number: '---', customer: 'Belum ada antrian' },
                        { id: 4, number: '001', customer: 'Siti Nurhaliza' },
                        { id: 6, number: '001', customer: 'Budi Santoso' }
                    ],
                    next_queues: [
                        { number: '002', name: 'Dewi Lestari', service: 'Teller' },
                        { number: '002', name: 'Rudi Hartono', service: 'Customer Service' },
                        { number: '002', name: 'Andi Wijaya', service: 'Kredit' }
                    ],
                    play_sound: false
                };

                // Randomly change one counter to simulate real-time updates
                if (Math.random() > 0.7) {
                    var randomCounter = Math.floor(Math.random() * mockData.counters.length);
                    if (mockData.counters[randomCounter].number === '---') {
                        mockData.counters[randomCounter].number = '001';
                        mockData.counters[randomCounter].customer = 'Nasabah Baru';
                        mockData.play_sound = true;
                    } else {
                        var newNumber = parseInt(mockData.counters[randomCounter].number) + 1;
                        mockData.counters[randomCounter].number = String(newNumber).padStart(3, '0');
                        mockData.counters[randomCounter].customer = 'Nasabah ' + newNumber;
                        mockData.play_sound = true;
                    }
                }

                // Update counters with animation
                $.each(mockData.counters, function(index, counter) {
                    var counterElement = $('#counter-' + counter.id);
                    var customerElement = counterElement.next('.xbx-counter-customer');

                    // Only animate if the value has changed
                    if (counterElement.text() !== counter.number) {
                        // Store the counter for highlighting
                        var counterBox = counterElement.closest('.xbx-counter');

                        // Flash effect before changing
                        counterBox.css('background-color', 'rgba(44, 120, 115, 0.3)');

                        // Animate number change with scale effect
                        counterElement.css('transform', 'scale(1)');
                        counterElement.animate({opacity: 0}, 200, function() {
                            $(this).text(counter.number);
                            $(this).animate({opacity: 1}, 200);
                            $(this).css('transform', 'scale(1.1)');
                            setTimeout(function() {
                                counterElement.css('transform', 'scale(1)');
                            }, 300);
                        });

                        // Reset background after delay
                        setTimeout(function() {
                            counterBox.css('background-color', '');
                        }, 1000);

                        // Add highlight effect
                        counterBox.addClass('highlight');
                        setTimeout(function() {
                            counterBox.removeClass('highlight');
                        }, 3000);
                    }

                    // Animate customer name change if needed
                    if (customerElement.text() !== counter.customer) {
                        customerElement.fadeOut(200, function() {
                            $(this).text(counter.customer).fadeIn(200);
                        });
                    }
                });

                // Play notification sound if needed
                if (mockData.play_sound) {
                    playNotificationWithVisualEffect();
                }

                // Refresh every 5 seconds for demo
                setTimeout(refreshDisplay, 5000);
            }

            // Function to play notification with enhanced visual effect
            function playNotificationWithVisualEffect() {
                // Play bell sound
                var audio = new Audio('../assets/sounds/bell.mp3');
                audio.play();

                // Add a notification message
                $('<div class="notification-message"></div>')
                    .css({
                        'position': 'fixed',
                        'top': '20%',
                        'left': '50%',
                        'transform': 'translateX(-50%)',
                        'background-color': 'rgba(0, 188, 212, 0.9)',
                        'color': 'white',
                        'padding': '15px 30px',
                        'border-radius': '10px',
                        'font-size': '24px',
                        'font-weight': 'bold',
                        'box-shadow': '0 5px 15px rgba(0,0,0,0.3)',
                        'z-index': 10000,
                        'opacity': 0,
                        'text-align': 'center'
                    })
                    .html('<i class="fas fa-bell mr-2"></i> Nomor Antrian Baru Dipanggil')
                    .appendTo('body')
                    .animate({opacity: 1, top: '15%'}, 500)
                    .delay(1500)
                    .animate({opacity: 0, top: '10%'}, 500, function() {
                        $(this).remove();
                    });
            }

            // Start auto refresh after a short delay
            setTimeout(refreshDisplay, 2000);
        });
    </script>
</body>
</html>
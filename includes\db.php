<?php
/**
 * Database Connection
 *
 * File untuk koneksi database yang digunakan oleh modul nasabah
 */

// Include database configuration
require_once __DIR__ . '/../config/database.php';

// Fungsi ini sudah didefinisikan di database.php, tapi kita definisikan ulang untuk memastikan kompatibilitas
if (!function_exists('sanitize_input')) {
    function sanitize_input($data) {
        return sanitize($data);
    }
}

// Fungsi untuk mengecek apakah email sudah terdaftar
function is_email_registered($email) {
    global $conn;
    $email = sanitize($email);
    $sql = "SELECT id FROM nasabah WHERE email = '$email'";
    $result = query($sql);
    return num_rows($result) > 0;
}

// Fungsi untuk mengecek apakah KTP sudah terdaftar
function is_ktp_registered($no_ktp) {
    global $conn;
    $no_ktp = sanitize($no_ktp);
    $sql = "SELECT id FROM nasabah WHERE no_identitas = '$no_ktp' AND jenis_identitas = 'ktp'";
    $result = query($sql);
    return num_rows($result) > 0;
}

// Catatan: Fungsi get_nasabah_by_email(), get_nasabah_by_id(), dan get_booking_by_id()
// telah dipindahkan ke functions.php untuk menghindari deklarasi ganda

// Catatan: Fungsi get_bookings_by_nasabah_id() telah dipindahkan ke functions.php dengan nama get_bookings_by_nasabah_id()

// Fungsi untuk mengecek apakah nasabah sudah login
function is_nasabah_logged_in() {
    return isset($_SESSION['user_id']) && isset($_SESSION['user_role']) && $_SESSION['user_role'] == 'nasabah';
}

<?php
// Online booking page with ID verification
$page_title = 'Booking Antrian Online';
require_once '../includes/functions.php';

// Check if IP is blocked
$ip_address = $_SERVER['REMOTE_ADDR'];
if (is_ip_blocked($ip_address)) {
    $_SESSION['error_message'] = 'Ma<PERSON>, akses Anda telah diblokir. Silahkan hubungi petugas untuk informasi lebih lanjut.';
    redirect('../index.php');
}

// Check if current time is within operational hours
if (!is_operational_hours()) {
    // Get operational hours from settings
    $jam_operasional = get_setting('jam_operasional');
    $jam = explode('-', $jam_operasional);
    $jam_buka = isset($jam[0]) ? trim($jam[0]) : '08:00';
    $jam_tutup = isset($jam[1]) ? trim($jam[1]) : '16:00';

    $_SESSION['error_message'] = "Maaf, layanan booking online hanya tersedia pada jam operasional bank ($jam_buka - $jam_tutup). Silahkan kembali pada jam operasional.";
    redirect('../index.php');
}

// Verifikasi wajib untuk booking online
$verifikasi_wajib = 1; // Selalu wajib untuk booking online

// Get available service types
$sql = "SELECT * FROM poli WHERE status = 'aktif' ORDER BY nama_poli";
$result = query($sql);
$poli_list = fetch_all($result);

// Process form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $nama = sanitize($_POST['nama']);
    $no_identitas = sanitize($_POST['no_identitas']);
    $jenis_identitas = sanitize($_POST['jenis_identitas']);
    $no_hp = sanitize($_POST['no_hp']);
    $email = sanitize($_POST['email']);
    $poli_id = (int)$_POST['poli_id'];
    $tanggal_booking = sanitize($_POST['tanggal_booking']);
    $booking_dari = 'online';

    // Validate input
    if (empty($nama) || empty($no_identitas) || empty($jenis_identitas) ||
        empty($no_hp) || empty($poli_id) || empty($tanggal_booking)) {
        $_SESSION['error_message'] = 'Semua field harus diisi.';
    }
    // Validate ID number format
    elseif ($jenis_identitas == 'ktp' && !preg_match('/^[0-9]{16}$/', $no_identitas)) {
        $_SESSION['error_message'] = 'Nomor KTP harus berisi 16 digit angka.';
    }
    elseif ($jenis_identitas == 'buku_tabungan' && !preg_match('/^[0-9]{10,20}$/', $no_identitas)) {
        $_SESSION['error_message'] = 'Nomor Buku Tabungan harus berisi 10-20 digit angka.';
    }
    // Validate phone number
    elseif (!preg_match('/^[0-9]{10,15}$/', $no_hp)) {
        $_SESSION['error_message'] = 'Nomor telepon harus berisi 10-15 digit angka.';
    } else {
        // Validate booking date (must be today or future date)
        $today = date('Y-m-d');
        if ($tanggal_booking < $today) {
            $_SESSION['error_message'] = 'Tanggal booking tidak valid. Silahkan pilih tanggal hari ini atau yang akan datang.';
        } else {
            // Check if poli exists
            $sql = "SELECT * FROM poli WHERE id = $poli_id AND status = 'aktif'";
            $result = query($sql);

            if (num_rows($result) == 0) {
                $_SESSION['error_message'] = 'Layanan yang dipilih tidak valid.';
            } else {
                $poli = fetch_assoc($result);

                // Check if max queue limit is reached for the booking date
                $sql = "SELECT COUNT(*) as total FROM antrian WHERE tanggal = '$tanggal_booking' AND poli_id = $poli_id";
                $result = query($sql);
                $row = fetch_assoc($result);

                $max_antrian = (int)get_setting('max_antrian');

                if ($row['total'] >= $max_antrian) {
                    $_SESSION['error_message'] = 'Maaf, kuota antrian untuk layanan ini pada tanggal tersebut telah penuh. Silahkan pilih tanggal lain.';
                } else {
                    // Handle file upload for ID verification
                    $file_identitas = '';
                    $upload_error = false;

                    if ($verifikasi_wajib) {
                        if (!isset($_FILES['file_identitas']) || $_FILES['file_identitas']['error'] != 0) {
                            $_SESSION['error_message'] = 'Anda harus mengunggah foto identitas.';
                            $upload_error = true;
                        } else {
                            $allowed_types = ['image/jpeg', 'image/png', 'image/jpg'];
                            $max_size = 2 * 1024 * 1024; // 2MB

                            if (!in_array($_FILES['file_identitas']['type'], $allowed_types)) {
                                $_SESSION['error_message'] = 'Format file tidak didukung. Gunakan format JPG atau PNG.';
                                $upload_error = true;
                            } elseif ($_FILES['file_identitas']['size'] > $max_size) {
                                $_SESSION['error_message'] = 'Ukuran file terlalu besar. Maksimal 2MB.';
                                $upload_error = true;
                            } else {
                                $upload_dir = '../uploads/identitas/';

                                // Create directory if not exists
                                if (!file_exists($upload_dir)) {
                                    mkdir($upload_dir, 0777, true);
                                }

                                $file_name = time() . '_' . $_FILES['file_identitas']['name'];
                                $file_path = $upload_dir . $file_name;

                                if (move_uploaded_file($_FILES['file_identitas']['tmp_name'], $file_path)) {
                                    $file_identitas = $file_name;
                                } else {
                                    $_SESSION['error_message'] = 'Gagal mengunggah file. Silahkan coba lagi.';
                                    $upload_error = true;
                                }
                            }
                        }
                    }

                    if (!$upload_error) {
                        // Check if customer already exists
                        $sql = "SELECT * FROM nasabah WHERE no_identitas = '$no_identitas'";
                        $result = query($sql);

                        if (num_rows($result) > 0) {
                            // Customer exists, check if blocked
                            $nasabah = fetch_assoc($result);
                            $nasabah_id = $nasabah['id'];

                            if ($nasabah['is_blocked'] == 1) {
                                $_SESSION['error_message'] = 'Maaf, akun Anda telah diblokir. Silahkan hubungi petugas untuk informasi lebih lanjut.';
                                redirect('booking.php');
                            } else {
                                // Update customer data
                                $sql = "UPDATE nasabah SET
                                        nama = '$nama',
                                        jenis_identitas = '$jenis_identitas',
                                        file_identitas = '$file_identitas',
                                        no_hp = '$no_hp',
                                        email = '$email',
                                        status_verifikasi = 'belum',
                                        updated_at = NOW()
                                        WHERE id = $nasabah_id";
                                query($sql);
                            }
                        } else {
                            // Create new customer
                            $sql = "INSERT INTO nasabah (nama, no_identitas, jenis_identitas, file_identitas, no_hp, email, status_verifikasi)
                                    VALUES ('$nama', '$no_identitas', '$jenis_identitas', '$file_identitas', '$no_hp', '$email', 'belum')";
                            query($sql);
                            $nasabah_id = last_id();
                        }

                        // Generate queue number
                        $nomor_antrian = generate_queue_number($poli_id, $tanggal_booking);

                        // Calculate estimated waiting time
                        $estimasi_waktu = calculate_estimated_time($poli_id, $tanggal_booking);

                        // Create queue record
                        $sql = "INSERT INTO antrian (nomor_antrian, tanggal, nasabah_id, poli_id, status, estimasi_waktu, booking_dari)
                                VALUES ('$nomor_antrian', '$tanggal_booking', $nasabah_id, $poli_id, 'menunggu', $estimasi_waktu, '$booking_dari')";
                        query($sql);
                        $antrian_id = last_id();

                        // Set session for booking confirmation
                        $_SESSION['booking'] = [
                            'id' => $antrian_id,
                            'nomor' => $nomor_antrian,
                            'nama' => $nama,
                            'poli' => $poli['nama_poli'],
                            'estimasi' => $estimasi_waktu,
                            'tanggal' => $tanggal_booking,
                            'verifikasi' => $verifikasi_wajib ? 'belum' : 'terverifikasi'
                        ];

                        redirect('confirmation.php');
                    }
                }
            }
        }
    }
}

// Get minimum date (today)
$min_date = date('Y-m-d');

// Get maximum date (7 days from today)
$max_date = date('Y-m-d', strtotime('+7 days'));
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Bank BJB</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">

    <style>
        body {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            min-height: 100vh;
            padding: 50px 0;
        }
        .booking-form-container {
            background-color: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
        .logo-container {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo-container img {
            height: 80px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="booking-form-container">
                    <div class="logo-container">
                        <img src="../assets/img/logo.jpeg" alt="Bank BJB">
                        <h2>Booking Antrian Online</h2>
                        <p class="text-muted">Silahkan isi form berikut untuk melakukan booking antrian dari rumah.</p>
                    </div>

                    <?php if(isset($_SESSION['error_message'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $_SESSION['error_message']; ?>
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <?php unset($_SESSION['error_message']); ?>
                    <?php endif; ?>

                    <form action="booking.php" method="post" enctype="multipart/form-data">
                        <div class="form-group">
                            <label for="nama">Nama Lengkap</label>
                            <input type="text" class="form-control" id="nama" name="nama" required>
                        </div>

                        <div class="form-group">
                            <label for="no_identitas">Nomor Identitas (KTP/Buku Tabungan)</label>
                            <input type="text" class="form-control" id="no_identitas" name="no_identitas" required>
                        </div>

                        <div class="form-group">
                            <label for="jenis_identitas">Jenis Identitas</label>
                            <select class="form-control" id="jenis_identitas" name="jenis_identitas" required>
                                <option value="">-- Pilih Jenis Identitas --</option>
                                <option value="ktp">KTP</option>
                                <option value="buku_tabungan">Buku Tabungan</option>
                            </select>
                        </div>

                        <?php if($verifikasi_wajib): ?>
                        <div class="form-group">
                            <label for="file_identitas">Foto Identitas (KTP/Buku Tabungan)</label>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input" id="file_identitas" name="file_identitas" required>
                                <label class="custom-file-label" for="file_identitas">Pilih file...</label>
                            </div>
                            <small class="form-text text-muted">Format: JPG/PNG, Maks: 2MB</small>
                            <img id="imagePreview" src="#" alt="Preview" style="max-width: 100%; margin-top: 10px; display: none;">
                        </div>
                        <?php endif; ?>

                        <div class="form-group">
                            <label for="no_hp">Nomor HP</label>
                            <input type="text" class="form-control" id="no_hp" name="no_hp" required>
                        </div>

                        <div class="form-group">
                            <label for="email">Email (Opsional)</label>
                            <input type="email" class="form-control" id="email" name="email">
                        </div>

                        <div class="form-group">
                            <label for="poli_id">Layanan</label>
                            <select class="form-control" id="poli_id" name="poli_id" required>
                                <option value="">-- Pilih Layanan --</option>
                                <?php foreach($poli_list as $poli): ?>
                                <option value="<?php echo $poli['id']; ?>"><?php echo $poli['nama_poli']; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="tanggal_booking">Tanggal Kunjungan</label>
                            <input type="date" class="form-control" id="tanggal_booking" name="tanggal_booking"
                                   min="<?php echo $min_date; ?>" max="<?php echo $max_date; ?>" required>
                        </div>

                        <div class="form-group">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <?php if($verifikasi_wajib): ?>
                                Booking Anda akan diverifikasi oleh petugas. Silahkan cek status booking secara berkala.
                                <?php else: ?>
                                Booking Anda akan langsung dikonfirmasi. Silahkan datang pada tanggal yang telah ditentukan.
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="form-group text-center">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-calendar-check"></i> Booking Antrian
                            </button>
                        </div>
                    </form>

                    <div class="text-center mt-3">
                        <a href="../index.php" class="btn btn-link">
                            <i class="fas fa-arrow-left"></i> Kembali ke Halaman Utama
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        $(document).ready(function() {
            // File input preview
            $('.custom-file-input').on('change', function() {
                var fileName = $(this).val().split('\\').pop();
                $(this).next('.custom-file-label').html(fileName);

                // Preview image
                if (this.files && this.files[0]) {
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        $('#imagePreview').attr('src', e.target.result).show();
                    }
                    reader.readAsDataURL(this.files[0]);
                }
            });
        });
    </script>
</body>
</html>

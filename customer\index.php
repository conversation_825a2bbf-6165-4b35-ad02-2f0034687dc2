<?php
// Customer queue creation page
$page_title = '<PERSON>bil <PERSON> Antrian';
require_once '../includes/functions.php';

// Check if IP is blocked
$ip_address = $_SERVER['REMOTE_ADDR'];
if (is_ip_blocked($ip_address)) {
    $_SESSION['error_message'] = 'Ma<PERSON>, akses Anda telah diblokir. Silahkan hubungi petugas untuk informasi lebih lanjut.';
    redirect('../index.php');
}

// Get available service types
$sql = "SELECT * FROM poli WHERE status = 'aktif' ORDER BY nama_poli";
$result = query($sql);
$poli_list = fetch_all($result);

// Process form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $nama = sanitize($_POST['nama']);
    $poli_id = (int)$_POST['poli_id'];
    $booking_dari = 'lokal';

    // Validate input
    if (empty($nama) || empty($poli_id)) {
        $_SESSION['error_message'] = 'Semua field harus diisi.';
    } else {
        // Check if poli exists
        $sql = "SELECT * FROM poli WHERE id = $poli_id AND status = 'aktif'";
        $result = query($sql);

        if (num_rows($result) == 0) {
            $_SESSION['error_message'] = 'Layanan yang dipilih tidak valid.';
        } else {
            $poli = fetch_assoc($result);

            // Check if max queue limit is reached
            $today = date('Y-m-d');
            $sql = "SELECT COUNT(*) as total FROM antrian WHERE tanggal = '$today' AND poli_id = $poli_id";
            $result = query($sql);
            $row = fetch_assoc($result);

            $max_antrian = (int)get_setting('max_antrian');

            if ($row['total'] >= $max_antrian) {
                $_SESSION['error_message'] = 'Maaf, kuota antrian untuk layanan ini telah penuh. Silahkan coba kembali besok.';
            } else {
                // Create customer record
                $sql = "INSERT INTO nasabah (nama, no_identitas, jenis_identitas, status_verifikasi)
                        VALUES ('$nama', '-', 'ktp', 'terverifikasi')";
                query($sql);
                $nasabah_id = last_id();

                // Generate queue number
                $nomor_antrian = generate_queue_number($poli_id);

                // Calculate estimated waiting time
                $estimasi_waktu = calculate_estimated_time($poli_id);

                // Create queue record
                $sql = "INSERT INTO antrian (nomor_antrian, tanggal, nasabah_id, poli_id, status, estimasi_waktu, booking_dari)
                        VALUES ('$nomor_antrian', '$today', $nasabah_id, $poli_id, 'menunggu', $estimasi_waktu, '$booking_dari')";
                query($sql);
                $antrian_id = last_id();

                // Set session for ticket display
                $_SESSION['ticket'] = [
                    'id' => $antrian_id,
                    'nomor' => $nomor_antrian,
                    'nama' => $nama,
                    'poli' => $poli['nama_poli'],
                    'estimasi' => $estimasi_waktu,
                    'tanggal' => $today
                ];

                redirect('ticket.php');
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - XBX TEAM</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">

    <style>
        body {
            background: linear-gradient(135deg, #9c27b0 0%, #673ab7 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .xbx-queue-container {
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        .btn-primary {
            background: linear-gradient(135deg, #e91e63 0%, #d81b60 100%);
            border: none;
            box-shadow: 0 4px 6px rgba(233, 30, 99, 0.2);
            padding: 10px 20px;
            border-radius: 50px;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #d81b60 0%, #c2185b 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(233, 30, 99, 0.3);
        }
        .btn-outline-secondary {
            color: #673ab7;
            border-color: #673ab7;
            border-radius: 50px;
        }
        .btn-outline-secondary:hover {
            background-color: #673ab7;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="xbx-queue-container">
                    <div class="xbx-queue-header text-center">
                        <img src="../assets/img/logo.jpeg" alt="Bank BJB" style="max-width: 200px;">
                        <h3>Bank BJB</h3>
                        <p>Silahkan isi data untuk mendapatkan nomor antrian.</p>
                    </div>

                    <?php if(isset($_SESSION['error_message'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $_SESSION['error_message']; ?>
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <?php unset($_SESSION['error_message']); ?>
                    <?php endif; ?>

                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-ticket-alt mr-2"></i> Ambil Nomor Antrian</h5>
                        </div>
                        <div class="card-body">
                            <form action="index.php" method="post" class="xbx-queue-form">
                                <div class="form-group">
                                    <label for="nama"><i class="fas fa-user mr-2"></i>Masukkan Nama Anda</label>
                                    <input type="text" class="form-control" id="nama" name="nama" required>
                                </div>

                                <div class="form-group">
                                    <label for="poli_id"><i class="fas fa-building mr-2"></i>Pilih Layanan</label>
                                    <select class="form-control" id="poli_id" name="poli_id" required>
                                        <option value="">-- Pilih Layanan --</option>
                                        <?php foreach($poli_list as $poli): ?>
                                        <option value="<?php echo $poli['id']; ?>"><?php echo $poli['nama_poli']; ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <button type="submit" class="btn btn-primary btn-block">
                                        <i class="fas fa-ticket-alt mr-2"></i> Ambil Nomor Antrian
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0"><i class="fas fa-user-plus mr-2"></i> Registrasi Akun</h5>
                        </div>
                        <div class="card-body">
                            <p>Belum terdaftar? Silahkan registrasi terlebih dahulu.</p>
                            <a href="register.php" class="btn btn-info btn-block">
                                <i class="fas fa-user-plus mr-2"></i> Registrasi Akun
                            </a>
                        </div>
                    </div>

                    <div class="xbx-queue-number">
                        <h4>Antrian Saat Ini</h4>
                        <?php
                        // Get current queue numbers for each service
                        $today = date('Y-m-d');
                        $current_numbers = [];

                        foreach($poli_list as $poli) {
                            $sql = "SELECT MAX(CAST(SUBSTRING(nomor_antrian, 4) AS UNSIGNED)) as last_number
                                    FROM antrian
                                    WHERE poli_id = {$poli['id']} AND tanggal = '$today'";
                            $result = query($sql);
                            $row = fetch_assoc($result);
                            $last_number = $row['last_number'] ? $row['last_number'] : 0;
                            $current_numbers[$poli['id']] = $last_number;
                        }

                        // Get total waiting queues
                        $sql = "SELECT COUNT(*) as total FROM antrian WHERE status = 'menunggu' AND tanggal = '$today'";
                        $result = query($sql);
                        $row = fetch_assoc($result);
                        $total_waiting = $row['total'];
                        ?>

                        <div class="number"><?php echo $total_waiting; ?></div>
                        <p class="text-muted">Total antrian yang sedang menunggu</p>

                        <div class="mt-3">
                            <?php foreach($poli_list as $poli): ?>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span><?php echo $poli['nama_poli']; ?>:</span>
                                <span class="badge badge-primary"><?php echo $current_numbers[$poli['id']]; ?></span>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <div class="text-center mt-3">
                        <a href="../index.php" class="btn btn-link" style="color: #1a6a83;">
                            <i class="fas fa-arrow-left"></i> Kembali ke Halaman Utama
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
// Booking process for nasabah
require_once '../includes/functions.php';
require_once '../includes/db.php';

// Fungsi untuk logging error
function log_error($message) {
    error_log("[BOOKING ERROR] " . date('Y-m-d H:i:s') . " - " . $message);
}

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Debug session untuk membantu troubleshooting
log_error("Session data: " . print_r($_SESSION, true));

// Check if user is logged in as nasabah
if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'nasabah') {
    // Fix: Allow using nasabah_id instead of user_id for backward compatibility
    if (isset($_SESSION['nasabah_id'])) {
        // Set user_id and user_role from nasabah_id for compatibility
        $_SESSION['user_id'] = $_SESSION['nasabah_id'];
        $_SESSION['user_role'] = 'nasabah';
        log_error("Session fixed: user_id and user_role set from nasabah_id");
    } else {
        log_error("Authentication failed: user not logged in");
        $_SESSION['error_messages'] = ["Anda harus login terlebih dahulu untuk melakukan booking antrian."];
        header("Location: login.php");
        exit;
    }
}

// Verifikasi koneksi database
if (!$conn || $conn->connect_error) {
    log_error("Database connection failed: " . ($conn ? $conn->connect_error : "Connection object is null"));
    $_SESSION['error_messages'] = ["Terjadi kesalahan pada sistem. Silakan coba lagi nanti."];
    header("Location: index.php");
    exit;
}

// Check if current time is within operational hours
if (!is_operational_hours()) {
    // Get operational hours from settings
    $jam_operasional = get_setting('jam_operasional');
    $jam = explode('-', $jam_operasional);
    $jam_buka = isset($jam[0]) ? trim($jam[0]) : '08:00';
    $jam_tutup = isset($jam[1]) ? trim($jam[1]) : '16:00';

    log_error("Booking attempted outside operational hours: " . date('H:i:s'));
    $_SESSION['error_messages'] = ["Maaf, layanan booking online hanya tersedia pada jam operasional bank ($jam_buka - $jam_tutup). Silahkan kembali pada jam operasional."];
    header("Location: index.php");
    exit;
}

// Check if form is submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Log request data untuk debugging
        log_error("POST data received: " . print_r($_POST, true));
        log_error("FILE data received: " . print_r($_FILES, true));
        
        // Get form data
        $nama = isset($_POST['nama']) ? trim($_POST['nama']) : '';
        $no_identitas = isset($_POST['no_identitas']) ? trim($_POST['no_identitas']) : '';
        $email = isset($_POST['email']) ? trim($_POST['email']) : '';
        $no_hp = isset($_POST['no_hp']) ? trim($_POST['no_hp']) : '';
        $layanan = isset($_POST['layanan']) ? trim($_POST['layanan']) : '';
        $tanggal = isset($_POST['tanggal']) ? trim($_POST['tanggal']) : '';
        $waktu = isset($_POST['waktu']) ? trim($_POST['waktu']) : '';
        $keterangan = isset($_POST['keterangan']) ? trim($_POST['keterangan']) : '';

        // Validate required fields
        $errors = [];

        if (empty($nama)) {
            $errors[] = "Nama lengkap harus diisi";
        }

        if (empty($no_identitas)) {
            $errors[] = "Nomor Identitas harus diisi";
        } elseif (strlen($no_identitas) < 10 || !is_numeric($no_identitas)) {
            $errors[] = "Nomor Identitas harus minimal 10 digit angka";
        }

        if (empty($email)) {
            $errors[] = "Email harus diisi";
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = "Format email tidak valid";
        }

        if (empty($no_hp)) {
            $errors[] = "Nomor HP harus diisi";
        } elseif (!preg_match('/^[0-9]{10,13}$/', $no_hp)) {
            $errors[] = "Nomor HP harus 10-13 digit angka";
        }

        if (empty($layanan)) {
            $errors[] = "Layanan harus dipilih";
        }

        if (empty($tanggal)) {
            $errors[] = "Tanggal kunjungan harus diisi";
        } elseif (strtotime($tanggal) < strtotime(date('Y-m-d'))) {
            $errors[] = "Tanggal kunjungan tidak boleh kurang dari hari ini";
        }

        if (empty($waktu)) {
            $errors[] = "Waktu kunjungan harus dipilih";
        }

        // Check if file is uploaded
        if (!isset($_FILES['file_upload']) || $_FILES['file_upload']['error'] === UPLOAD_ERR_NO_FILE) {
            $errors[] = "File KTP/Buku Tabungan harus diunggah";
        } else {
            $file = $_FILES['file_upload'];
            $file_name = $file['name'];
            $file_tmp = $file['tmp_name'];
            $file_size = $file['size'];
            $file_error = $file['error'];

            // Get file extension
            $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

            // Allowed extensions
            $allowed_ext = ['jpg', 'jpeg', 'png', 'pdf'];

            if ($file_error !== UPLOAD_ERR_OK) {
                $errors[] = "Terjadi kesalahan saat mengunggah file (kode: " . $file_error . ")";
                log_error("File upload error code: " . $file_error);
            } elseif (!in_array($file_ext, $allowed_ext)) {
                $errors[] = "Format file tidak diizinkan. Format yang diizinkan: JPG, PNG, PDF";
            } elseif ($file_size > 2097152) { // 2MB in bytes
                $errors[] = "Ukuran file terlalu besar. Maksimal 2MB";
            }
        }

        // If there are errors, redirect back with error messages
        if (!empty($errors)) {
            log_error("Validation errors: " . implode(", ", $errors));
            $_SESSION['error_messages'] = $errors;
            $_SESSION['form_data'] = $_POST; // Save form data for repopulating the form
            header("Location: booking.php");
            exit;
        }

        // Process file upload
        $upload_dir = '../uploads/';
        if (!file_exists($upload_dir)) {
            if (!mkdir($upload_dir, 0777, true)) {
                log_error("Failed to create upload directory: " . $upload_dir);
                $_SESSION['error_messages'] = ["Gagal membuat direktori upload. Silakan hubungi administrator."];
                $_SESSION['form_data'] = $_POST;
                header("Location: booking.php");
                exit;
            }
        }

        $new_file_name = uniqid('doc_') . '.' . $file_ext;
        $upload_path = $upload_dir . $new_file_name;

        if (!move_uploaded_file($file_tmp, $upload_path)) {
            log_error("Failed to move uploaded file to: " . $upload_path);
            $_SESSION['error_messages'] = ["Gagal mengunggah file. Silakan coba lagi."];
            $_SESSION['form_data'] = $_POST;
            header("Location: booking.php");
            exit;
        }

        // Generate booking ID
        $booking_id = 'BK' . date('Ymd') . rand(1000, 9999);

        // Generate queue number based on service type
        $prefix = '';
        switch ($layanan) {
            case 'teller':
                $prefix = 'T';
                break;
            case 'cs':
                $prefix = 'C';
                break;
            case 'kredit':
                $prefix = 'K';
                break;
            default:
                $prefix = 'X'; // Fallback prefix
        }

        // Get the next queue number for the selected date and service
        try {
            $stmt = $conn->prepare("SELECT MAX(CAST(SUBSTRING_INDEX(nomor_antrian, '-', -1) AS UNSIGNED)) as last_number FROM bookings WHERE tanggal = ? AND layanan = ?");
            if (!$stmt) {
                throw new Exception("Prepare statement error: " . $conn->error);
            }
            
            $stmt->bind_param("ss", $tanggal, $layanan);
            if (!$stmt->execute()) {
                throw new Exception("Execute statement error: " . $stmt->error);
            }
            
            $result = $stmt->get_result();
            $row = $result->fetch_assoc();

            $last_number = isset($row['last_number']) && is_numeric($row['last_number']) ? (int)$row['last_number'] : 0;
            $next_number = $last_number + 1;
            
            // Format nomor antrian
            $queue_number = $prefix . str_pad($next_number, 3, '0', STR_PAD_LEFT);
            
            log_error("Generated queue number: " . $queue_number);
        } catch (Exception $e) {
            log_error("Error generating queue number: " . $e->getMessage());
            // Buat nomor antrian default jika query gagal
            $queue_number = $prefix . str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT);
            log_error("Using fallback queue number: " . $queue_number);
        }

        // Get nasabah_id from session (use either user_id or nasabah_id)
        $nasabah_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : $_SESSION['nasabah_id'];
        
        // Verifikasi nasabah_id valid
        if (!is_numeric($nasabah_id) || $nasabah_id <= 0) {
            log_error("Invalid nasabah_id: " . $nasabah_id);
            $_SESSION['error_messages'] = ["Data nasabah tidak valid. Silakan login ulang."];
            header("Location: login.php");
            exit;
        }

        // Check if user already has pending booking
        try {
            if (has_pending_booking($nasabah_id)) {
                log_error("User already has pending booking: nasabah_id=" . $nasabah_id);
                $_SESSION['error_messages'] = ["Anda sudah memiliki antrian yang masih aktif. Silakan selesaikan atau batalkan antrian tersebut sebelum membuat antrian baru."];
                $_SESSION['form_data'] = $_POST;
                header("Location: check_status.php");
                exit;
            }
        } catch (Exception $e) {
            log_error("Error checking pending booking: " . $e->getMessage());
            // Lanjutkan proses jika fungsi check gagal
        }

        // Insert booking data into database
        try {
            $stmt = $conn->prepare("INSERT INTO bookings (booking_id, nasabah_id, nama, no_identitas, email, no_hp, layanan, tanggal, waktu, keterangan, file_identitas, nomor_antrian, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', NOW())");
            
            if (!$stmt) {
                throw new Exception("Prepare statement error: " . $conn->error);
            }
            
            $status = 'pending';
            $stmt->bind_param("sissssssssss", $booking_id, $nasabah_id, $nama, $no_identitas, $email, $no_hp, $layanan, $tanggal, $waktu, $keterangan, $new_file_name, $queue_number);
            
            if (!$stmt->execute()) {
                throw new Exception("Execute statement error: " . $stmt->error);
            }
            
            log_error("Booking inserted successfully: " . $booking_id);
            
            // Setelah booking berhasil, langsung konfirmasi dan buat antrian di loket yang dipilih
            require_once '../includes/queue_connector.php';
            
            $queue_result = create_queue_from_booking($booking_id, $queue_number, $tanggal, $nasabah_id, $layanan);
            
            if ($queue_result) {
                log_error("Queue created successfully for booking: " . $booking_id);
            } else {
                log_error("Failed to create queue for booking: " . $booking_id);
                // Lanjutkan proses meskipun gagal membuat antrian
            }
            
            // Simpan informasi penting di session untuk ditampilkan di halaman sukses
            $_SESSION['booking_id'] = $booking_id;
            $_SESSION['nomor_antrian'] = $queue_number;
            
            // Success - redirect ke halaman sukses
            header("Location: booking_success.php?id=" . $booking_id);
            exit;
            
        } catch (Exception $e) {
            log_error("Error inserting booking: " . $e->getMessage());
            $_SESSION['error_messages'] = ["Gagal melakukan booking. Silakan coba lagi. Error: " . $e->getMessage()];
            $_SESSION['form_data'] = $_POST;
            header("Location: booking.php");
            exit;
        }
        
    } catch (Exception $e) {
        log_error("Unexpected error in booking process: " . $e->getMessage());
        $_SESSION['error_messages'] = ["Terjadi kesalahan pada sistem. Silakan coba lagi nanti atau hubungi administrator."];
        $_SESSION['form_data'] = $_POST;
        header("Location: booking.php");
        exit;
    }
} else {
    // If not POST request, redirect to booking page
    log_error("Attempted to access booking_process.php without POST method");
    header("Location: booking.php");
    exit;
}
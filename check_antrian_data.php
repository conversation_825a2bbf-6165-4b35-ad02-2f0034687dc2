<?php
// Check antrian data in database
require_once 'includes/functions.php';

echo "<h1>Check Antrian Data</h1>";

// Check if antrian table exists
echo "<h2>1. Table Check</h2>";
$sql = "SHOW TABLES LIKE 'antrian'";
$result = query($sql);
if ($result && num_rows($result) > 0) {
    echo "<p>✅ Table 'antrian' exists</p>";
} else {
    echo "<p>❌ Table 'antrian' does not exist</p>";
    exit;
}

// Check table structure
echo "<h2>2. Table Structure</h2>";
$sql = "DESCRIBE antrian";
$result = query($sql);
echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
while ($row = fetch_assoc($result)) {
    echo "<tr>";
    echo "<td>" . $row['Field'] . "</td>";
    echo "<td>" . $row['Type'] . "</td>";
    echo "<td>" . $row['Null'] . "</td>";
    echo "<td>" . $row['Key'] . "</td>";
    echo "<td>" . $row['Default'] . "</td>";
    echo "</tr>";
}
echo "</table>";

// Check all antrian data
echo "<h2>3. All Antrian Data</h2>";
$sql = "SELECT * FROM antrian ORDER BY id DESC";
$result = query($sql);
if ($result && num_rows($result) > 0) {
    echo "<p>Total records: " . num_rows($result) . "</p>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Nomor Antrian</th><th>Nama</th><th>Layanan</th><th>Tanggal</th><th>Status</th><th>Loket ID</th><th>Waktu Booking</th></tr>";
    while ($row = fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['nomor_antrian'] . "</td>";
        echo "<td>" . $row['nama'] . "</td>";
        echo "<td>" . $row['jenis_layanan'] . "</td>";
        echo "<td>" . $row['tanggal'] . "</td>";
        echo "<td>" . $row['status'] . "</td>";
        echo "<td>" . ($row['loket_id'] ? $row['loket_id'] : 'NULL') . "</td>";
        echo "<td>" . $row['waktu_booking'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>❌ No antrian data found</p>";
}

// Check today's antrian data
echo "<h2>4. Today's Antrian Data</h2>";
$sql = "SELECT * FROM antrian WHERE tanggal = CURDATE() ORDER BY id DESC";
$result = query($sql);
if ($result && num_rows($result) > 0) {
    echo "<p>Today's records: " . num_rows($result) . "</p>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Nomor Antrian</th><th>Nama</th><th>Layanan</th><th>Status</th><th>Loket ID</th><th>Waktu Booking</th></tr>";
    while ($row = fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['nomor_antrian'] . "</td>";
        echo "<td>" . $row['nama'] . "</td>";
        echo "<td>" . $row['jenis_layanan'] . "</td>";
        echo "<td>" . $row['status'] . "</td>";
        echo "<td>" . ($row['loket_id'] ? $row['loket_id'] : 'NULL') . "</td>";
        echo "<td>" . date('H:i', strtotime($row['waktu_booking'])) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>❌ No antrian data for today</p>";
}

// Test the exact query used in latest_queue.php
echo "<h2>5. Test Latest Queue Query</h2>";
$sql = "SELECT a.id, a.nomor_antrian, a.nama,
        CASE
            WHEN a.jenis_layanan = 'teller' THEN 'Teller'
            WHEN a.jenis_layanan = 'cs' THEN 'Customer Service'
            WHEN a.jenis_layanan = 'kredit' THEN 'Kredit'
            ELSE 'Layanan Lain'
        END as layanan,
        CASE
            WHEN a.loket_id IS NOT NULL THEN CONCAT('Loket ', a.loket_id)
            ELSE '-'
        END as loket,
        a.status,
        TIME_FORMAT(a.waktu_booking, '%H:%i') as waktu_booking
        FROM antrian a
        WHERE a.tanggal = CURDATE()
        ORDER BY a.id DESC
        LIMIT 10";

try {
    $result = query($sql);
    if ($result && num_rows($result) > 0) {
        echo "<p>✅ Query successful, found " . num_rows($result) . " results</p>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Nomor Antrian</th><th>Nama</th><th>Layanan</th><th>Loket</th><th>Status</th><th>Waktu Booking</th></tr>";
        while ($row = fetch_assoc($result)) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . $row['nomor_antrian'] . "</td>";
            echo "<td>" . $row['nama'] . "</td>";
            echo "<td>" . $row['layanan'] . "</td>";
            echo "<td>" . $row['loket'] . "</td>";
            echo "<td>" . $row['status'] . "</td>";
            echo "<td>" . $row['waktu_booking'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>❌ Query returned no results</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Query failed: " . $e->getMessage() . "</p>";
}

// Check current date
echo "<h2>6. Current Date Check</h2>";
echo "<p>Current date (CURDATE()): " . date('Y-m-d') . "</p>";
$sql = "SELECT CURDATE() as current_date";
$result = query($sql);
$row = fetch_assoc($result);
echo "<p>Database current date: " . $row['current_date'] . "</p>";

echo "<br><br><a href='admin/index.php'>Back to Admin Dashboard</a>";
?>

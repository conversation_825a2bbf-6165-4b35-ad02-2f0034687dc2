<?php
// Nasabah login page (Mock Version)
$page_title = 'Login Nasabah';
// Nasabah login page (Mock Version)
$page_title = 'Login Nasabah';
require_once '../includes/functions.php'; // gunakan functions.php asli untuk login real database

// Process login form
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $email = isset($_POST['email']) ? trim($_POST['email']) : '';
    $password = isset($_POST['password']) ? $_POST['password'] : '';

    // Validate input
    if (empty($email)) {
        $error = 'Email tidak boleh kosong.';
    } elseif (empty($password)) {
        $error = 'Password tidak boleh kosong.';
    } else {
        try {
            // Check if using real database or mock
            global $conn;

            if ($conn instanceof MockConnection) {
                // Untuk mode mock, kita terima semua kredensial
                // Jika email = <EMAIL>, gunakan nama "Vina"
                if (strpos($email, 'vina') !== false) {
                    $nama = 'Vina';
                } else {
                    // Ekstrak nama dari email (ambil bagian sebelum @)
                    $nama_parts = explode('@', $email);
                    $nama = ucfirst($nama_parts[0]); // Kapitalisasi huruf pertama
                }

                $_SESSION['nasabah_id'] = 1;
                $_SESSION['nasabah_nama'] = $nama;
                $_SESSION['nasabah_email'] = $email;
                // Add user_id and user_role for compatibility
                $_SESSION['user_id'] = 1;
                $_SESSION['user_role'] = 'nasabah';

                // Set success message
                $_SESSION['success_message'] = "Selamat datang, $nama!";

                // Redirect to dashboard
                redirect('dashboard.php');
            } else {
                // Using real database - check credentials
                $email = sanitize($email);
                $sql = "SELECT * FROM nasabah WHERE email = '$email'";
                $result = query($sql);

                if (num_rows($result) > 0) {
                    $user = fetch_assoc($result);

                    // Check if user is blocked
                    if (isset($user['is_blocked']) && $user['is_blocked'] == 1) {
                        $error = 'Akun Anda telah diblokir. Silakan hubungi customer service.';
                    } elseif ($user['status_verifikasi'] == 'belum') {
                        $error = 'Akun Anda belum diverifikasi. Silakan hubungi customer service untuk verifikasi.';
                    } else {
                        // Untuk mode development, terima semua password
                        // Dalam produksi, gunakan password_verify()
                        $_SESSION['nasabah_id'] = $user['id'];
                        $_SESSION['nasabah_nama'] = $user['nama'];
                        $_SESSION['nasabah_email'] = $user['email'];
                        // Add user_id and user_role for compatibility
                        $_SESSION['user_id'] = $user['id'];
                        $_SESSION['user_role'] = 'nasabah';

                        // Set success message
                        $_SESSION['success_message'] = "Selamat datang, {$user['nama']}!";

                        // Redirect to dashboard
                        redirect('dashboard.php');
                    }
                } else {
                    // Jika email tidak ditemukan, coba gunakan mock data
                    // Ekstrak nama dari email (ambil bagian sebelum @)
                    $nama_parts = explode('@', $email);
                    $nama = ucfirst($nama_parts[0]); // Kapitalisasi huruf pertama

                    $_SESSION['nasabah_id'] = 1;
                    $_SESSION['nasabah_nama'] = $nama;
                    $_SESSION['nasabah_email'] = $email;
                    // Add user_id and user_role for compatibility
                    $_SESSION['user_id'] = 1;
                    $_SESSION['user_role'] = 'nasabah';

                    // Set success message
                    $_SESSION['success_message'] = "Selamat datang, $nama!";

                    // Redirect to dashboard
                    redirect('dashboard.php');
                }
            }
        } catch (Exception $e) {
            // Log the error
            error_log("Login error: " . $e->getMessage());
            $error = 'Terjadi kesalahan saat login. Silakan coba lagi nanti.';
        }
    }
}

// Process login form
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $email = isset($_POST['email']) ? trim($_POST['email']) : '';
    $password = isset($_POST['password']) ? $_POST['password'] : '';

    // Validate input
    if (empty($email)) {
        $error = 'Email tidak boleh kosong.';
    } elseif (empty($password)) {
        $error = 'Password tidak boleh kosong.';
    } else {
        try {
            // Check if using real database or mock
            global $conn;

            if ($conn instanceof MockConnection) {
                // Untuk mode mock, kita terima semua kredensial
                // Jika email = <EMAIL>, gunakan nama "Vina"
                if (strpos($email, 'vina') !== false) {
                    $nama = 'Vina';
                } else {
                    // Ekstrak nama dari email (ambil bagian sebelum @)
                    $nama_parts = explode('@', $email);
                    $nama = ucfirst($nama_parts[0]); // Kapitalisasi huruf pertama
                }

                $_SESSION['nasabah_id'] = 1;
                $_SESSION['nasabah_nama'] = $nama;
                $_SESSION['nasabah_email'] = $email;

                // Set success message
                $_SESSION['success_message'] = "Selamat datang, $nama!";

                // Redirect to dashboard
                redirect('dashboard.php');
            } else {
                // Using real database - check credentials
                $email = sanitize($email);
                $sql = "SELECT * FROM nasabah WHERE email = '$email'";
                $result = query($sql);

                if (num_rows($result) > 0) {
                    $user = fetch_assoc($result);

                    // Check if user is blocked
                    if (isset($user['is_blocked']) && $user['is_blocked'] == 1) {
                        $error = 'Akun Anda telah diblokir. Silakan hubungi customer service.';
                    } elseif ($user['status_verifikasi'] == 'belum') {
                        $error = 'Akun Anda belum diverifikasi. Silakan hubungi customer service untuk verifikasi.';
                    } else {
                        // Untuk mode development, terima semua password
                        // Dalam produksi, gunakan password_verify()
                        $_SESSION['nasabah_id'] = $user['id'];
                        $_SESSION['nasabah_nama'] = $user['nama'];
                        $_SESSION['nasabah_email'] = $user['email'];

                        // Set success message
                        $_SESSION['success_message'] = "Selamat datang, {$user['nama']}!";

                        // Redirect to dashboard
                        redirect('dashboard.php');
                    }
                } else {
                    // Jika email tidak ditemukan, coba gunakan mock data
                    // Ekstrak nama dari email (ambil bagian sebelum @)
                    $nama_parts = explode('@', $email);
                    $nama = ucfirst($nama_parts[0]); // Kapitalisasi huruf pertama

                    $_SESSION['nasabah_id'] = 1;
                    $_SESSION['nasabah_nama'] = $nama;
                    $_SESSION['nasabah_email'] = $email;

                    // Set success message
                    $_SESSION['success_message'] = "Selamat datang, $nama!";

                    // Redirect to dashboard
                    redirect('dashboard.php');
                }
            }
        } catch (Exception $e) {
            // Log the error
            error_log("Login error: " . $e->getMessage());
            $error = 'Terjadi kesalahan saat login. Silakan coba lagi nanti.';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Nasabah - Bank BJB</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">

    <style>
        body {
            background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            flex-direction: column;
        }

        .header {
            background-color: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            padding: 15px 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 100;
        }

        .logo-container {
            display: flex;
            align-items: center;
        }

        .logo-container img {
            height: 50px;
            margin-right: 15px;
        }

        .logo-container h1 {
            color: white;
            font-size: 24px;
            margin: 0;
            font-weight: 600;
        }

        .nav-menu {
            display: flex;
            justify-content: flex-end;
        }

        .nav-menu a {
            color: white;
            margin-left: 20px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s;
            padding: 8px 15px;
            border-radius: 50px;
        }

        .nav-menu a:hover {
            background-color: rgba(255, 255, 255, 0.2);
            transform: translateY(-3px);
        }

        .nav-menu a.active {
            background-color: white;
            color: #4caf50;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .nav-menu a i {
            margin-right: 5px;
        }

        .login-container {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 50px 0;
        }

        .login-card {
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 900px;
        }

        .login-row {
            display: flex;
            flex-wrap: wrap;
        }

        .login-left {
            flex: 1;
            padding: 40px;
            min-width: 300px;
        }

        .login-right {
            flex: 1;
            background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
            padding: 40px;
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            min-width: 300px;
        }

        .login-logo {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-logo img {
            height: 70px;
        }

        .login-title {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-title h3 {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .login-title p {
            color: #666;
        }

        .form-group label {
            font-weight: 600;
            color: #555;
        }

        .form-control {
            border-radius: 8px;
            padding: 12px 15px;
            border: 1px solid #ddd;
        }

        .form-control:focus {
            box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
            border-color: #4caf50;
        }

        .btn-login {
            background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
            display: block;
            width: 100%;
            margin-top: 20px;
        }

        .btn-login:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .forgot-password {
            text-align: right;
            margin-top: 10px;
        }

        .forgot-password a {
            color: #4caf50;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.3s;
            font-size: 14px;
        }

        .forgot-password a:hover {
            color: #388e3c;
            text-decoration: underline;
        }

        .register-link {
            text-align: center;
            margin-top: 30px;
        }

        .register-link a {
            color: #4caf50;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s;
        }

        .register-link a:hover {
            color: #388e3c;
            text-decoration: underline;
        }

        .welcome-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 20px;
        }

        .welcome-text {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 30px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .feature-icon {
            width: 40px;
            height: 40px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .feature-text h4 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .feature-text p {
            font-size: 14px;
            opacity: 0.9;
            margin: 0;
        }

        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #666;
        }

        footer {
            background-color: #333;
            color: white;
            padding: 20px 0;
            text-align: center;
        }

        .copyright {
            opacity: 0.7;
            font-size: 14px;
            margin: 0;
        }

        @media (max-width: 768px) {
            .login-right {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-4">
                    <div class="logo-container">
                        <img src="../assets/img/logo.jpeg" alt="Bank BJB">
                        <h1>Bank BJB</h1>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="nav-menu">
                        <a href="index.php"><i class="fas fa-home"></i> Beranda</a>
                        <a href="booking.php"><i class="fas fa-calendar-check"></i> Booking</a>
                        <a href="check_status.php"><i class="fas fa-search"></i> Cek Status</a>
                        <a href="register.php"><i class="fas fa-user-plus"></i> Registrasi</a>
                        <a href="login.php" class="active"><i class="fas fa-sign-in-alt"></i> Login</a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Login Container -->
    <div class="login-container">
        <div class="container">
            <div class="login-card">
                <div class="login-row">
                    <div class="login-left">
                        <div class="login-logo">
                            <img src="../assets/img/logo.jpeg" alt="Bank BJB">
                        </div>

                        <div class="login-title">
                            <h3>Login Nasabah</h3>
                            <p>Masuk untuk menggunakan layanan booking antrian</p>
                        </div>

                        <?php if (isset($_SESSION['error_messages']) && !empty($_SESSION['error_messages'])): ?>
                        <div class="alert alert-danger alert-dismissible fade show">
                            <ul class="mb-0">
                                <?php foreach ($_SESSION['error_messages'] as $err): ?>
                                <li><?php echo $err; ?></li>
                                <?php endforeach; ?>
                            </ul>
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <?php unset($_SESSION['error_messages']); ?>
                        <?php endif; ?>

                        <?php if (isset($_SESSION['success_message'])): ?>
                        <div class="alert alert-success alert-dismissible fade show">
                            <i class="fas fa-check-circle mr-2"></i> <?php echo $_SESSION['success_message']; ?>
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <?php unset($_SESSION['success_message']); ?>
                        <?php endif; ?>

                        <?php if (!empty($error)): ?>
                        <div class="alert alert-danger alert-dismissible fade show">
                            <i class="fas fa-exclamation-circle mr-2"></i> <?php echo $error; ?>
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <?php endif; ?>

                        <form action="" method="post">
                            <div class="form-group">
                                <label for="email">Email</label>
                                <input type="email" class="form-control" id="email" name="email" value="<?php echo isset($form_data['email']) ? htmlspecialchars($form_data['email']) : ''; ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="password">Password</label>
                                <div class="position-relative">
                                    <input type="password" class="form-control" id="password" name="password" required>
                                    <span class="password-toggle" id="password-toggle">
                                        <i class="fas fa-eye"></i>
                                    </span>
                                </div>
                            </div>

                            <div class="form-group form-check">
                                <input type="checkbox" class="form-check-input" id="remember" name="remember">
                                <label class="form-check-label" for="remember">Ingat saya</label>
                                <div class="forgot-password">
                                    <a href="forgot_password.php">Lupa password?</a>
                                </div>
                            </div>

                            <button type="submit" class="btn-login">Login</button>

                            <div class="register-link">
                                Belum memiliki akun? <a href="register.php">Daftar di sini</a>
                            </div>
                        </form>
                    </div>

                    <div class="login-right">
                        <h3 class="welcome-title">Selamat Datang di Layanan Booking Antrian Bank BJB</h3>
                        <p class="welcome-text">Nikmati berbagai kemudahan dalam menggunakan layanan perbankan kami:</p>

                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="feature-text">
                                <h4>Hemat Waktu</h4>
                                <p>Tidak perlu menunggu lama di bank, cukup datang sesuai jadwal booking Anda</p>
                            </div>
                        </div>

                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <div class="feature-text">
                                <h4>Fleksibel</h4>
                                <p>Pilih tanggal dan waktu yang sesuai dengan jadwal Anda</p>
                            </div>
                        </div>

                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="feature-text">
                                <h4>Aman & Terpercaya</h4>
                                <p>Verifikasi identitas dengan KTP/buku tabungan untuk keamanan transaksi</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <p class="copyright">© 2023 Bank BJB. All Rights Reserved.</p>
        </div>
    </footer>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        $(document).ready(function() {
            // Toggle password visibility
            $("#password-toggle").click(function() {
                var passwordField = $("#password");
                var passwordIcon = $(this).find("i");

                if (passwordField.attr("type") === "password") {
                    passwordField.attr("type", "text");
                    passwordIcon.removeClass("fa-eye").addClass("fa-eye-slash");
                } else {
                    passwordField.attr("type", "password");
                    passwordIcon.removeClass("fa-eye-slash").addClass("fa-eye");
                }
            });
        });
    </script>
</body>
</html>

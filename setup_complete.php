<?php
// Complete setup for the queue management system
require_once 'includes/functions.php';

echo "<h1>Complete Database Setup</h1>";

// Check database connection
global $conn;
echo "<h2>1. Database Connection Check</h2>";
if ($conn instanceof MockConnection) {
    echo "<div style='color: red;'>❌ Using Mock Connection - Database not connected properly</div>";
    echo "<p>Please check your database configuration in config/database.php</p>";
    echo "<p>Make sure MySQL is running and database 'antrian_bank' exists</p>";
} else {
    echo "<div style='color: green;'>✅ Real Database Connection Active</div>";
}

echo "<h2>2. Creating Tables</h2>";

// Create users table
$sql_users = "CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    nama_lengkap VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    role ENUM('admin', 'staff') NOT NULL DEFAULT 'staff',
    status ENUM('aktif', 'nonaktif') NOT NULL DEFAULT 'aktif',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

if (query($sql_users)) {
    echo "<p>✅ Table 'users' created successfully</p>";
} else {
    echo "<p>❌ Failed to create table 'users'</p>";
}

// Create loket table
$sql_loket = "CREATE TABLE IF NOT EXISTS loket (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nama_loket VARCHAR(50) NOT NULL,
    jenis_layanan ENUM('teller', 'cs', 'kredit') NOT NULL,
    status ENUM('aktif', 'nonaktif') NOT NULL DEFAULT 'aktif',
    user_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

if (query($sql_loket)) {
    echo "<p>✅ Table 'loket' created successfully</p>";
} else {
    echo "<p>❌ Failed to create table 'loket'</p>";
}

echo "<h2>3. Adding Default Data</h2>";

// Add admin user
$sql_check_admin = "SELECT COUNT(*) as count FROM users WHERE role = 'admin'";
$result = query($sql_check_admin);
if ($result) {
    $row = fetch_assoc($result);
    if ($row['count'] == 0) {
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $sql_admin = "INSERT INTO users (username, password, nama_lengkap, email, role, status) 
                      VALUES ('admin', '$admin_password', 'Administrator', '<EMAIL>', 'admin', 'aktif')";
        
        if (query($sql_admin)) {
            echo "<p>✅ Admin user created (username: admin, password: admin123)</p>";
        } else {
            echo "<p>❌ Failed to create admin user</p>";
        }
    } else {
        echo "<p>ℹ️ Admin user already exists</p>";
    }
}

// Add staff users
$sql_check_staff = "SELECT COUNT(*) as count FROM users WHERE role = 'staff'";
$result = query($sql_check_staff);
if ($result) {
    $row = fetch_assoc($result);
    if ($row['count'] == 0) {
        echo "<p>Adding default staff...</p>";
        
        $staff_data = [
            ['username' => 'petugas1', 'nama' => 'Petugas Teller 1'],
            ['username' => 'petugas2', 'nama' => 'Petugas Teller 2'],
            ['username' => 'petugas3', 'nama' => 'Petugas CS 1'],
            ['username' => 'petugas4', 'nama' => 'Petugas CS 2'],
            ['username' => 'petugas5', 'nama' => 'Petugas Kredit 1']
        ];
        
        foreach ($staff_data as $staff) {
            $password = password_hash('petugas123', PASSWORD_DEFAULT);
            $sql = "INSERT INTO users (username, password, nama_lengkap, email, role, status) 
                    VALUES ('{$staff['username']}', '$password', '{$staff['nama']}', 
                            '{$staff['username']}@bankbjb.com', 'staff', 'aktif')";
            
            if (query($sql)) {
                echo "<p>✅ Added staff: {$staff['nama']}</p>";
            } else {
                echo "<p>❌ Failed to add staff: {$staff['nama']}</p>";
            }
        }
    } else {
        echo "<p>ℹ️ Staff users already exist ({$row['count']} found)</p>";
    }
}

// Add loket data
$sql_check_loket = "SELECT COUNT(*) as count FROM loket";
$result = query($sql_check_loket);
if ($result) {
    $row = fetch_assoc($result);
    if ($row['count'] == 0) {
        echo "<p>Adding default loket...</p>";
        
        $loket_data = [
            ['id' => 1, 'nama' => 'Loket 1', 'jenis' => 'teller'],
            ['id' => 2, 'nama' => 'Loket 2', 'jenis' => 'teller'],
            ['id' => 3, 'nama' => 'Loket 3', 'jenis' => 'teller'],
            ['id' => 4, 'nama' => 'Loket 4', 'jenis' => 'cs'],
            ['id' => 5, 'nama' => 'Loket 5', 'jenis' => 'cs'],
            ['id' => 6, 'nama' => 'Loket 6', 'jenis' => 'kredit'],
            ['id' => 7, 'nama' => 'Loket 7', 'jenis' => 'kredit']
        ];
        
        foreach ($loket_data as $loket) {
            $sql = "INSERT INTO loket (id, nama_loket, jenis_layanan, status) 
                    VALUES ({$loket['id']}, '{$loket['nama']}', '{$loket['jenis']}', 'aktif')";
            
            if (query($sql)) {
                echo "<p>✅ Added loket: {$loket['nama']}</p>";
            } else {
                echo "<p>❌ Failed to add loket: {$loket['nama']}</p>";
            }
        }
    } else {
        echo "<p>ℹ️ Loket data already exists ({$row['count']} found)</p>";
    }
}

echo "<h2>4. Verification</h2>";

// Verify staff data
$sql = "SELECT COUNT(*) as count FROM users WHERE role = 'staff'";
$result = query($sql);
if ($result) {
    $row = fetch_assoc($result);
    echo "<p>Total staff in database: {$row['count']}</p>";
}

// Verify loket data
$sql = "SELECT COUNT(*) as count FROM loket";
$result = query($sql);
if ($result) {
    $row = fetch_assoc($result);
    echo "<p>Total loket in database: {$row['count']}</p>";
}

echo "<h2>5. Test Query</h2>";
$sql = "SELECT u.*, l.id as loket_id
        FROM users u
        LEFT JOIN loket l ON u.id = l.user_id
        WHERE u.role = 'staff'
        ORDER BY u.id ASC";

$result = query($sql);
if ($result && num_rows($result) > 0) {
    echo "<p>✅ Staff query successful, found " . num_rows($result) . " staff members</p>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Username</th><th>Nama Lengkap</th><th>Loket ID</th></tr>";
    while ($row = fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['username'] . "</td>";
        echo "<td>" . $row['nama_lengkap'] . "</td>";
        echo "<td>" . ($row['loket_id'] ? $row['loket_id'] : 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>❌ No staff found with the query</p>";
}

echo "<h2>Setup Complete!</h2>";
echo "<p><a href='admin/petugas.php'>Go to Petugas Management</a></p>";
?>

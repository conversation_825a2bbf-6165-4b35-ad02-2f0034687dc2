<?php
// Check staff data in database
require_once 'includes/functions.php';

echo "<h2>Checking Staff Data in Database</h2>";

// Check if users table exists
$sql = "SHOW TABLES LIKE 'users'";
$result = query($sql);
if (num_rows($result) > 0) {
    echo "<p>✅ Table 'users' exists</p>";
} else {
    echo "<p>❌ Table 'users' does not exist</p>";
    exit;
}

// Check table structure
echo "<h3>Users Table Structure:</h3>";
$sql = "DESCRIBE users";
$result = query($sql);
echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
while ($row = fetch_assoc($result)) {
    echo "<tr>";
    echo "<td>" . $row['Field'] . "</td>";
    echo "<td>" . $row['Type'] . "</td>";
    echo "<td>" . $row['Null'] . "</td>";
    echo "<td>" . $row['Key'] . "</td>";
    echo "<td>" . $row['Default'] . "</td>";
    echo "<td>" . $row['Extra'] . "</td>";
    echo "</tr>";
}
echo "</table>";

// Check all users
echo "<h3>All Users in Database:</h3>";
$sql = "SELECT * FROM users";
$result = query($sql);
if (num_rows($result) > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Username</th><th>Role</th><th>Nama Lengkap</th><th>Status</th></tr>";
    while ($row = fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['username'] . "</td>";
        echo "<td>" . $row['role'] . "</td>";
        echo "<td>" . (isset($row['nama_lengkap']) ? $row['nama_lengkap'] : 'N/A') . "</td>";
        echo "<td>" . (isset($row['status']) ? $row['status'] : 'N/A') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>❌ No users found in database</p>";
}

// Check staff specifically
echo "<h3>Staff Users Only:</h3>";
$sql = "SELECT * FROM users WHERE role = 'staff'";
$result = query($sql);
if (num_rows($result) > 0) {
    echo "<p>✅ Found " . num_rows($result) . " staff members</p>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Username</th><th>Role</th><th>Nama Lengkap</th></tr>";
    while ($row = fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['username'] . "</td>";
        echo "<td>" . $row['role'] . "</td>";
        echo "<td>" . (isset($row['nama_lengkap']) ? $row['nama_lengkap'] : 'N/A') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>❌ No staff found in database</p>";
}

// Check if loket table exists
echo "<h3>Loket Table Check:</h3>";
$sql = "SHOW TABLES LIKE 'loket'";
$result = query($sql);
if (num_rows($result) > 0) {
    echo "<p>✅ Table 'loket' exists</p>";
    
    // Show loket data
    $sql = "SELECT * FROM loket";
    $result = query($sql);
    if (num_rows($result) > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Nama Loket</th><th>User ID</th><th>Status</th></tr>";
        while ($row = fetch_assoc($result)) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . (isset($row['nama_loket']) ? $row['nama_loket'] : 'N/A') . "</td>";
            echo "<td>" . (isset($row['user_id']) ? $row['user_id'] : 'NULL') . "</td>";
            echo "<td>" . (isset($row['status']) ? $row['status'] : 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} else {
    echo "<p>❌ Table 'loket' does not exist</p>";
}

// Test the exact query used in petugas.php
echo "<h3>Testing Exact Query from petugas.php:</h3>";
$sql = "SELECT u.*, l.id as loket_id
        FROM users u
        LEFT JOIN loket l ON u.id = l.user_id
        WHERE u.role = 'staff'
        ORDER BY u.id ASC";

try {
    $result = query($sql);
    if ($result && num_rows($result) > 0) {
        echo "<p>✅ Query successful, found " . num_rows($result) . " results</p>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Username</th><th>Nama Lengkap</th><th>Role</th><th>Loket ID</th></tr>";
        while ($row = fetch_assoc($result)) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . $row['username'] . "</td>";
            echo "<td>" . (isset($row['nama_lengkap']) ? $row['nama_lengkap'] : 'N/A') . "</td>";
            echo "<td>" . $row['role'] . "</td>";
            echo "<td>" . (isset($row['loket_id']) ? $row['loket_id'] : 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>❌ Query returned no results</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Query failed: " . $e->getMessage() . "</p>";
}

echo "<br><br><a href='admin/petugas.php'>Back to Petugas Management</a>";
?>

<?php
// Verification management page
$page_title = 'Verifikasi Identitas';
$active_menu = 'verifikasi';
$is_admin = true;
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!is_logged_in() || !is_admin()) {
    redirect('../login.php');
}

// Get filter
$filter = isset($_GET['filter']) ? $_GET['filter'] : 'belum';

// Build query based on filter
$where_clause = '';
if ($filter == 'belum') {
    $where_clause = "WHERE n.status_verifikasi = 'belum'";
} elseif ($filter == 'terverifikasi') {
    $where_clause = "WHERE n.status_verifikasi = 'sudah'";
} elseif ($filter == 'ditolak') {
    $where_clause = "WHERE n.status_verifikasi = 'ditolak'";
} elseif ($filter == 'blocked') {
    $where_clause = "WHERE n.is_blocked = 1";
}

// Process verify customer
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['verify_customer'])) {
    $customer_id = (int)$_POST['customer_id'];

    // Update customer verification status
    $sql = "UPDATE nasabah SET status_verifikasi = 'sudah' WHERE id = $customer_id";

    if (query($sql)) {
        $_SESSION['success_message'] = 'Nasabah berhasil diverifikasi.';
        redirect('verifikasi.php');
    } else {
        $_SESSION['error_message'] = 'Gagal memverifikasi nasabah.';
    }
}

// Process reject verification
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['reject_verification'])) {
    $customer_id = (int)$_POST['customer_id'];
    $alasan = sanitize($_POST['alasan']);

    // Update customer verification status
    $sql = "UPDATE nasabah SET status_verifikasi = 'ditolak', catatan_verifikasi = '$alasan' WHERE id = $customer_id";

    if (query($sql)) {
        $_SESSION['success_message'] = 'Verifikasi nasabah berhasil ditolak.';
        redirect('verifikasi.php');
    } else {
        $_SESSION['error_message'] = 'Gagal menolak verifikasi nasabah.';
    }
}

// Process block customer
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['block_customer'])) {
    $customer_id = (int)$_POST['customer_id'];
    $alasan = sanitize($_POST['alasan']);

    // Update customer block status
    $sql = "UPDATE nasabah SET is_blocked = 1, catatan_verifikasi = '$alasan' WHERE id = $customer_id";

    if (query($sql)) {
        $_SESSION['success_message'] = 'Nasabah berhasil diblokir.';
        redirect('verifikasi.php');
    } else {
        $_SESSION['error_message'] = 'Gagal memblokir nasabah.';
    }
}

// Process unblock customer
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['unblock_customer'])) {
    $customer_id = (int)$_POST['customer_id'];

    // Update customer block status
    $sql = "UPDATE nasabah SET is_blocked = 0 WHERE id = $customer_id";

    if (query($sql)) {
        $_SESSION['success_message'] = 'Nasabah berhasil dibuka blokirnya.';
        redirect('verifikasi.php');
    } else {
        $_SESSION['error_message'] = 'Gagal membuka blokir nasabah.';
    }
}

// Get all customers
$sql = "SELECT n.*,
        (SELECT COUNT(*) FROM antrian WHERE nasabah_id = n.id) as total_antrian
        FROM nasabah n
        $where_clause
        ORDER BY n.id DESC";
$result = query($sql);
$customers = fetch_all($result);

// Include header
include_once '../includes/header.php';
?>

<div class="row">
    <div class="col-md-12">
        <h1 class="mb-4">Verifikasi Identitas Nasabah</h1>

        <?php include_once '../includes/alerts.php'; ?>

        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-filter"></i> Filter Nasabah
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-12">
                        <a href="verifikasi.php?filter=belum" class="btn <?php echo $filter == 'belum' ? 'btn-warning' : 'btn-outline-warning'; ?> mb-2">
                            <i class="fas fa-clock"></i> Belum Diverifikasi
                        </a>
                        <a href="verifikasi.php?filter=terverifikasi" class="btn <?php echo $filter == 'terverifikasi' ? 'btn-success' : 'btn-outline-success'; ?> mb-2">
                            <i class="fas fa-check"></i> Terverifikasi
                        </a>
                        <a href="verifikasi.php?filter=ditolak" class="btn <?php echo $filter == 'ditolak' ? 'btn-danger' : 'btn-outline-danger'; ?> mb-2">
                            <i class="fas fa-times"></i> Ditolak
                        </a>
                        <a href="verifikasi.php?filter=blocked" class="btn <?php echo $filter == 'blocked' ? 'btn-dark' : 'btn-outline-dark'; ?> mb-2">
                            <i class="fas fa-ban"></i> Diblokir
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-id-card"></i> Daftar Nasabah
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover datatable">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Nama</th>
                                <th>No. Identitas</th>
                                <th>No. HP</th>
                                <th>Email</th>
                                <th>Status</th>
                                <th>Total Antrian</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if(count($customers) > 0): ?>
                            <?php foreach($customers as $customer): ?>
                            <tr>
                                <td><?php echo $customer['id']; ?></td>
                                <td><?php echo $customer['nama']; ?></td>
                                <td><?php echo $customer['no_identitas']; ?></td>
                                <td><?php echo $customer['no_hp']; ?></td>
                                <td><?php echo $customer['email']; ?></td>
                                <td>
                                    <?php if($customer['status_verifikasi'] == 'belum'): ?>
                                    <span class="badge badge-warning">Belum Diverifikasi</span>
                                    <?php elseif($customer['status_verifikasi'] == 'sudah'): ?>
                                    <span class="badge badge-success">Terverifikasi</span>
                                    <?php elseif($customer['status_verifikasi'] == 'ditolak'): ?>
                                    <span class="badge badge-danger">Ditolak</span>
                                    <?php endif; ?>

                                    <?php if($customer['is_blocked']): ?>
                                    <span class="badge badge-dark">Diblokir</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo $customer['total_antrian']; ?></td>
                                <td>
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-sm btn-info dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            Aksi
                                        </button>
                                        <div class="dropdown-menu">
                                            <?php if($customer['file_identitas']): ?>
                                            <a class="dropdown-item btn-preview-id" href="#" data-toggle="modal" data-target="#previewModal" data-id="<?php echo $customer['id']; ?>" data-image="../uploads/identitas/<?php echo $customer['file_identitas']; ?>">
                                                <i class="fas fa-eye"></i> Lihat Identitas
                                            </a>
                                            <?php endif; ?>

                                            <?php if($customer['status_verifikasi'] == 'belum'): ?>
                                            <a class="dropdown-item" href="#" data-toggle="modal" data-target="#verifyModal<?php echo $customer['id']; ?>">
                                                <i class="fas fa-check"></i> Verifikasi
                                            </a>
                                            <a class="dropdown-item text-danger" href="#" data-toggle="modal" data-target="#rejectModal<?php echo $customer['id']; ?>">
                                                <i class="fas fa-times"></i> Tolak
                                            </a>
                                            <?php endif; ?>

                                            <?php if(!$customer['is_blocked']): ?>
                                            <a class="dropdown-item text-dark" href="#" data-toggle="modal" data-target="#blockModal<?php echo $customer['id']; ?>">
                                                <i class="fas fa-ban"></i> Blokir
                                            </a>
                                            <?php else: ?>
                                            <a class="dropdown-item text-success" href="#" data-toggle="modal" data-target="#unblockModal<?php echo $customer['id']; ?>">
                                                <i class="fas fa-unlock"></i> Buka Blokir
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                            </tr>

                            <!-- Verify Modal -->
                            <?php if($customer['status_verifikasi'] == 'belum'): ?>
                            <div class="modal fade" id="verifyModal<?php echo $customer['id']; ?>" tabindex="-1" role="dialog" aria-labelledby="verifyModalLabel<?php echo $customer['id']; ?>" aria-hidden="true">
                                <div class="modal-dialog" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="verifyModalLabel<?php echo $customer['id']; ?>">Verifikasi Nasabah</h5>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <form action="verifikasi.php" method="post">
                                            <div class="modal-body">
                                                <p>Apakah Anda yakin ingin memverifikasi nasabah ini?</p>
                                                <input type="hidden" name="customer_id" value="<?php echo $customer['id']; ?>">
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                                                <button type="submit" name="verify_customer" class="btn btn-success">Verifikasi</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- Reject Modal -->
                            <div class="modal fade" id="rejectModal<?php echo $customer['id']; ?>" tabindex="-1" role="dialog" aria-labelledby="rejectModalLabel<?php echo $customer['id']; ?>" aria-hidden="true">
                                <div class="modal-dialog" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="rejectModalLabel<?php echo $customer['id']; ?>">Tolak Verifikasi</h5>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <form action="verifikasi.php" method="post">
                                            <div class="modal-body">
                                                <div class="form-group">
                                                    <label for="alasan">Alasan Penolakan</label>
                                                    <textarea class="form-control" id="alasan" name="alasan" rows="3" required></textarea>
                                                </div>
                                                <input type="hidden" name="customer_id" value="<?php echo $customer['id']; ?>">
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                                                <button type="submit" name="reject_verification" class="btn btn-danger">Tolak</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>

                            <!-- Block Modal -->
                            <?php if(!$customer['is_blocked']): ?>
                            <div class="modal fade" id="blockModal<?php echo $customer['id']; ?>" tabindex="-1" role="dialog" aria-labelledby="blockModalLabel<?php echo $customer['id']; ?>" aria-hidden="true">
                                <div class="modal-dialog" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="blockModalLabel<?php echo $customer['id']; ?>">Blokir Nasabah</h5>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <form action="verifikasi.php" method="post">
                                            <div class="modal-body">
                                                <div class="form-group">
                                                    <label for="alasan">Alasan Pemblokiran</label>
                                                    <textarea class="form-control" id="alasan" name="alasan" rows="3" required></textarea>
                                                </div>
                                                <input type="hidden" name="customer_id" value="<?php echo $customer['id']; ?>">
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                                                <button type="submit" name="block_customer" class="btn btn-dark">Blokir</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <?php else: ?>
                            <!-- Unblock Modal -->
                            <div class="modal fade" id="unblockModal<?php echo $customer['id']; ?>" tabindex="-1" role="dialog" aria-labelledby="unblockModalLabel<?php echo $customer['id']; ?>" aria-hidden="true">
                                <div class="modal-dialog" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="unblockModalLabel<?php echo $customer['id']; ?>">Buka Blokir Nasabah</h5>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <form action="verifikasi.php" method="post">
                                            <div class="modal-body">
                                                <p>Apakah Anda yakin ingin membuka blokir nasabah ini?</p>
                                                <input type="hidden" name="customer_id" value="<?php echo $customer['id']; ?>">
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                                                <button type="submit" name="unblock_customer" class="btn btn-success">Buka Blokir</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                            <?php endforeach; ?>
                            <?php else: ?>
                            <tr>
                                <td colspan="8" class="text-center">Tidak ada data nasabah.</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Preview ID Modal -->
<div class="modal fade" id="previewModal" tabindex="-1" role="dialog" aria-labelledby="previewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previewModalLabel">Preview Identitas</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <img id="previewImage" src="" alt="ID Card" class="img-fluid">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Preview ID image
    $('.btn-preview-id').on('click', function() {
        var imageUrl = $(this).data('image');
        $('#previewImage').attr('src', imageUrl);
    });
});
</script>

<?php
// Include footer
include_once '../includes/footer.php';
?>

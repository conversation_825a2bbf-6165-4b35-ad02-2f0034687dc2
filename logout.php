<?php
// Logout process
require_once 'includes/functions_mock.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Set success message
$_SESSION['success_message'] = 'Anda berhasil logout.';

// Clear remember me cookie if exists
if (isset($_COOKIE['remember_token'])) {
    setcookie('remember_token', '', time() - 3600, '/'); // Expire cookie
}
if (isset($_COOKIE['user_id'])) {
    setcookie('user_id', '', time() - 3600, '/'); // Expire cookie
}

// Determine redirect page based on role
$redirect_page = 'index.php';
if (isset($_SESSION['role'])) {
    if ($_SESSION['role'] == 'admin') {
        $redirect_page = 'login_admin.php';
    } elseif ($_SESSION['role'] == 'petugas') {
        $redirect_page = 'login_petugas.php';
    } elseif ($_SESSION['role'] == 'nasabah') {
        $redirect_page = 'nasabah/login.php';
    }
}

// Save success message
$success_message = $_SESSION['success_message'];

// Clear all session variables
$_SESSION = array();

// Destroy the session
if (session_id() != "") {
    session_destroy();
}

// Start a new session for the message
session_start();
$_SESSION['success_message'] = $success_message;

// Redirect to appropriate login page
redirect($redirect_page);

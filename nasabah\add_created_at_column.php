<?php
// <PERSON><PERSON><PERSON> to add created_at column to nasabah table
require_once '../includes/db.php';

// Check if created_at column already exists
$result = $conn->query("SHOW COLUMNS FROM nasabah LIKE 'created_at'");
$exists = ($result->num_rows > 0);

if (!$exists) {
    // Add created_at column to nasabah table
    $sql = "ALTER TABLE nasabah ADD COLUMN created_at DATETIME DEFAULT CURRENT_TIMESTAMP";
    
    if ($conn->query($sql) === TRUE) {
        echo "created_at column added successfully to nasabah table.";
    } else {
        echo "Error adding created_at column: " . $conn->error;
    }
} else {
    echo "created_at column already exists in nasabah table.";
}

echo "<br><br><a href='register.php'>Go to Registration Page</a>";
?>

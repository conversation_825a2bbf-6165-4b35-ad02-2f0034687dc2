<?php
// Customer Service display page
$page_title = 'Display Antrian Customer Service';
$no_header = true;
require_once '../includes/functions.php';

// Get active counters for Customer Service
$sql = "SELECT l.*, u.nama_lengkap
        FROM loket l
        LEFT JOIN users u ON l.user_id = u.id
        WHERE l.status = 'aktif'
        AND l.id IN (SELECT DISTINCT loket_id FROM antrian WHERE poli_id = 2 AND tanggal = CURDATE())
        ORDER BY l.id";
$result = query($sql);
$counters = fetch_all($result);

// Get current queue for each counter
foreach ($counters as $key => $counter) {
    $sql = "SELECT a.*, n.nama, p.nama_poli
            FROM antrian a
            LEFT JOIN nasabah n ON a.nasabah_id = n.id
            LEFT JOIN poli p ON a.poli_id = p.id
            WHERE a.loket_id = {$counter['id']}
            AND a.status = 'dipanggil'
            AND a.tanggal = CURDATE()
            AND a.poli_id = 2
            ORDER BY a.waktu_dipanggil DESC
            LIMIT 1";
    $result = query($sql);

    if (num_rows($result) > 0) {
        $counters[$key]['current_queue'] = fetch_assoc($result);
    } else {
        $counters[$key]['current_queue'] = null;
    }
}

// Get next queues for Customer Service
$sql = "SELECT a.*, n.nama, p.nama_poli
        FROM antrian a
        LEFT JOIN nasabah n ON a.nasabah_id = n.id
        LEFT JOIN poli p ON a.poli_id = p.id
        WHERE a.status = 'menunggu'
        AND a.tanggal = CURDATE()
        AND a.poli_id = 2
        ORDER BY a.id
        LIMIT 5";
$result = query($sql);
$next_queues = fetch_all($result);
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Bank BJB</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        /* Override footer styles for display page */
        footer {
            background-color: #000 !important;
            border-top: 1px solid #333 !important;
            box-shadow: none !important;
            height: 25px !important;
        }

        footer span {
            color: #888 !important;
            font-size: 10px !important;
        }

        footer .col-md-6.text-left {
            padding-left: 20px !important;
        }

        /* Add padding to container to prevent content from being hidden by footer */
        .container {
            padding-bottom: 30px;
        }

        /* Improved display styles for TV */
        body.xbx-display {
            background: linear-gradient(135deg, #03a9f4 0%, #0288d1 100%);
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow-x: hidden;
        }

        .xbx-header {
            background-color: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 10px 0;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .xbx-logo img {
            height: 50px;
            margin-left: 10px;
        }

        .xbx-time {
            text-align: right;
            padding-right: 15px;
        }

        #clock {
            font-size: 32px;
            font-weight: bold;
            color: #fff;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
            display: block;
        }

        .xbx-counter {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
            margin-bottom: 25px;
            border: 1px solid rgba(255,255,255,0.1);
        }

        .xbx-counter:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.4);
        }

        .xbx-counter-header {
            padding: 12px;
            text-align: center;
            font-size: 22px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
            background: linear-gradient(135deg, #03a9f4 0%, #0288d1 100%);
        }

        .xbx-counter-body {
            padding: 20px;
            background-color: rgba(0, 0, 0, 0.5);
            text-align: center;
            border-top: 1px solid rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .xbx-counter-number {
            font-size: 80px;
            font-weight: bold;
            margin: 15px 0;
            color: #fff;
            text-shadow: 0 0 10px rgba(255,255,255,0.3);
        }

        .xbx-counter-customer {
            font-size: 22px;
            margin-bottom: 10px;
            color: #fff;
            background-color: rgba(255,255,255,0.2);
            padding: 8px;
            border-radius: 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .card.bg-dark {
            background-color: rgba(0, 0, 0, 0.5) !important;
            border: 1px solid rgba(255,255,255,0.1);
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .card-header {
            border-bottom: 1px solid rgba(255,255,255,0.1);
            padding: 15px;
            background: linear-gradient(135deg, #03a9f4 0%, #0288d1 100%);
        }

        .table-dark {
            background-color: transparent;
            color: #ddd;
        }

        .table-dark thead th {
            border-bottom: 2px solid rgba(255,255,255,0.1);
            text-transform: uppercase;
            font-size: 14px;
            letter-spacing: 1px;
            color: #999;
        }

        .table-dark td, .table-dark th {
            border-top: 1px solid rgba(255,255,255,0.05);
            padding: 12px 15px;
        }

        .badge {
            font-size: 18px;
            padding: 8px 15px;
            border-radius: 5px;
            background: linear-gradient(135deg, #03a9f4 0%, #0288d1 100%);
        }

        .video-container {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.4);
        }

        iframe {
            display: block;
        }
        
        .service-title {
            background-color: rgba(255, 255, 255, 0.2);
            padding: 10px 20px;
            border-radius: 50px;
            display: inline-block;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body class="xbx-display">
    <div class="xbx-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="xbx-logo d-flex align-items-center">
                        <img src="../assets/img/logo.jpeg" alt="Bank BJB" class="mr-3">
                        <h2 class="mb-0" style="color: #fff; font-weight: 600; letter-spacing: 1px;">ANTRIAN CUSTOMER SERVICE</h2>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="xbx-time text-right">
                        <span id="clock">22:40:39</span>
                        <div style="color: #ccc; font-size: 18px;"><?php echo tanggal_indonesia(); ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <div class="xbx-content">
            <div class="text-center mb-4">
                <div class="service-title">
                    <h3 class="mb-0"><i class="fas fa-headset mr-2"></i> LAYANAN CUSTOMER SERVICE</h3>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-8">
                    <div class="row">
                        <?php if(count($counters) > 0): ?>
                            <?php foreach($counters as $counter): ?>
                            <div class="col-md-6 mb-4">
                                <div class="xbx-counter">
                                    <div class="xbx-counter-header">
                                        <i class="fas fa-user-tie mr-2"></i><?php echo $counter['nama_loket']; ?>
                                    </div>
                                    <div class="xbx-counter-body">
                                        <div class="xbx-counter-number" id="counter-<?php echo $counter['id']; ?>">
                                            <?php
                                            if ($counter['current_queue']) {
                                                echo explode('-', $counter['current_queue']['nomor_antrian'])[1];
                                            } else {
                                                echo '---';
                                            }
                                            ?>
                                        </div>
                                        <div class="xbx-counter-customer">
                                            <?php
                                            if ($counter['current_queue']) {
                                                echo $counter['current_queue']['nama'];
                                            } else {
                                                echo 'Belum ada antrian';
                                            }
                                            ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="col-12 text-center">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle fa-2x mb-3 d-block"></i>
                                    <h4>Belum ada loket Customer Service yang aktif</h4>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card bg-dark text-white">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-list-ol mr-2"></i> Antrian Berikutnya</h5>
                                </div>
                                <div class="card-body">
                                    <?php if(count($next_queues) > 0): ?>
                                    <div class="table-responsive">
                                        <table class="table table-dark table-hover">
                                            <thead>
                                                <tr>
                                                    <th width="20%">Nomor</th>
                                                    <th width="40%">Nama</th>
                                                    <th width="40%">Layanan</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach($next_queues as $queue): ?>
                                                <tr>
                                                    <td>
                                                        <span class="badge">
                                                            <?php echo explode('-', $queue['nomor_antrian'])[1]; ?>
                                                        </span>
                                                    </td>
                                                    <td><?php echo $queue['nama']; ?></td>
                                                    <td><i class="fas fa-tag mr-1"></i> <?php echo $queue['nama_poli']; ?></td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                    <?php else: ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-info-circle fa-3x mb-3"></i>
                                        <h5>Tidak ada antrian yang menunggu</h5>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card bg-dark text-white h-100">
                        <div class="card-body">
                            <div class="video-container mb-4">
                                <iframe width="100%" height="250" src="https://www.youtube.com/embed/QkPv-tkRflI" frameborder="0" allow="autoplay; encrypted-media" allowfullscreen></iframe>
                            </div>

                            <div class="card text-white mb-4" style="background: linear-gradient(135deg, #03a9f4 0%, #0288d1 100%); border-radius: 10px;">
                                <div class="card-body text-center py-4">
                                    <i class="fas fa-university fa-3x mb-3"></i>
                                    <h4 style="font-weight: 600;">Selamat Datang di Bank BJB</h4>
                                    <p class="mb-0" style="font-size: 16px;">Terima kasih telah menggunakan layanan kami</p>
                                </div>
                            </div>

                            <div class="text-center mt-4">
                                <img src="../assets/img/logo.jpeg" alt="Bank BJB" class="img-fluid" style="max-width: 180px;">
                                <p class="mt-3" style="color: #999; font-size: 14px;">Melayani dengan sepenuh hati</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        $(document).ready(function() {
            // Update clock
            function updateClock() {
                var now = new Date();
                var hours = now.getHours();
                var minutes = now.getMinutes();
                var seconds = now.getSeconds();

                // Add leading zeros
                hours = (hours < 10) ? "0" + hours : hours;
                minutes = (minutes < 10) ? "0" + minutes : minutes;
                seconds = (seconds < 10) ? "0" + seconds : seconds;

                // Display the time
                $('#clock').text(hours + ":" + minutes + ":" + seconds);

                // Update every second
                setTimeout(updateClock, 1000);
            }

            updateClock();
            
            // Auto refresh page every 30 seconds
            setTimeout(function() {
                location.reload();
            }, 30000);
        });
    </script>
</body>
</html>

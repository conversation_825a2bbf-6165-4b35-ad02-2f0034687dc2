<?php
// Booking confirmation page
$page_title = 'Konfirmasi Booking';
require_once '../includes/functions.php';

// Check if booking exists in session
if (!isset($_SESSION['booking'])) {
    redirect('booking.php');
}

$booking = $_SESSION['booking'];
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Bank BJB</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">

    <style>
        body {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .confirmation-container {
            background-color: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
            width: 100%;
            max-width: 600px;
        }
        .logo-container {
            text-align: center;
            margin-bottom: 20px;
        }
        .logo-container img {
            height: 60px;
            margin-bottom: 10px;
        }
        .confirmation-content {
            border: 2px dashed #ccc;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .booking-number {
            font-size: 36px;
            font-weight: bold;
            text-align: center;
            color: #007bff;
            margin: 20px 0;
        }
        .booking-info {
            margin-bottom: 10px;
        }
        .verification-status {
            padding: 10px;
            border-radius: 5px;
            margin: 20px 0;
            text-align: center;
            font-weight: bold;
        }
        .verification-status.pending {
            background-color: #ffc107;
            color: #212529;
        }
        .verification-status.verified {
            background-color: #28a745;
            color: white;
        }
        .print-section {
            text-align: center;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="confirmation-container">
                    <div class="logo-container">
                        <img src="../assets/img/logo.jpeg" alt="Bank BJB">
                        <h2>Bank BJB</h2>
                        <p class="text-muted">Konfirmasi Booking Antrian</p>
                    </div>

                    <div class="alert alert-success text-center">
                        <i class="fas fa-check-circle"></i> Booking antrian berhasil dibuat!
                    </div>

                    <div class="confirmation-content" id="confirmation-print">
                        <div class="text-center mb-3">
                            <h4><?php echo get_setting('nama_instansi'); ?></h4>
                            <p>Booking Antrian Online</p>
                        </div>

                        <div class="booking-number">
                            <?php echo $booking['nomor']; ?>
                        </div>

                        <div class="booking-info">
                            <strong>Nama:</strong> <?php echo $booking['nama']; ?>
                        </div>

                        <div class="booking-info">
                            <strong>Layanan:</strong> <?php echo $booking['poli']; ?>
                        </div>

                        <div class="booking-info">
                            <strong>Tanggal Kunjungan:</strong> <?php echo date('d/m/Y', strtotime($booking['tanggal'])); ?>
                        </div>

                        <div class="booking-info">
                            <strong>Estimasi Waktu Tunggu:</strong> <?php echo $booking['estimasi']; ?> menit
                        </div>

                        <?php if($booking['verifikasi'] == 'belum'): ?>
                        <div class="verification-status pending">
                            <i class="fas fa-clock"></i> Menunggu Verifikasi
                        </div>
                        <div class="alert alert-warning text-center">
                            <small>Booking Anda sedang menunggu verifikasi oleh petugas. Silahkan cek status booking secara berkala.</small>
                        </div>
                        <div class="alert alert-info text-center">
                            <small><i class="fas fa-info-circle"></i> Verifikasi identitas diperlukan untuk mencegah booking palsu. Jika data identitas tidak valid, akun Anda dapat diblokir.</small>
                        </div>
                        <?php else: ?>
                        <div class="verification-status verified">
                            <i class="fas fa-check-circle"></i> Terverifikasi
                        </div>
                        <div class="alert alert-success text-center">
                            <small>Booking Anda telah terverifikasi. Silahkan datang pada tanggal yang telah ditentukan.</small>
                        </div>
                        <?php endif; ?>

                        <div class="text-center mt-4">
                            <p><small>Konfirmasi ini dicetak pada: <?php echo date('d/m/Y H:i:s'); ?></small></p>
                        </div>
                    </div>

                    <div class="print-section">
                        <button class="btn btn-success" onclick="printConfirmation()">
                            <i class="fas fa-print"></i> Cetak Konfirmasi
                        </button>

                        <a href="check_status.php" class="btn btn-primary ml-2">
                            <i class="fas fa-search"></i> Cek Status
                        </a>
                    </div>

                    <div class="text-center mt-4">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <?php if($booking['verifikasi'] == 'belum'): ?>
                            Silahkan simpan nomor booking Anda: <strong><?php echo $booking['nomor']; ?></strong> untuk cek status verifikasi.
                            <?php else: ?>
                            Silahkan datang pada tanggal <?php echo date('d/m/Y', strtotime($booking['tanggal'])); ?> dan tunjukkan nomor booking Anda: <strong><?php echo $booking['nomor']; ?></strong>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="text-center mt-3">
                        <a href="../index.php" class="btn btn-link">
                            <i class="fas fa-arrow-left"></i> Kembali ke Halaman Utama
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function printConfirmation() {
            var printContents = document.getElementById('confirmation-print').innerHTML;
            var originalContents = document.body.innerHTML;

            document.body.innerHTML = '<div style="padding: 20px;">' + printContents + '</div>';

            window.print();

            document.body.innerHTML = originalContents;
        }
    </script>
</body>
</html>

<?php
// Clear booking from session after display
unset($_SESSION['booking']);
?>

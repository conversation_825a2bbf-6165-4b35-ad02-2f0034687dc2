<?php
// Booking antrian Bank BJB - Registrasi Akun
session_start();
require_once '../includes/functions.php';
require_once '../includes/db.php';
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registrasi Akun - Bank BJB</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">

    <style>
        body {
            background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .header {
            background-color: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            padding: 15px 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 100;
        }

        .logo-container {
            display: flex;
            align-items: center;
        }

        .logo-container img {
            height: 50px;
            margin-right: 15px;
        }

        .logo-container h1 {
            color: white;
            font-size: 24px;
            margin: 0;
            font-weight: 600;
        }

        .nav-menu {
            display: flex;
            justify-content: flex-end;
        }

        .nav-menu a {
            color: white;
            margin-left: 20px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s;
            padding: 8px 15px;
            border-radius: 50px;
        }

        .nav-menu a:hover {
            background-color: rgba(255, 255, 255, 0.2);
            transform: translateY(-3px);
        }

        .nav-menu a.active {
            background-color: white;
            color: #4caf50;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .nav-menu a i {
            margin-right: 5px;
        }

        .page-title {
            text-align: center;
            color: white;
            padding: 40px 0;
        }

        .page-title h2 {
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .page-title p {
            font-size: 18px;
            opacity: 0.9;
            max-width: 700px;
            margin: 0 auto;
        }

        .register-container {
            background-color: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 50px;
        }

        .form-title {
            text-align: center;
            margin-bottom: 30px;
        }

        .form-title h3 {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .form-title p {
            color: #666;
        }

        .form-group label {
            font-weight: 600;
            color: #555;
        }

        .form-control {
            border-radius: 8px;
            padding: 12px 15px;
            border: 1px solid #ddd;
        }

        .form-control:focus {
            box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
            border-color: #4caf50;
        }

        .btn-register {
            background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
            display: block;
            width: 100%;
            margin-top: 20px;
        }

        .btn-register:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .login-link {
            text-align: center;
            margin-top: 20px;
        }

        .login-link a {
            color: #4caf50;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s;
        }

        .login-link a:hover {
            color: #388e3c;
            text-decoration: underline;
        }

        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #666;
        }

        .password-requirements {
            margin-top: 10px;
            font-size: 14px;
            color: #666;
        }

        .password-requirements ul {
            padding-left: 20px;
            margin-bottom: 0;
        }

        .password-requirements li {
            margin-bottom: 5px;
        }

        .password-requirements li.valid {
            color: #4caf50;
        }

        .password-requirements li.invalid {
            color: #f44336;
        }

        footer {
            background-color: #333;
            color: white;
            padding: 40px 0;
            text-align: center;
        }

        .footer-logo {
            height: 60px;
            margin-bottom: 20px;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }

        .footer-links a {
            color: white;
            margin: 0 15px;
            text-decoration: none;
            transition: all 0.3s;
        }

        .footer-links a:hover {
            color: #4caf50;
        }

        .copyright {
            opacity: 0.7;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-4">
                    <div class="logo-container">
                        <img src="../assets/img/logo.jpeg" alt="Bank BJB">
                        <h1>Bank BJB</h1>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="nav-menu">
                        <a href="index.php"><i class="fas fa-home"></i> Beranda</a>
                        <a href="booking.php"><i class="fas fa-calendar-check"></i> Booking</a>
                        <a href="check_status.php"><i class="fas fa-search"></i> Cek Status</a>
                        <a href="register.php" class="active"><i class="fas fa-user-plus"></i> Registrasi</a>
                        <a href="login.php"><i class="fas fa-sign-in-alt"></i> Login</a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Page Title -->
    <div class="page-title">
        <div class="container">
            <h2>Registrasi Akun</h2>
            <p>Daftar akun untuk menggunakan layanan booking antrian online Bank BJB</p>
        </div>
    </div>

    <!-- Registration Form -->
    <div class="container">
        <div class="register-container">
            <div class="form-title">
                <h3>Formulir Registrasi</h3>
                <p>Silakan lengkapi data berikut dengan benar</p>
            </div>

            <?php if (isset($_SESSION['error_messages']) && !empty($_SESSION['error_messages'])): ?>
            <div class="alert alert-danger">
                <ul class="mb-0">
                    <?php foreach ($_SESSION['error_messages'] as $error): ?>
                    <li><?php echo $error; ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php unset($_SESSION['error_messages']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success">
                <?php echo $_SESSION['success_message']; ?>
            </div>
            <?php unset($_SESSION['success_message']); ?>
            <?php endif; ?>

            <form action="register_process.php" method="post">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="nama">Nama Lengkap</label>
                            <input type="text" class="form-control" id="nama" name="nama" value="<?php echo isset($form_data['nama']) ? htmlspecialchars($form_data['nama']) : ''; ?>" required>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="no_ktp">Nomor KTP</label>
                            <input type="text" class="form-control" id="no_ktp" name="no_ktp" value="<?php echo isset($form_data['no_ktp']) ? htmlspecialchars($form_data['no_ktp']) : ''; ?>" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="email">Email</label>
                            <input type="email" class="form-control" id="email" name="email" value="<?php echo isset($form_data['email']) ? htmlspecialchars($form_data['email']) : ''; ?>" required>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="no_hp">Nomor HP</label>
                            <input type="tel" class="form-control" id="no_hp" name="no_hp" value="<?php echo isset($form_data['no_hp']) ? htmlspecialchars($form_data['no_hp']) : ''; ?>" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="password">Password</label>
                            <div class="position-relative">
                                <input type="password" class="form-control" id="password" name="password" required>
                                <span class="password-toggle" id="password-toggle">
                                    <i class="fas fa-eye"></i>
                                </span>
                            </div>
                            <div class="password-requirements">
                                <p>Password harus memenuhi kriteria berikut:</p>
                                <ul id="password-criteria">
                                    <li id="length">Minimal 8 karakter</li>
                                    <li id="uppercase">Minimal 1 huruf besar</li>
                                    <li id="lowercase">Minimal 1 huruf kecil</li>
                                    <li id="number">Minimal 1 angka</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="confirm_password">Konfirmasi Password</label>
                            <div class="position-relative">
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                <span class="password-toggle" id="confirm-password-toggle">
                                    <i class="fas fa-eye"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="alamat">Alamat</label>
                    <textarea class="form-control" id="alamat" name="alamat" rows="3" required><?php echo isset($form_data['alamat']) ? htmlspecialchars($form_data['alamat']) : ''; ?></textarea>
                </div>

                <div class="form-group form-check">
                    <input type="checkbox" class="form-check-input" id="terms" name="terms" required>
                    <label class="form-check-label" for="terms">
                        Saya menyetujui <a href="#" data-toggle="modal" data-target="#termsModal">Syarat dan Ketentuan</a> yang berlaku
                    </label>
                </div>

                <button type="submit" class="btn-register">Daftar Sekarang</button>

                <div class="login-link">
                    Sudah memiliki akun? <a href="login.php">Login di sini</a>
                </div>
            </form>
        </div>
    </div>

    <!-- Terms and Conditions Modal -->
    <div class="modal fade" id="termsModal" tabindex="-1" role="dialog" aria-labelledby="termsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="termsModalLabel">Syarat dan Ketentuan</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <h6>1. Umum</h6>
                    <p>Dengan menggunakan layanan booking antrian online Bank BJB, Anda menyetujui syarat dan ketentuan yang berlaku.</p>

                    <h6>2. Pendaftaran Akun</h6>
                    <p>Anda wajib memberikan informasi yang akurat dan lengkap saat mendaftar. Bank BJB berhak menolak pendaftaran jika ditemukan informasi yang tidak valid.</p>

                    <h6>3. Booking Antrian</h6>
                    <p>Booking antrian hanya berlaku untuk tanggal dan waktu yang telah dipilih. Jika Anda tidak hadir pada waktu yang ditentukan, booking akan otomatis dibatalkan.</p>

                    <h6>4. Verifikasi Identitas</h6>
                    <p>Verifikasi identitas menggunakan KTP atau buku tabungan diperlukan untuk memastikan keamanan transaksi. Data yang diunggah akan dijaga kerahasiaannya.</p>

                    <h6>5. Pembatalan</h6>
                    <p>Pembatalan booking dapat dilakukan minimal 1 jam sebelum waktu yang telah ditentukan.</p>

                    <h6>6. Privasi</h6>
                    <p>Bank BJB akan menjaga kerahasiaan data pribadi Anda sesuai dengan kebijakan privasi yang berlaku.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <img src="../assets/img/logo.jpeg" alt="Bank BJB" class="footer-logo">
            <div class="footer-links">
                <a href="#">Tentang Kami</a>
                <a href="#">Syarat & Ketentuan</a>
                <a href="#">Kebijakan Privasi</a>
                <a href="#">Hubungi Kami</a>
            </div>
            <p class="copyright">© 2023 Bank BJB. All Rights Reserved.</p>
        </div>
    </footer>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        $(document).ready(function() {
            // Toggle password visibility
            $("#password-toggle").click(function() {
                var passwordField = $("#password");
                var passwordIcon = $(this).find("i");

                if (passwordField.attr("type") === "password") {
                    passwordField.attr("type", "text");
                    passwordIcon.removeClass("fa-eye").addClass("fa-eye-slash");
                } else {
                    passwordField.attr("type", "password");
                    passwordIcon.removeClass("fa-eye-slash").addClass("fa-eye");
                }
            });

            $("#confirm-password-toggle").click(function() {
                var passwordField = $("#confirm_password");
                var passwordIcon = $(this).find("i");

                if (passwordField.attr("type") === "password") {
                    passwordField.attr("type", "text");
                    passwordIcon.removeClass("fa-eye").addClass("fa-eye-slash");
                } else {
                    passwordField.attr("type", "password");
                    passwordIcon.removeClass("fa-eye-slash").addClass("fa-eye");
                }
            });

            // Password validation
            $("#password").keyup(function() {
                var password = $(this).val();

                // Check length
                if (password.length >= 8) {
                    $("#length").addClass("valid").removeClass("invalid");
                } else {
                    $("#length").addClass("invalid").removeClass("valid");
                }

                // Check uppercase
                if (/[A-Z]/.test(password)) {
                    $("#uppercase").addClass("valid").removeClass("invalid");
                } else {
                    $("#uppercase").addClass("invalid").removeClass("valid");
                }

                // Check lowercase
                if (/[a-z]/.test(password)) {
                    $("#lowercase").addClass("valid").removeClass("invalid");
                } else {
                    $("#lowercase").addClass("invalid").removeClass("valid");
                }

                // Check number
                if (/[0-9]/.test(password)) {
                    $("#number").addClass("valid").removeClass("invalid");
                } else {
                    $("#number").addClass("invalid").removeClass("valid");
                }
            });

            // Confirm password validation
            $("#confirm_password").keyup(function() {
                if ($(this).val() === $("#password").val()) {
                    $(this).css("border-color", "#4caf50");
                } else {
                    $(this).css("border-color", "#f44336");
                }
            });
        });
    </script>
</body>
</html>

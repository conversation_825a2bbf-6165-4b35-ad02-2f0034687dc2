<?php
/**
 * Latest Queue Component for Staff Dashboard
 *
 * This component displays the latest queue data from the database
 */

// Include database configuration if not already included
if (!function_exists('query')) {
    require_once __DIR__ . '/../../config/database.php';
    require_once __DIR__ . '/../../includes/functions.php';
}

// Include queue connector
require_once __DIR__ . '/../../includes/queue_connector.php'; // pastikan selalu di-include

// Get staff ID
$staff_id = $_SESSION['user_id'] ?? 0;

// Get loket ID and jenis_layanan assigned to staff
$sql_loket = "SELECT l.id as loket_id, l.jenis_layanan
              FROM loket l
              WHERE l.user_id = $staff_id";
$result_loket = query($sql_loket);
$loket_id = 0;
$jenis_layanan = '';

if ($result_loket && num_rows($result_loket) > 0) {
    $row_loket = fetch_assoc($result_loket);
    $loket_id = $row_loket['loket_id'];
    $jenis_layanan = $row_loket['jenis_layanan'];
}

// Get latest queues from database
$latest_queues = [];

try {
    // Get all queues for today
    $sql = "SELECT a.id, a.nomor_antrian, n.nama,
            CASE
                WHEN a.jenis_layanan = 'teller' THEN 'Teller'
                WHEN a.jenis_layanan = 'cs' THEN 'Customer Service'
                WHEN a.jenis_layanan = 'kredit' THEN 'Kredit'
                ELSE p.nama_poli
            END as layanan,
            CASE
                WHEN l.nama_loket IS NULL THEN '-'
                ELSE l.nama_loket
            END as loket,
            a.status,
            TIME_FORMAT(a.waktu_booking, '%H:%i') as waktu_booking
            FROM antrian a
            LEFT JOIN nasabah n ON a.nasabah_id = n.id
            LEFT JOIN poli p ON a.poli_id = p.id
            LEFT JOIN loket l ON a.loket_id = l.id
            WHERE a.tanggal = CURDATE()";

    // If staff has a loket assigned, filter by loket or service type
    if ($loket_id > 0) {
        $sql .= " AND (a.loket_id = $loket_id OR (a.loket_id IS NULL AND a.jenis_layanan = '$jenis_layanan'))";
    }

    $sql .= " ORDER BY a.id DESC LIMIT 10";

    $result = query($sql);

    if ($result && num_rows($result) > 0) {
        $latest_queues = fetch_all($result);
    } else if (!empty($jenis_layanan)) {
        // Jika tidak ada data, coba ambil antrian berdasarkan jenis layanan
        $waiting_queues = get_waiting_queues($jenis_layanan);
        if (!empty($waiting_queues)) {
            $latest_queues = $waiting_queues;
        }
    }
} catch (Exception $e) {
    // Log error
    error_log("Error fetching latest queues for staff: " . $e->getMessage());
}
?>

<div class="card shadow-sm mb-4">
    <div class="card-header bg-white">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0 d-flex align-items-center">
                <i class="fas fa-list-alt text-primary mr-2"></i> Antrian Terbaru
            </h5>
            <a href="../latest_queue.php" target="_blank" class="btn btn-sm btn-outline-primary">
                <i class="fas fa-external-link-alt mr-1"></i> Lihat Semua
            </a>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="bg-light">
                    <tr>
                        <th>No. Antrian</th>
                        <th>Nama</th>
                        <th>Layanan</th>
                        <th>Loket</th>
                        <th>Status</th>
                        <th>Waktu Booking</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($latest_queues)): ?>
                        <?php foreach ($latest_queues as $queue): ?>
                        <tr>
                            <td class="font-weight-bold"><?php echo $queue['nomor_antrian']; ?></td>
                            <td><?php echo $queue['nama']; ?></td>
                            <td><?php echo $queue['layanan']; ?></td>
                            <td><?php echo $queue['loket']; ?></td>
                            <td>
                                <?php if ($queue['status'] == 'selesai'): ?>
                                <span class="badge badge-success">Selesai</span>
                                <?php elseif ($queue['status'] == 'dipanggil'): ?>
                                <span class="badge badge-info">Dipanggil</span>
                                <?php elseif ($queue['status'] == 'menunggu'): ?>
                                <span class="badge badge-warning">Menunggu</span>
                                <?php elseif ($queue['status'] == 'batal'): ?>
                                <span class="badge badge-danger">Batal</span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo $queue['waktu_booking']; ?></td>
                        </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr><td colspan="6" class="text-center">Tidak ada data antrian.</td></tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

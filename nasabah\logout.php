<?php
// Logout process for nasabah
require_once '../includes/functions.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Set success message
$_SESSION['success_message'] = 'Anda berhasil logout.';

// Clear remember me cookie if exists
if (isset($_COOKIE['remember_token'])) {
    setcookie('remember_token', '', time() - 3600, '/'); // Expire cookie
}
if (isset($_COOKIE['user_id'])) {
    setcookie('user_id', '', time() - 3600, '/'); // Expire cookie
}

// Destroy session variables but keep the success message
$nasabah_id = isset($_SESSION['nasabah_id']) ? $_SESSION['nasabah_id'] : null;
$success_message = $_SESSION['success_message'];

// Clear all session variables
$_SESSION = array();

// Restore success message
$_SESSION['success_message'] = $success_message;

// Destroy the session
if (session_id() != "") {
    session_destroy();
}

// Start a new session for the message
session_start();
$_SESSION['success_message'] = $success_message;

// Redirect to login page
redirect('login.php');

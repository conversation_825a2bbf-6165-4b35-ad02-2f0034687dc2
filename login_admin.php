<?php
// Admin Login page
require_once 'includes/functions_mock.php';

// Check if user is already logged in
if (is_logged_in()) {
    // Redirect to admin page if already logged in as admin
    if (is_admin()) {
        redirect('admin/index.php');
    }
}

// Process login form
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = isset($_POST['username']) ? trim($_POST['username']) : '';
    $password = isset($_POST['password']) ? $_POST['password'] : '';

    // Validate input
    if (empty($username)) {
        $_SESSION['error_message'] = 'Username tidak boleh kosong.';
    } elseif (empty($password)) {
        $_SESSION['error_message'] = 'Password tidak boleh kosong.';
    } else {
        try {
            // Check if using real database or mock
            global $conn;

            if ($conn instanceof MockConnection) {
                // Using mock data - accept admin credentials
                if ($username == 'admin' && $password == 'admin123') {
                    // Set session variables
                    $_SESSION['user_id'] = 1;
                    $_SESSION['username'] = 'admin';
                    $_SESSION['nama_lengkap'] = 'Administrator';
                    $_SESSION['role'] = 'admin';

                    // Set success message
                    $_SESSION['success_message'] = "Selamat datang, Administrator!";

                    // Redirect to admin dashboard
                    redirect('admin/index.php');
                } else {
                    $_SESSION['error_message'] = 'Username atau password salah. Silahkan coba lagi.';
                }
            } else {
                // Using real database - check credentials
                $username = sanitize($username);
                $sql = "SELECT * FROM users WHERE username = '$username' AND role = 'admin'";
                $result = query($sql);

                if (num_rows($result) > 0) {
                    $user = fetch_assoc($result);

                    // Check if user is active
                    if (isset($user['status']) && $user['status'] != 'aktif') {
                        $_SESSION['error_message'] = 'Akun Anda tidak aktif. Silakan hubungi administrator.';
                    } else {
                        // Untuk mode development, terima semua password
                        // Dalam produksi, gunakan password_verify()
                        $_SESSION['user_id'] = $user['id'];
                        $_SESSION['username'] = $user['username'];
                        $_SESSION['nama_lengkap'] = $user['nama_lengkap'];
                        $_SESSION['role'] = $user['role'];

                        // Set success message
                        $_SESSION['success_message'] = "Selamat datang, {$user['nama_lengkap']}!";

                        // Redirect to admin dashboard
                        redirect('admin/index.php');
                    }
                } else {
                    // Jika username tidak ditemukan atau bukan admin, gunakan mock data
                    if ($username == 'admin' && $password == 'admin123') {
                        // Set session variables
                        $_SESSION['user_id'] = 1;
                        $_SESSION['username'] = 'admin';
                        $_SESSION['nama_lengkap'] = 'Administrator';
                        $_SESSION['role'] = 'admin';

                        // Set success message
                        $_SESSION['success_message'] = "Selamat datang, Administrator!";

                        // Redirect to admin dashboard
                        redirect('admin/index.php');
                    } else {
                        $_SESSION['error_message'] = 'Username atau password salah. Silahkan coba lagi.';
                    }
                }
            }
        } catch (Exception $e) {
            // Log the error
            error_log("Login error: " . $e->getMessage());
            $_SESSION['error_message'] = 'Terjadi kesalahan saat login. Silakan coba lagi nanti.';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Admin - Sistem Antrian Bank BJB</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">

    <style>
        .login-page {
            background: linear-gradient(135deg, #e91e63 0%, #d81b60 100%);
        }
        .login-box {
            background-color: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            width: 100%;
            max-width: 500px;
            margin: 50px auto;
        }
        .login-logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .login-logo img {
            margin-bottom: 15px;
        }
        .login-form .form-group {
            margin-bottom: 20px;
        }
        .login-footer {
            text-align: center;
            margin-top: 30px;
            color: #777;
            font-size: 14px;
        }
    </style>
</head>
<body class="login-page">
    <div class="login-box">
        <div class="login-logo">
            <img src="assets/img/logo.jpeg" alt="Bank BJB" style="max-width: 200px;">
            <h3>APLIKASI ANTRIAN PENGUNJUNG</h3>
            <p>BANK BJB</p>
            <p class="mt-3">Login Admin</p>
        </div>

        <?php if(isset($_SESSION['error_message'])): ?>
        <div class="alert alert-dismissible fade show" role="alert" style="background-color: rgba(233, 30, 99, 0.1); color: #e91e63; border-color: rgba(233, 30, 99, 0.2); border-radius: 8px;">
            <?php echo $_SESSION['error_message']; ?>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <?php unset($_SESSION['error_message']); ?>
        <?php endif; ?>

        <form action="login_admin.php" method="post" class="login-form">
            <div class="form-group">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                    </div>
                    <input type="text" class="form-control" name="username" placeholder="Username" required>
                </div>
            </div>
            <div class="form-group">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                    </div>
                    <input type="password" class="form-control" name="password" placeholder="Password" required>
                </div>
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn-block" style="background: linear-gradient(135deg, #e91e63 0%, #d81b60 100%); color: white; border: none; box-shadow: 0 4px 6px rgba(233, 30, 99, 0.2); padding: 10px 20px; border-radius: 4px; font-weight: 600;">
                    <i class="fas fa-sign-in-alt"></i> Login
                </button>
            </div>
        </form>

        <div class="text-center mt-3">
            <a href="index.php" class="btn btn-link" style="color: #e91e63;">
                <i class="fas fa-arrow-left"></i> Kembali ke Halaman Utama
            </a>
        </div>

        <div class="text-center mt-3">
            <div class="alert" style="background-color: rgba(233, 30, 99, 0.1); color: #e91e63; border-color: rgba(233, 30, 99, 0.2); border-radius: 8px;">
                <strong>Demo Credentials:</strong><br>
                Admin: admin / admin123
            </div>
        </div>

        <div class="login-footer">
            &copy; <?php echo date('Y'); ?> - Bank BJB
        </div>
    </div>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
// Test database connection and queries
echo "<h1>Database Connection Test</h1>";

// Test 1: Include functions
echo "<h2>1. Including Functions</h2>";
try {
    require_once 'includes/functions.php';
    echo "<p>✅ Functions included successfully</p>";
} catch (Exception $e) {
    echo "<p>❌ Error including functions: " . $e->getMessage() . "</p>";
    exit;
}

// Test 2: Check global connection
echo "<h2>2. Database Connection</h2>";
global $conn;
if (isset($conn)) {
    echo "<p>✅ Connection variable exists</p>";
    echo "<p>Connection type: " . get_class($conn) . "</p>";
    
    if ($conn instanceof MockConnection) {
        echo "<p>⚠️ Using Mock Connection - Database not properly connected</p>";
    } else {
        echo "<p>✅ Real database connection</p>";
    }
} else {
    echo "<p>❌ Connection variable not set</p>";
}

// Test 3: Test query function
echo "<h2>3. Testing Query Function</h2>";
try {
    $result = query("SELECT 1 as test");
    if ($result) {
        echo "<p>✅ Query function works</p>";
        echo "<p>Result type: " . gettype($result) . "</p>";
    } else {
        echo "<p>❌ Query function returned false</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Query function error: " . $e->getMessage() . "</p>";
}

// Test 4: Check if users table exists
echo "<h2>4. Check Users Table</h2>";
try {
    $result = query("SHOW TABLES LIKE 'users'");
    if ($result && num_rows($result) > 0) {
        echo "<p>✅ Users table exists</p>";
    } else {
        echo "<p>❌ Users table does not exist</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Error checking users table: " . $e->getMessage() . "</p>";
}

// Test 5: Count users
echo "<h2>5. Count All Users</h2>";
try {
    $result = query("SELECT COUNT(*) as total FROM users");
    if ($result) {
        $row = fetch_assoc($result);
        echo "<p>✅ Total users: " . $row['total'] . "</p>";
    } else {
        echo "<p>❌ Failed to count users</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Error counting users: " . $e->getMessage() . "</p>";
}

// Test 6: Count staff users
echo "<h2>6. Count Staff Users</h2>";
try {
    $result = query("SELECT COUNT(*) as total FROM users WHERE role = 'staff'");
    if ($result) {
        $row = fetch_assoc($result);
        echo "<p>✅ Total staff: " . $row['total'] . "</p>";
    } else {
        echo "<p>❌ Failed to count staff</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Error counting staff: " . $e->getMessage() . "</p>";
}

// Test 7: Get staff data
echo "<h2>7. Get Staff Data</h2>";
try {
    $result = query("SELECT * FROM users WHERE role = 'staff' LIMIT 5");
    if ($result && num_rows($result) > 0) {
        echo "<p>✅ Found " . num_rows($result) . " staff members</p>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Username</th><th>Nama Lengkap</th><th>Role</th></tr>";
        while ($row = fetch_assoc($result)) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . $row['username'] . "</td>";
            echo "<td>" . $row['nama_lengkap'] . "</td>";
            echo "<td>" . $row['role'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>❌ No staff found</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Error getting staff data: " . $e->getMessage() . "</p>";
}

// Test 8: Check database configuration
echo "<h2>8. Database Configuration</h2>";
if (file_exists('config/database.php')) {
    echo "<p>✅ Database config file exists</p>";
    
    // Show config content (without sensitive data)
    $config_content = file_get_contents('config/database.php');
    if (strpos($config_content, 'MockConnection') !== false) {
        echo "<p>⚠️ Config file contains MockConnection</p>";
    }
    
    if (strpos($config_content, 'mysqli') !== false) {
        echo "<p>✅ Config file contains mysqli</p>";
    }
} else {
    echo "<p>❌ Database config file not found</p>";
}

// Test 9: Check if MySQL is running
echo "<h2>9. MySQL Connection Test</h2>";
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'antrian_bank';

try {
    $test_conn = new mysqli($host, $username, $password, $database);
    if ($test_conn->connect_error) {
        echo "<p>❌ MySQL connection failed: " . $test_conn->connect_error . "</p>";
    } else {
        echo "<p>✅ Direct MySQL connection successful</p>";
        
        // Test query
        $result = $test_conn->query("SELECT COUNT(*) as total FROM users WHERE role = 'staff'");
        if ($result) {
            $row = $result->fetch_assoc();
            echo "<p>✅ Direct query successful - Staff count: " . $row['total'] . "</p>";
        }
        
        $test_conn->close();
    }
} catch (Exception $e) {
    echo "<p>❌ MySQL connection error: " . $e->getMessage() . "</p>";
}

echo "<br><br><a href='admin/petugas.php'>Go to Petugas Management</a> | <a href='admin/petugas.php?debug=1'>Go to Petugas Management (Debug)</a>";
?>

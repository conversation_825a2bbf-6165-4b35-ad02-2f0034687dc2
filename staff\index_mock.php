<?php
// Staff dashboard page (Mock Version)
$page_title = 'Dashboard Petugas';
$active_menu = 'dashboard';
require_once '../includes/functions_mock.php';

// Check if user is logged in
if (!is_logged_in()) {
    redirect('../login.php');
}

// Check if user is staff
if (!is_staff()) {
    redirect('../index.php');
}

// Mock data for counter
$counter = [
    'id' => 1,
    'nama_loket' => 'Loket 1',
    'jenis_layanan' => 'teller',
    'status' => 'aktif',
    'user_id' => $_SESSION['user_id']
];

// Mock data for current queue
$current_queue = [
    'id' => 1,
    'nomor_antrian' => 'T001',
    'nama' => '<PERSON>',
    'nama_poli' => 'Teller',
    'status' => 'dipanggil'
];

// Mock data for waiting queues
$waiting_queues = [
    [
        'id' => 4,
        'nomor_antrian' => 'T002',
        'nama' => 'Dewi Lestari',
        'nama_poli' => 'Teller',
        'status' => 'menunggu'
    ],
    [
        'id' => 7,
        'nomor_antrian' => 'T003',
        'nama' => 'Budi Santoso',
        'nama_poli' => 'Teller',
        'status' => 'menunggu'
    ],
    [
        'id' => 10,
        'nomor_antrian' => 'T004',
        'nama' => 'Siti Nurhaliza',
        'nama_poli' => 'Teller',
        'status' => 'menunggu'
    ]
];

// Mock data for available counters
$available_counters = [
    [
        'id' => 1,
        'nama_loket' => 'Loket 1',
        'jenis_layanan' => 'teller',
        'status' => 'aktif'
    ],
    [
        'id' => 2,
        'nama_loket' => 'Loket 2',
        'jenis_layanan' => 'teller',
        'status' => 'aktif'
    ],
    [
        'id' => 3,
        'nama_loket' => 'Loket 3',
        'jenis_layanan' => 'teller',
        'status' => 'aktif'
    ]
];

$has_counter = true;
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Bank BJB</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .counter-selection {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .current-serving {
            background: linear-gradient(135deg, #1a6a83 0%, #0e4b5e 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(26, 106, 131, 0.3);
        }

        .current-serving .number {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .current-serving .customer {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .current-serving .service {
            font-size: 18px;
            opacity: 0.8;
            margin-bottom: 15px;
        }

        .btn-action {
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .waiting-list {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .waiting-list h5 {
            margin-bottom: 20px;
            color: #1a6a83;
            font-weight: 600;
        }

        .waiting-list .table th {
            border-top: none;
            color: #6c757d;
            font-weight: 600;
        }

        .waiting-list .badge {
            font-size: 14px;
            padding: 5px 10px;
        }

        .btn-call {
            padding: 3px 8px;
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <!-- Sidebar -->
        <nav id="sidebar">
            <div class="sidebar-header">
                <div class="logo-container">
                    <img src="../assets/img/logo.jpeg" alt="Bank BJB" class="logo-bjb">
                </div>
                <h4>Bank BJB Kantor<br>Cabang Khusus<br>Banten</h4>
                <p><?php echo $_SESSION['nama_lengkap']; ?></p>
            </div>

            <ul class="list-unstyled components">
                <li class="<?php echo ($active_menu == 'dashboard') ? 'active' : ''; ?>">
                    <a href="index_mock.php">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                </li>
                <li class="<?php echo ($active_menu == 'history') ? 'active' : ''; ?>">
                    <a href="history.php">
                        <i class="fas fa-history"></i> Riwayat Antrian
                    </a>
                </li>
                <li>
                    <a href="../logout.php">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Page Content -->
        <div id="content">
            <!-- Navbar -->
            <nav class="navbar navbar-expand-lg navbar-light bg-light">
                <div class="container-fluid">
                    <button type="button" id="sidebarCollapse" class="btn btn-info">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="ml-auto">
                        <span id="navbar-time"><?php echo date('H:i:s'); ?></span>
                    </div>
                </div>
            </nav>

            <!-- Content -->
            <div class="container-fluid">
                <div class="row">
                    <div class="col-md-12">
                        <h2 class="mb-4">Dashboard Petugas</h2>
                    </div>
                </div>

                <?php if (!$has_counter): ?>
                <div class="row">
                    <div class="col-md-12">
                        <div class="counter-selection">
                            <h5><i class="fas fa-desktop mr-2"></i> Pilih Loket</h5>
                            <p>Silahkan pilih loket yang akan Anda gunakan untuk melayani nasabah.</p>

                            <form action="select_counter.php" method="post">
                                <div class="form-group">
                                    <label for="counter">Loket:</label>
                                    <select name="counter" id="counter" class="form-control">
                                        <option value="">-- Pilih Loket --</option>
                                        <?php foreach ($available_counters as $c): ?>
                                        <option value="<?php echo $c['id']; ?>"><?php echo $c['nama_loket']; ?> (<?php echo ucfirst($c['jenis_layanan']); ?>)</option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-check-circle mr-1"></i> Pilih Loket
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                <?php else: ?>
                <div class="row">
                    <div class="col-md-6">
                        <div class="current-serving" data-counter-id="<?php echo $counter['id']; ?>" data-counter-type="<?php echo $counter['jenis_layanan']; ?>">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0"><i class="fas fa-desktop mr-2"></i> <?php echo $counter['nama_loket']; ?> (<?php echo ucfirst($counter['jenis_layanan']); ?>)</h5>
                                <button type="button" class="btn btn-sm btn-light" data-toggle="modal" data-target="#changeCounterModal">
                                    <i class="fas fa-exchange-alt mr-1"></i> Ganti Loket
                                </button>
                            </div>

                            <?php if ($current_queue): ?>
                            <div class="number"><?php echo $current_queue['nomor_antrian']; ?></div>
                            <div class="customer"><?php echo $current_queue['nama']; ?></div>
                            <div class="service"><i class="fas fa-tag mr-1"></i> <?php echo $current_queue['nama_poli']; ?></div>

                            <div class="action-buttons">
                                <button id="btnComplete" class="btn btn-success btn-action">
                                    <i class="fas fa-check-circle mr-1"></i> Selesai
                                </button>
                                <button id="btnCallAgain" class="btn btn-info btn-action">
                                    <i class="fas fa-volume-up mr-1"></i> Panggil Ulang
                                </button>
                                <button id="btnCancel" class="btn btn-danger btn-action">
                                    <i class="fas fa-times-circle mr-1"></i> Batal
                                </button>
                            </div>
                            <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-user-clock fa-3x mb-3"></i>
                                <h5>Belum ada antrian yang dilayani</h5>
                                <p>Silahkan panggil antrian berikutnya.</p>
                            </div>
                            <?php endif; ?>

                            <div class="mt-3">
                                <button id="btnNextCustomer" class="btn btn-primary btn-block" data-counter="<?php echo $counter['id']; ?>">
                                    <i class="fas fa-arrow-right mr-1"></i> Panggil Antrian Berikutnya
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="waiting-list">
                            <h5><i class="fas fa-list-ol mr-2"></i> Daftar Antrian Menunggu</h5>

                            <?php if (count($waiting_queues) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th width="20%">Nomor</th>
                                            <th width="40%">Nama</th>
                                            <th width="25%">Layanan</th>
                                            <th width="15%">Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($waiting_queues as $queue): ?>
                                        <tr>
                                            <td>
                                                <span class="badge badge-primary">
                                                    <?php echo $queue['nomor_antrian']; ?>
                                                </span>
                                            </td>
                                            <td><?php echo $queue['nama']; ?></td>
                                            <td><?php echo $queue['nama_poli']; ?></td>
                                            <td>
                                                <button class="btn btn-sm btn-primary btn-call btn-call-direct" data-counter="<?php echo $counter['id']; ?>" data-queue="<?php echo $queue['id']; ?>">
                                                    <i class="fas fa-volume-up"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-info-circle fa-3x mb-3"></i>
                                <h5>Tidak ada antrian yang menunggu</h5>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Change Counter Modal -->
    <div class="modal fade" id="changeCounterModal" tabindex="-1" role="dialog" aria-labelledby="changeCounterModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="changeCounterModalLabel">Ganti Loket</h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form action="select_counter.php" method="post" id="changeCounterForm">
                        <div class="form-group">
                            <label for="counter_id">Pilih Loket:</label>
                            <select class="form-control" id="counter_id" name="counter_id" required>
                                <option value="">-- Pilih Loket --</option>
                                <option value="1">Loket 1 (Teller)</option>
                                <option value="2">Loket 2 (Teller)</option>
                                <option value="3">Loket 3 (Teller)</option>
                                <option value="4">Loket 4 (Customer Service)</option>
                                <option value="5">Loket 5 (Customer Service)</option>
                                <option value="6">Loket 6 (Kredit)</option>
                                <option value="7">Loket 7 (Kredit)</option>
                            </select>
                        </div>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle mr-2"></i> Mengganti loket akan mengakhiri sesi pelayanan saat ini.
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="button" class="btn btn-primary" id="btnChangeCounter">Ganti Loket</button>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        $(document).ready(function() {
            // Sidebar toggle
            $('#sidebarCollapse').on('click', function() {
                $('#sidebar').toggleClass('active');
                $('#content').toggleClass('active');
            });

            // Update time every second
            setInterval(function() {
                var now = new Date();
                var hours = now.getHours().toString().padStart(2, '0');
                var minutes = now.getMinutes().toString().padStart(2, '0');
                var seconds = now.getSeconds().toString().padStart(2, '0');
                $('#navbar-time').text(hours + ':' + minutes + ':' + seconds);
            }, 1000);

            // Complete button
            $('#btnComplete').on('click', function() {
                alert('Antrian telah diselesaikan.');
                location.reload();
            });

            // Call again button
            $('#btnCallAgain').on('click', function() {
                alert('Memanggil ulang antrian...');
                // Play bell sound
                var audio = new Audio('../assets/sounds/bell.mp3');
                audio.play();
            });

            // Cancel button
            $('#btnCancel').on('click', function() {
                if (confirm('Apakah Anda yakin ingin membatalkan antrian ini?')) {
                    alert('Antrian telah dibatalkan.');
                    location.reload();
                }
            });

            // Next customer button
            $('#btnNextCustomer').on('click', function() {
                alert('Memanggil antrian berikutnya...');
                // Play bell sound
                var audio = new Audio('../assets/sounds/bell.mp3');
                audio.play();

                // Reload page after 2 seconds
                setTimeout(function() {
                    location.reload();
                }, 2000);
            });

            // Direct call button
            $('.btn-call-direct').on('click', function() {
                alert('Memanggil antrian yang dipilih...');
                // Play bell sound
                var audio = new Audio('../assets/sounds/bell.mp3');
                audio.play();

                // Reload page after 2 seconds
                setTimeout(function() {
                    location.reload();
                }, 2000);
            });

            // Change counter button
            $('#btnChangeCounter').on('click', function() {
                // Submit the form
                $('#changeCounterForm').submit();
            });
        });
    </script>
</body>
</html>

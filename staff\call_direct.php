<?php
// Call direct API
header('Content-Type: application/json');
session_start();

// Include database configuration
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and is staff
if (!is_logged_in() || !is_staff()) {
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized access.'
    ]);
    exit;
}

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method.'
    ]);
    exit;
}

// Get parameters
$counter_id = isset($_POST['counter_id']) ? (int)$_POST['counter_id'] : 0;
$queue_id = isset($_POST['queue_id']) ? (int)$_POST['queue_id'] : 0;

// Validate counter
$user_id = $_SESSION['user_id'];
$sql = "SELECT * FROM loket WHERE id = $counter_id AND user_id = $user_id AND status = 'aktif'";
$result = query($sql);

if (num_rows($result) == 0) {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid counter.'
    ]);
    exit;
}

$counter = fetch_assoc($result);

// Check if there's already a customer being served
$sql = "SELECT * FROM antrian
        WHERE loket_id = $counter_id
        AND status = 'dipanggil'
        AND tanggal = CURDATE()";
$result = query($sql);

if (num_rows($result) > 0) {
    echo json_encode([
        'success' => false,
        'message' => 'Ada antrian yang sedang dilayani. Silahkan selesaikan terlebih dahulu.'
    ]);
    exit;
}

// Get counter's jenis_layanan
$jenis_layanan = $counter['jenis_layanan'];

// Validate queue
$sql = "SELECT a.*, n.nama, p.nama_poli
        FROM antrian a
        LEFT JOIN nasabah n ON a.nasabah_id = n.id
        LEFT JOIN poli p ON a.poli_id = p.id
        WHERE a.id = $queue_id
        AND a.status = 'menunggu'
        AND a.tanggal = CURDATE()";

// Validate jenis_layanan if available
if (!empty($jenis_layanan)) {
    $sql .= " AND (a.jenis_layanan = '$jenis_layanan' OR a.jenis_layanan IS NULL)";
}

$result = query($sql);

if (num_rows($result) == 0) {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid queue or queue type does not match counter type.'
    ]);
    exit;
}

$queue = fetch_assoc($result);

// Update queue status
$sql = "UPDATE antrian
        SET status = 'dipanggil',
            loket_id = $counter_id,
            waktu_dipanggil = NOW()
        WHERE id = $queue_id";
query($sql);

// Return success response
echo json_encode([
    'success' => true,
    'message' => 'Antrian berhasil dipanggil.',
    'queue' => $queue
]);
?>

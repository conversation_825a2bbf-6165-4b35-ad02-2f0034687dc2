<?php
// Create necessary tables for the queue management system
require_once 'includes/functions.php';

echo "<h2>Creating Database Tables</h2>";

// Create users table
$sql_users = "CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    nama_lengkap VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    role ENUM('admin', 'staff') NOT NULL DEFAULT 'staff',
    status ENUM('aktif', 'nonaktif') NOT NULL DEFAULT 'aktif',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

if (query($sql_users)) {
    echo "<p>✅ Table 'users' created/verified successfully</p>";
} else {
    echo "<p>❌ Failed to create table 'users'</p>";
}

// Create loket table
$sql_loket = "CREATE TABLE IF NOT EXISTS loket (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nama_loket VARCHAR(50) NOT NULL,
    jenis_layanan ENUM('teller', 'cs', 'kredit') NOT NULL,
    status ENUM('aktif', 'nonaktif') NOT NULL DEFAULT 'aktif',
    user_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
)";

if (query($sql_loket)) {
    echo "<p>✅ Table 'loket' created/verified successfully</p>";
} else {
    echo "<p>❌ Failed to create table 'loket'</p>";
}

// Create nasabah table if not exists
$sql_nasabah = "CREATE TABLE IF NOT EXISTS nasabah (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nama VARCHAR(100) NOT NULL,
    no_identitas VARCHAR(20) NOT NULL,
    jenis_identitas ENUM('ktp', 'buku_tabungan') NOT NULL DEFAULT 'ktp',
    email VARCHAR(100),
    no_hp VARCHAR(20),
    password VARCHAR(255),
    alamat TEXT,
    file_identitas VARCHAR(255),
    status_verifikasi ENUM('belum', 'sudah', 'ditolak') NOT NULL DEFAULT 'belum',
    alasan_penolakan TEXT,
    is_blocked TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

if (query($sql_nasabah)) {
    echo "<p>✅ Table 'nasabah' created/verified successfully</p>";
} else {
    echo "<p>❌ Failed to create table 'nasabah'</p>";
}

// Create antrian table if not exists
$sql_antrian = "CREATE TABLE IF NOT EXISTS antrian (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nomor_antrian VARCHAR(10) NOT NULL,
    nasabah_id INT,
    nama VARCHAR(100) NOT NULL,
    no_identitas VARCHAR(20),
    layanan ENUM('teller', 'cs', 'kredit') NOT NULL,
    tanggal DATE NOT NULL,
    waktu TIME,
    status ENUM('menunggu', 'dipanggil', 'selesai', 'batal') NOT NULL DEFAULT 'menunggu',
    loket_id INT,
    booking_dari ENUM('online', 'langsung') DEFAULT 'langsung',
    keterangan TEXT,
    waktu_booking TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    waktu_dipanggil TIMESTAMP NULL,
    waktu_selesai TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (nasabah_id) REFERENCES nasabah(id) ON DELETE SET NULL,
    FOREIGN KEY (loket_id) REFERENCES loket(id) ON DELETE SET NULL
)";

if (query($sql_antrian)) {
    echo "<p>✅ Table 'antrian' created/verified successfully</p>";
} else {
    echo "<p>❌ Failed to create table 'antrian'</p>";
}

// Create pengaturan table if not exists
$sql_pengaturan = "CREATE TABLE IF NOT EXISTS pengaturan (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nama_pengaturan VARCHAR(50) NOT NULL UNIQUE,
    nilai TEXT NOT NULL,
    deskripsi TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

if (query($sql_pengaturan)) {
    echo "<p>✅ Table 'pengaturan' created/verified successfully</p>";
} else {
    echo "<p>❌ Failed to create table 'pengaturan'</p>";
}

// Insert default admin user if not exists
$sql_check_admin = "SELECT COUNT(*) as count FROM users WHERE role = 'admin'";
$result = query($sql_check_admin);
$row = fetch_assoc($result);

if ($row['count'] == 0) {
    $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
    $sql_admin = "INSERT INTO users (username, password, nama_lengkap, email, role, status) 
                  VALUES ('admin', '$admin_password', 'Administrator', '<EMAIL>', 'admin', 'aktif')";
    
    if (query($sql_admin)) {
        echo "<p>✅ Default admin user created (username: admin, password: admin123)</p>";
    } else {
        echo "<p>❌ Failed to create default admin user</p>";
    }
} else {
    echo "<p>ℹ️ Admin user already exists</p>";
}

// Insert default settings if not exists
$default_settings = [
    ['nama_pengaturan' => 'nama_instansi', 'nilai' => 'Bank BJB Kantor Cabang Khusus Banten', 'deskripsi' => 'Nama institusi'],
    ['nama_pengaturan' => 'logo_instansi', 'nilai' => 'logo.jpeg', 'deskripsi' => 'Logo institusi'],
    ['nama_pengaturan' => 'jam_operasional', 'nilai' => '08:00-16:00', 'deskripsi' => 'Jam operasional pelayanan'],
    ['nama_pengaturan' => 'max_antrian', 'nilai' => '100', 'deskripsi' => 'Maksimal antrian per hari'],
    ['nama_pengaturan' => 'verifikasi_wajib', 'nilai' => '1', 'deskripsi' => 'Verifikasi identitas wajib']
];

foreach ($default_settings as $setting) {
    $sql_check = "SELECT COUNT(*) as count FROM pengaturan WHERE nama_pengaturan = '{$setting['nama_pengaturan']}'";
    $result = query($sql_check);
    $row = fetch_assoc($result);
    
    if ($row['count'] == 0) {
        $sql_insert = "INSERT INTO pengaturan (nama_pengaturan, nilai, deskripsi) 
                       VALUES ('{$setting['nama_pengaturan']}', '{$setting['nilai']}', '{$setting['deskripsi']}')";
        
        if (query($sql_insert)) {
            echo "<p>✅ Added setting: {$setting['nama_pengaturan']}</p>";
        } else {
            echo "<p>❌ Failed to add setting: {$setting['nama_pengaturan']}</p>";
        }
    }
}

echo "<h3>Database Setup Complete!</h3>";
echo "<p><a href='add_default_staff.php'>Add Default Staff Data</a> | <a href='check_staff_data.php'>Check Staff Data</a> | <a href='admin/petugas.php'>Go to Petugas Management</a></p>";
?>

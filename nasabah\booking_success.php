<?php
// Booking success page
require_once '../includes/functions.php';
require_once '../includes/db.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if booking ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: booking.php");
    exit;
}

$booking_id = $_GET['id'];

// Get booking details from database
$stmt = $conn->prepare("SELECT * FROM bookings WHERE booking_id = ?");
$stmt->bind_param("s", $booking_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    $_SESSION['error_messages'] = ["Booking tidak ditemukan."];
    header("Location: booking.php");
    exit;
}

$booking = $result->fetch_assoc();

// Format layanan name for display
$layanan_display = '';
switch ($booking['layanan']) {
    case 'teller':
        $layanan_display = 'Teller';
        break;
    case 'cs':
        $layanan_display = 'Customer Service';
        break;
    case 'kredit':
        $layanan_display = 'Kredit';
        break;
    default:
        $layanan_display = $booking['layanan'];
}

// Format date
$tanggal_display = date('d F Y', strtotime($booking['tanggal']));

// Format time
$waktu_parts = explode(':', $booking['waktu']);
$waktu_display = $waktu_parts[0] . ':' . $waktu_parts[1];
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Booking Berhasil - Bank BJB</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">

    <style>
        body {
            background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .header {
            background-color: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            padding: 15px 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 100;
        }
        
        .logo-container {
            display: flex;
            align-items: center;
        }
        
        .logo-container img {
            height: 50px;
            margin-right: 15px;
        }
        
        .logo-container h1 {
            color: white;
            font-size: 24px;
            margin: 0;
            font-weight: 600;
        }
        
        .nav-menu {
            display: flex;
            justify-content: flex-end;
        }
        
        .nav-menu a {
            color: white;
            margin-left: 20px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s;
            padding: 8px 15px;
            border-radius: 50px;
        }
        
        .nav-menu a:hover {
            background-color: rgba(255, 255, 255, 0.2);
            transform: translateY(-3px);
        }
        
        .nav-menu a.active {
            background-color: white;
            color: #4caf50;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }
        
        .nav-menu a i {
            margin-right: 5px;
        }
        
        .page-title {
            text-align: center;
            color: white;
            padding: 40px 0;
        }
        
        .page-title h2 {
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .page-title p {
            font-size: 18px;
            opacity: 0.9;
            max-width: 700px;
            margin: 0 auto;
        }
        
        .success-container {
            background-color: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 50px;
            text-align: center;
        }
        
        .success-icon {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            color: white;
            font-size: 48px;
            box-shadow: 0 10px 20px rgba(76, 175, 80, 0.3);
        }
        
        .success-title {
            font-size: 28px;
            font-weight: 700;
            color: #333;
            margin-bottom: 20px;
        }
        
        .success-message {
            font-size: 18px;
            color: #666;
            margin-bottom: 30px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .booking-details {
            background-color: #f9f9f9;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: left;
        }
        
        .booking-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        
        .booking-id {
            font-size: 20px;
            font-weight: 700;
            color: #333;
        }
        
        .booking-date {
            margin-left: auto;
            color: #666;
            font-size: 14px;
        }
        
        .booking-info {
            margin-bottom: 20px;
        }
        
        .info-row {
            display: flex;
            margin-bottom: 10px;
        }
        
        .info-label {
            width: 150px;
            font-weight: 600;
            color: #555;
            flex-shrink: 0;
        }
        
        .info-value {
            color: #333;
        }
        
        .queue-number {
            background-color: #e8f5e9;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .queue-title {
            font-size: 18px;
            font-weight: 600;
            color: #388e3c;
            margin-bottom: 10px;
        }
        
        .queue-value {
            font-size: 48px;
            font-weight: 700;
            color: #4caf50;
            margin-bottom: 0;
        }
        
        .action-buttons {
            margin-top: 30px;
        }
        
        .btn-action {
            margin: 0 10px;
            padding: 12px 30px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s;
        }
        
        .btn-action:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .btn-print {
            background-color: #4caf50;
            color: white;
            border: none;
        }
        
        .btn-print:hover {
            background-color: #388e3c;
            color: white;
        }
        
        .btn-home {
            background-color: white;
            color: #4caf50;
            border: 2px solid #4caf50;
        }
        
        .btn-home:hover {
            background-color: #f0f9f0;
            color: #388e3c;
        }
        
        .important-note {
            background-color: #fff8e1;
            border-left: 4px solid #ffa000;
            padding: 15px;
            border-radius: 5px;
            margin-top: 30px;
            text-align: left;
        }
        
        .important-note h5 {
            color: #f57c00;
            font-weight: 600;
            margin-bottom: 10px;
            font-size: 18px;
        }
        
        .important-note p {
            color: #666;
            margin-bottom: 0;
        }
        
        footer {
            background-color: #333;
            color: white;
            padding: 20px 0;
            text-align: center;
        }
        
        .copyright {
            opacity: 0.7;
            font-size: 14px;
            margin: 0;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-4">
                    <div class="logo-container">
                        <img src="../assets/img/logo.jpeg" alt="Bank BJB">
                        <h1>Bank BJB</h1>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="nav-menu">
                        <a href="index.php"><i class="fas fa-home"></i> Beranda</a>
                        <a href="booking.php" class="active"><i class="fas fa-calendar-check"></i> Booking</a>
                        <a href="check_status.php"><i class="fas fa-search"></i> Cek Status</a>
                        <a href="register.php"><i class="fas fa-user-plus"></i> Registrasi</a>
                        <a href="login.php"><i class="fas fa-sign-in-alt"></i> Login</a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Page Title -->
    <div class="page-title">
        <div class="container">
            <h2>Booking Berhasil</h2>
            <p>Terima kasih telah melakukan booking antrian di Bank BJB</p>
        </div>
    </div>

    <!-- Success Container -->
    <div class="container">
        <div class="success-container">
            <div class="success-icon">
                <i class="fas fa-check"></i>
            </div>
            
            <h3 class="success-title">Booking Antrian Berhasil!</h3>
            <p class="success-message">Booking antrian Anda telah berhasil dibuat. Silakan datang ke Bank BJB sesuai dengan jadwal yang telah Anda pilih.</p>
            
            <div class="booking-details">
                <div class="booking-header">
                    <div class="booking-id">Booking ID: <?php echo $booking['booking_id']; ?></div>
                    <div class="booking-date">Dibuat pada: <?php echo date('d M Y, H:i', strtotime($booking['created_at'])); ?></div>
                </div>
                
                <div class="booking-info">
                    <div class="info-row">
                        <div class="info-label">Nama:</div>
                        <div class="info-value"><?php echo $booking['nama']; ?></div>
                    </div>
                    
                    <div class="info-row">
                        <div class="info-label">Layanan:</div>
                        <div class="info-value"><?php echo $layanan_display; ?></div>
                    </div>
                    
                    <div class="info-row">
                        <div class="info-label">Tanggal:</div>
                        <div class="info-value"><?php echo $tanggal_display; ?></div>
                    </div>
                    
                    <div class="info-row">
                        <div class="info-label">Waktu:</div>
                        <div class="info-value"><?php echo $waktu_display; ?></div>
                    </div>
                    
                    <div class="info-row">
                        <div class="info-label">Status:</div>
                        <div class="info-value">
                            <span class="badge badge-success">Terkonfirmasi</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="queue-number">
                <div class="queue-title">Nomor Antrian Anda</div>
                <div class="queue-value"><?php echo $booking['nomor_antrian']; ?></div>
            </div>
            
            <div class="action-buttons">
                <a href="dashboard.php" class="btn btn-action btn-home">
                    <i class="fas fa-home mr-2"></i> Kembali ke Dashboard
                </a>
                <button class="btn btn-action btn-print" onclick="window.print()">
                    <i class="fas fa-print mr-2"></i> Cetak Tiket
                </button>
            </div>
            
            <div class="important-note">
                <h5><i class="fas fa-exclamation-circle mr-2"></i> Penting!</h5>
                <p>Harap datang 15 menit sebelum waktu yang telah ditentukan dan bawa KTP/Buku Tabungan asli untuk verifikasi. Booking akan otomatis dibatalkan jika Anda tidak hadir pada waktu yang telah ditentukan.</p>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <p class="copyright">© 2023 Bank BJB. All Rights Reserved.</p>
        </div>
    </footer>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

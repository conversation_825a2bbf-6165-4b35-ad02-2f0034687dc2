<?php
// Feedback page for customers
$page_title = 'Berikan Feedback';
require_once '../includes/functions.php';

// Initialize variables
$queue_number = '';
$rating = '';
$comment = '';
$errors = [];
$success = false;

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate input
    $queue_number = trim($_POST['queue_number'] ?? '');
    $rating = $_POST['rating'] ?? '';
    $comment = trim($_POST['comment'] ?? '');

    // Validation
    if (empty($queue_number)) {
        $errors[] = 'Nomor antrian harus diisi';
    }

    if (empty($rating)) {
        $errors[] = 'Rating harus dipilih';
    } elseif (!in_array($rating, ['1', '2', '3', '4', '5'])) {
        $errors[] = 'Rating tidak valid';
    }

    // If no errors, proceed with saving feedback
    if (empty($errors)) {
        // Connect to database
        $conn = db_connect();
        
        // Check if queue number exists and has been completed
        $stmt = $conn->prepare("SELECT id, status FROM queue WHERE queue_number = ? AND date = CURDATE()");
        $stmt->bind_param("s", $queue_number);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            $errors[] = 'Nomor antrian tidak ditemukan atau sudah tidak berlaku.';
        } else {
            $queue = $result->fetch_assoc();
            
            if ($queue['status'] !== 'completed') {
                $errors[] = 'Anda hanya dapat memberikan feedback setelah layanan selesai.';
            } else {
                // Check if feedback already exists for this queue
                $stmt = $conn->prepare("SELECT id FROM feedback WHERE queue_id = ?");
                $stmt->bind_param("i", $queue['id']);
                $stmt->execute();
                $feedback_result = $stmt->get_result();
                
                if ($feedback_result->num_rows > 0) {
                    $errors[] = 'Anda sudah memberikan feedback untuk nomor antrian ini.';
                } else {
                    // Save feedback
                    $stmt = $conn->prepare("INSERT INTO feedback (queue_id, rating, comment, created_at) VALUES (?, ?, ?, NOW())");
                    $stmt->bind_param("iis", $queue['id'], $rating, $comment);
                    
                    if ($stmt->execute()) {
                        $success = true;
                        $queue_number = '';
                        $rating = '';
                        $comment = '';
                    } else {
                        $errors[] = 'Terjadi kesalahan saat menyimpan feedback. Silakan coba lagi.';
                    }
                }
            }
        }
        
        $stmt->close();
        $conn->close();
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Berikan Feedback - Bank BJB</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">

    <style>
        body {
            background: linear-gradient(135deg, #1a6a83 0%, #0e4b5e 100%);
            min-height: 100vh;
        }
        .feedback-box {
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 8px;
            padding: 30px;
            margin-top: 50px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        .feedback-header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .feedback-header img {
            height: 80px;
            margin-bottom: 20px;
        }
        .rating {
            display: flex;
            flex-direction: row-reverse;
            justify-content: center;
            margin-bottom: 30px;
        }
        .rating > input {
            display: none;
        }
        .rating > label {
            position: relative;
            width: 1.1em;
            font-size: 3rem;
            color: #FFD700;
            cursor: pointer;
        }
        .rating > label::before {
            content: "\2605";
            position: absolute;
            opacity: 0;
        }
        .rating > label:hover:before,
        .rating > label:hover ~ label:before {
            opacity: 1 !important;
        }
        .rating > input:checked ~ label:before {
            opacity: 1;
        }
        .rating:hover > input:checked ~ label:before {
            opacity: 0.4;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="feedback-box">
                    <div class="feedback-header">
                        <img src="../assets/img/logo.jpeg" alt="Bank BJB" style="max-width: 200px;">
                        <h2>Berikan Feedback</h2>
                        <p class="text-muted">Bantu kami meningkatkan layanan dengan memberikan feedback</p>
                    </div>

                    <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                            <li><?php echo $error; ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <?php endif; ?>

                    <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle mr-2"></i>Terima kasih atas feedback Anda! Kami sangat menghargai masukan Anda untuk meningkatkan layanan kami.
                    </div>
                    <?php endif; ?>

                    <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>">
                        <div class="form-group">
                            <label for="queue_number"><i class="fas fa-ticket-alt mr-2"></i>Nomor Antrian</label>
                            <input type="text" class="form-control" id="queue_number" name="queue_number" value="<?php echo htmlspecialchars($queue_number); ?>" placeholder="Contoh: A001" required>
                            <small class="form-text text-muted">Masukkan nomor antrian yang telah selesai dilayani</small>
                        </div>

                        <div class="form-group">
                            <label><i class="fas fa-star mr-2"></i>Rating Pelayanan</label>
                            <div class="rating">
                                <input type="radio" name="rating" value="5" id="rating-5" <?php if ($rating === '5') echo 'checked'; ?>>
                                <label for="rating-5"></label>
                                <input type="radio" name="rating" value="4" id="rating-4" <?php if ($rating === '4') echo 'checked'; ?>>
                                <label for="rating-4"></label>
                                <input type="radio" name="rating" value="3" id="rating-3" <?php if ($rating === '3') echo 'checked'; ?>>
                                <label for="rating-3"></label>
                                <input type="radio" name="rating" value="2" id="rating-2" <?php if ($rating === '2') echo 'checked'; ?>>
                                <label for="rating-2"></label>
                                <input type="radio" name="rating" value="1" id="rating-1" <?php if ($rating === '1') echo 'checked'; ?>>
                                <label for="rating-1"></label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="comment"><i class="fas fa-comment mr-2"></i>Komentar (Opsional)</label>
                            <textarea class="form-control" id="comment" name="comment" rows="4" placeholder="Berikan komentar atau saran Anda"><?php echo htmlspecialchars($comment); ?></textarea>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary btn-block"><i class="fas fa-paper-plane mr-2"></i>Kirim Feedback</button>
                        </div>
                    </form>

                    <div class="text-center mt-4">
                        <a href="../index.php" class="btn btn-outline-secondary"><i class="fas fa-home mr-2"></i>Kembali ke Beranda</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
/**
 * Latest Queue Component for Admin Dashboard
 *
 * This component displays the latest queue data from the database
 */

// Include database configuration if not already included
if (!function_exists('query')) {
    require_once __DIR__ . '/../../config/database.php';
    require_once __DIR__ . '/../../includes/functions.php';
    require_once __DIR__ . '/../../includes/queue_connector.php';
}

// Get latest queues from database
$latest_queues = [];

try {
    // Get all queues for today
    $sql = "SELECT a.id, a.nomor_antrian, n.nama,
            CASE
                WHEN a.jenis_layanan = 'teller' THEN 'Teller'
                WHEN a.jenis_layanan = 'cs' THEN 'Customer Service'
                WHEN a.jenis_layanan = 'kredit' THEN 'Kredit'
                ELSE p.nama_poli
            END as layanan,
            CASE
                WHEN l.nama_loket IS NULL THEN '-'
                ELSE l.nama_loket
            END as loket,
            a.status,
            TIME_FORMAT(a.waktu_booking, '%H:%i') as waktu_booking
            FROM antrian a
            LEFT JOIN nasabah n ON a.nasabah_id = n.id
            LEFT JOIN poli p ON a.poli_id = p.id
            LEFT JOIN loket l ON a.loket_id = l.id
            WHERE a.tanggal = CURDATE()
            ORDER BY a.id DESC
            LIMIT 10";

    $result = query($sql);

    if ($result && num_rows($result) > 0) {
        $latest_queues = fetch_all($result);
    } else {
        // Jika tidak ada data, coba ambil dari fungsi get_all_queues_today
        $latest_queues = get_all_queues_today();
    }
} catch (Exception $e) {
    // Log error
    error_log("Error fetching latest queues for admin: " . $e->getMessage());
}

// If no data found, use mock data
if (empty($latest_queues)) {
    $latest_queues = [
        [
            'id' => 1,
            'nomor_antrian' => 'T001',
            'nama' => 'Ahmad Fauzi',
            'layanan' => 'Teller',
            'loket' => 'Loket 1',
            'status' => 'selesai',
            'waktu_booking' => '03:14'
        ],
        [
            'id' => 2,
            'nomor_antrian' => 'C001',
            'nama' => 'Siti Nurhaliza',
            'layanan' => 'Customer Service',
            'loket' => 'Loket 4',
            'status' => 'selesai',
            'waktu_booking' => '04:44'
        ],
        [
            'id' => 3,
            'nomor_antrian' => 'K001',
            'nama' => 'Budi Santoso',
            'layanan' => 'Kredit',
            'loket' => 'Loket 6',
            'status' => 'selesai',
            'waktu_booking' => '04:14'
        ],
        [
            'id' => 4,
            'nomor_antrian' => 'T002',
            'nama' => 'Dewi Lestari',
            'layanan' => 'Teller',
            'loket' => 'Loket 1',
            'status' => 'dipanggil',
            'waktu_booking' => '04:44'
        ],
        [
            'id' => 5,
            'nomor_antrian' => 'C002',
            'nama' => 'Rudi Hartono',
            'layanan' => 'Customer Service',
            'loket' => '-',
            'status' => 'menunggu',
            'waktu_booking' => '04:59'
        ]
    ];
}
?>

<div class="card shadow-sm mb-4">
    <div class="card-header bg-white">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0 d-flex align-items-center">
                <i class="fas fa-list-alt text-primary mr-2"></i> Antrian Terbaru
            </h5>
            <a href="../latest_queue.php" target="_blank" class="btn btn-sm btn-outline-primary">
                <i class="fas fa-external-link-alt mr-1"></i> Lihat Semua
            </a>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="bg-light">
                    <tr>
                        <th>No. Antrian</th>
                        <th>Nama</th>
                        <th>Layanan</th>
                        <th>Loket</th>
                        <th>Status</th>
                        <th>Waktu Booking</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($latest_queues as $queue): ?>
                    <tr>
                        <td class="font-weight-bold"><?php echo $queue['nomor_antrian']; ?></td>
                        <td><?php echo $queue['nama']; ?></td>
                        <td><?php echo $queue['layanan']; ?></td>
                        <td><?php echo $queue['loket']; ?></td>
                        <td>
                            <?php if ($queue['status'] == 'selesai'): ?>
                            <span class="badge badge-success">Selesai</span>
                            <?php elseif ($queue['status'] == 'dipanggil'): ?>
                            <span class="badge badge-info">Dipanggil</span>
                            <?php elseif ($queue['status'] == 'menunggu'): ?>
                            <span class="badge badge-warning">Menunggu</span>
                            <?php elseif ($queue['status'] == 'batal'): ?>
                            <span class="badge badge-danger">Batal</span>
                            <?php endif; ?>
                        </td>
                        <td><?php echo $queue['waktu_booking']; ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

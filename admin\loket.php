<?php
// Admin counter management page
$page_title = 'Manajemen Loket';
$active_menu = 'loket';
$is_admin = true;
require_once '../includes/functions.php';

// Check if user is logged in
if (!is_logged_in()) {
    redirect('../login_admin.php');
}

// Check if user is admin
if (!is_admin()) {
    redirect('../index.php');
}

// Process form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['add_counter'])) {
        // Validate input
        $nama_loket = trim($_POST['nama_loket']);
        $jenis_layanan = $_POST['jenis_layanan'];
        $status = $_POST['status'];

        if (empty($nama_loket) || empty($jenis_layanan) || empty($status)) {
            $_SESSION['error_message'] = 'Semua field harus diisi.';
        } else {
            // In real implementation, this would insert to database
            // For now, just show success message
            $_SESSION['success_message'] = "Loket '$nama_loket' berhasil ditambahkan.";
        }
        redirect('loket.php');

    } elseif (isset($_POST['edit_counter'])) {
        // Validate input
        $counter_id = $_POST['counter_id'];
        $nama_loket = trim($_POST['nama_loket']);
        $jenis_layanan = $_POST['jenis_layanan'];
        $status = $_POST['status'];

        if (empty($nama_loket) || empty($jenis_layanan) || empty($status)) {
            $_SESSION['error_message'] = 'Semua field harus diisi.';
        } else {
            // In real implementation, this would update database
            // For now, just show success message
            $_SESSION['success_message'] = "Loket '$nama_loket' berhasil diperbarui.";
        }
        redirect('loket.php');

    } elseif (isset($_POST['assign_staff'])) {
        // Validate input
        $counter_id = $_POST['counter_id'];
        $user_id = $_POST['user_id'];

        if (empty($user_id)) {
            $_SESSION['error_message'] = 'Pilih petugas yang akan ditugaskan.';
        } else {
            // In real implementation, this would update database
            // For now, just show success message
            if ($user_id == '0') {
                $_SESSION['success_message'] = 'Petugas berhasil dihapus dari loket.';
            } else {
                $_SESSION['success_message'] = 'Petugas berhasil ditugaskan ke loket.';
            }
        }
        redirect('loket.php');

    } elseif (isset($_POST['delete_counter'])) {
        // Validate input
        $counter_id = $_POST['counter_id'];

        if (empty($counter_id)) {
            $_SESSION['error_message'] = 'ID loket tidak valid.';
        } else {
            // In real implementation, this would delete from database
            // For now, just show success message
            $_SESSION['success_message'] = 'Loket berhasil dihapus.';
        }
        redirect('loket.php');
    }
}

// Mock data for counters
$counters = [
    [
        'id' => 1,
        'nama_loket' => 'Loket 1',
        'jenis_layanan' => 'teller',
        'status' => 'aktif',
        'user_id' => 2,
        'nama_petugas' => 'Petugas Teller 1'
    ],
    [
        'id' => 2,
        'nama_loket' => 'Loket 2',
        'jenis_layanan' => 'teller',
        'status' => 'aktif',
        'user_id' => null,
        'nama_petugas' => null
    ],
    [
        'id' => 3,
        'nama_loket' => 'Loket 3',
        'jenis_layanan' => 'teller',
        'status' => 'aktif',
        'user_id' => null,
        'nama_petugas' => null
    ],
    [
        'id' => 4,
        'nama_loket' => 'Loket 4',
        'jenis_layanan' => 'cs',
        'status' => 'aktif',
        'user_id' => 3,
        'nama_petugas' => 'Petugas CS 1'
    ],
    [
        'id' => 5,
        'nama_loket' => 'Loket 5',
        'jenis_layanan' => 'cs',
        'status' => 'aktif',
        'user_id' => 6,
        'nama_petugas' => 'Petugas CS 2'
    ],
    [
        'id' => 6,
        'nama_loket' => 'Loket 6',
        'jenis_layanan' => 'kredit',
        'status' => 'aktif',
        'user_id' => 4,
        'nama_petugas' => 'Petugas Kredit 1'
    ],
    [
        'id' => 7,
        'nama_loket' => 'Loket 7',
        'jenis_layanan' => 'kredit',
        'status' => 'nonaktif',
        'user_id' => null,
        'nama_petugas' => null
    ]
];

// Mock data for staff
$staff = [
    [
        'id' => 2,
        'username' => 'petugas1',
        'nama_lengkap' => 'Petugas Teller 1'
    ],
    [
        'id' => 3,
        'username' => 'petugas2',
        'nama_lengkap' => 'Petugas CS 1'
    ],
    [
        'id' => 4,
        'username' => 'petugas3',
        'nama_lengkap' => 'Petugas Kredit 1'
    ],
    [
        'id' => 5,
        'username' => 'petugas4',
        'nama_lengkap' => 'Petugas Teller 2'
    ],
    [
        'id' => 6,
        'username' => 'petugas5',
        'nama_lengkap' => 'Petugas CS 2'
    ]
];
?>

<?php
// Include header
include_once 'includes/header.php';
?>

<style>
    .counter-table th {
        background-color: #f8f9fa;
        font-weight: 600;
    }

    .counter-actions .btn {
        margin-right: 5px;
        margin-bottom: 5px;
    }

    .badge-aktif {
        background-color: #28a745;
        color: white;
    }

    .badge-nonaktif {
        background-color: #dc3545;
        color: white;
    }

    .badge-teller {
        background-color: #007bff;
        color: white;
    }

    .badge-cs {
        background-color: #6f42c1;
        color: white;
    }

    .badge-kredit {
        background-color: #fd7e14;
        color: white;
    }

    .modal-header {
        background-color: #1a6a83;
        color: white;
    }

    .modal-header .close {
        color: white;
    }

    .form-group label {
        font-weight: 600;
    }

    .required::after {
        content: " *";
        color: red;
    }
</style>

            <!-- Content -->
            <div class="container-fluid">
                <div class="row mb-4">
                    <div class="col-md-8">
                        <h2>Manajemen Loket</h2>
                    </div>
                    <div class="col-md-4 text-right">
                        <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addCounterModal">
                            <i class="fas fa-plus-circle mr-1"></i> Tambah Loket
                        </button>
                    </div>
                </div>

                <?php if (isset($_SESSION['success_message'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle mr-1"></i> <?php echo $_SESSION['success_message']; ?>
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <?php unset($_SESSION['success_message']); ?>
                <?php endif; ?>

                <?php if (isset($_SESSION['error_message'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle mr-1"></i> <?php echo $_SESSION['error_message']; ?>
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <?php unset($_SESSION['error_message']); ?>
                <?php endif; ?>

                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover counter-table">
                                <thead>
                                    <tr>
                                        <th width="5%">No</th>
                                        <th width="15%">Nama Loket</th>
                                        <th width="15%">Jenis Layanan</th>
                                        <th width="15%">Petugas</th>
                                        <th width="10%">Status</th>
                                        <th width="15%">Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($counters as $index => $counter): ?>
                                    <tr>
                                        <td><?php echo $index + 1; ?></td>
                                        <td><?php echo $counter['nama_loket']; ?></td>
                                        <td>
                                            <span class="badge badge-<?php echo $counter['jenis_layanan']; ?>">
                                                <?php
                                                    switch($counter['jenis_layanan']) {
                                                        case 'teller':
                                                            echo 'Teller';
                                                            break;
                                                        case 'cs':
                                                            echo 'Customer Service';
                                                            break;
                                                        case 'kredit':
                                                            echo 'Kredit';
                                                            break;
                                                        default:
                                                            echo '-';
                                                    }
                                                ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($counter['user_id']): ?>
                                            <?php echo $counter['nama_petugas']; ?>
                                            <?php else: ?>
                                            <span class="text-muted">Belum ditugaskan</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge badge-<?php echo $counter['status']; ?>">
                                                <?php echo ucfirst($counter['status']); ?>
                                            </span>
                                        </td>
                                        <td class="counter-actions">
                                            <button type="button" class="btn btn-sm btn-info" data-toggle="modal" data-target="#editCounterModal<?php echo $counter['id']; ?>" title="Edit Loket">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-primary" data-toggle="modal" data-target="#assignStaffModal<?php echo $counter['id']; ?>" title="Tugaskan Petugas">
                                                <i class="fas fa-user-plus"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger" data-toggle="modal" data-target="#deleteCounterModal<?php echo $counter['id']; ?>" title="Hapus Loket">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Counter Modal -->
    <div class="modal fade" id="addCounterModal" tabindex="-1" role="dialog" aria-labelledby="addCounterModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addCounterModalLabel">Tambah Loket</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form action="" method="post">
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="nama_loket" class="required">Nama Loket</label>
                            <input type="text" class="form-control" id="nama_loket" name="nama_loket" required>
                        </div>
                        <div class="form-group">
                            <label for="jenis_layanan" class="required">Jenis Layanan</label>
                            <select class="form-control" id="jenis_layanan" name="jenis_layanan" required>
                                <option value="">-- Pilih Jenis Layanan --</option>
                                <option value="teller">Teller</option>
                                <option value="cs">Customer Service</option>
                                <option value="kredit">Kredit</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="status" class="required">Status</label>
                            <select class="form-control" id="status" name="status" required>
                                <option value="aktif">Aktif</option>
                                <option value="nonaktif">Non-aktif</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                        <button type="submit" name="add_counter" class="btn btn-primary">Simpan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Counter Modals -->
    <?php foreach ($counters as $counter): ?>
    <div class="modal fade" id="editCounterModal<?php echo $counter['id']; ?>" tabindex="-1" role="dialog" aria-labelledby="editCounterModalLabel<?php echo $counter['id']; ?>" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editCounterModalLabel<?php echo $counter['id']; ?>">Edit Loket - <?php echo $counter['nama_loket']; ?></h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form action="" method="post">
                    <div class="modal-body">
                        <input type="hidden" name="counter_id" value="<?php echo $counter['id']; ?>">
                        <div class="form-group">
                            <label for="edit_nama_loket<?php echo $counter['id']; ?>" class="required">Nama Loket</label>
                            <input type="text" class="form-control" id="edit_nama_loket<?php echo $counter['id']; ?>" name="nama_loket" value="<?php echo $counter['nama_loket']; ?>" required>
                        </div>
                        <div class="form-group">
                            <label for="edit_jenis_layanan<?php echo $counter['id']; ?>" class="required">Jenis Layanan</label>
                            <select class="form-control" id="edit_jenis_layanan<?php echo $counter['id']; ?>" name="jenis_layanan" required>
                                <option value="teller" <?php echo $counter['jenis_layanan'] == 'teller' ? 'selected' : ''; ?>>Teller</option>
                                <option value="cs" <?php echo $counter['jenis_layanan'] == 'cs' ? 'selected' : ''; ?>>Customer Service</option>
                                <option value="kredit" <?php echo $counter['jenis_layanan'] == 'kredit' ? 'selected' : ''; ?>>Kredit</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="edit_status<?php echo $counter['id']; ?>" class="required">Status</label>
                            <select class="form-control" id="edit_status<?php echo $counter['id']; ?>" name="status" required>
                                <option value="aktif" <?php echo $counter['status'] == 'aktif' ? 'selected' : ''; ?>>Aktif</option>
                                <option value="nonaktif" <?php echo $counter['status'] == 'nonaktif' ? 'selected' : ''; ?>>Non-aktif</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                        <button type="submit" name="edit_counter" class="btn btn-primary">Update</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php endforeach; ?>

    <!-- Assign Staff Modals -->
    <?php foreach ($counters as $counter): ?>
    <div class="modal fade" id="assignStaffModal<?php echo $counter['id']; ?>" tabindex="-1" role="dialog" aria-labelledby="assignStaffModalLabel<?php echo $counter['id']; ?>" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="assignStaffModalLabel<?php echo $counter['id']; ?>">Tugaskan Petugas - <?php echo $counter['nama_loket']; ?></h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form action="" method="post">
                    <div class="modal-body">
                        <input type="hidden" name="counter_id" value="<?php echo $counter['id']; ?>">
                        <div class="form-group">
                            <label for="assign_staff<?php echo $counter['id']; ?>" class="required">Pilih Petugas</label>
                            <select class="form-control" id="assign_staff<?php echo $counter['id']; ?>" name="user_id" required>
                                <option value="">-- Pilih Petugas --</option>
                                <?php foreach ($staff as $s): ?>
                                <option value="<?php echo $s['id']; ?>" <?php echo $counter['user_id'] == $s['id'] ? 'selected' : ''; ?>>
                                    <?php echo $s['nama_lengkap']; ?> (<?php echo $s['username']; ?>)
                                </option>
                                <?php endforeach; ?>
                                <option value="0" <?php echo $counter['user_id'] == null ? 'selected' : ''; ?>>Tidak ada petugas</option>
                            </select>
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle mr-1"></i>
                            Petugas yang ditugaskan akan bertanggung jawab untuk melayani antrian di loket ini.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                        <button type="submit" name="assign_staff" class="btn btn-primary">Tugaskan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php endforeach; ?>

    <!-- Delete Counter Modals -->
    <?php foreach ($counters as $counter): ?>
    <div class="modal fade" id="deleteCounterModal<?php echo $counter['id']; ?>" tabindex="-1" role="dialog" aria-labelledby="deleteCounterModalLabel<?php echo $counter['id']; ?>" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deleteCounterModalLabel<?php echo $counter['id']; ?>">Hapus Loket</h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form action="" method="post">
                    <div class="modal-body">
                        <input type="hidden" name="counter_id" value="<?php echo $counter['id']; ?>">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle mr-1"></i>
                            <strong>Peringatan!</strong> Tindakan ini tidak dapat dibatalkan.
                        </div>
                        <p>Apakah Anda yakin ingin menghapus loket <strong><?php echo $counter['nama_loket']; ?></strong>?</p>
                        <p class="text-muted">Semua data antrian yang terkait dengan loket ini akan terpengaruh.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                        <button type="submit" name="delete_counter" class="btn btn-danger">Ya, Hapus</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php endforeach; ?>

    <!-- JavaScript for enhanced functionality -->
    <script>
    $(document).ready(function() {
        // Form validation
        $('form').on('submit', function(e) {
            var form = $(this);
            var isValid = true;

            // Check required fields
            form.find('input[required], select[required]').each(function() {
                if ($(this).val() === '') {
                    isValid = false;
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });

            if (!isValid) {
                e.preventDefault();
                alert('Mohon lengkapi semua field yang wajib diisi.');
                return false;
            }
        });

        // Remove validation class on input
        $('input, select').on('input change', function() {
            $(this).removeClass('is-invalid');
        });

        // Confirm delete action
        $('button[name="delete_counter"]').on('click', function(e) {
            var counterName = $(this).closest('.modal').find('.modal-title').text();
            if (!confirm('Apakah Anda yakin ingin menghapus ' + counterName + '?')) {
                e.preventDefault();
                return false;
            }
        });

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);

        // Tooltip initialization for buttons with title attribute
        $('button[title]').tooltip();

        // Enhanced modal behavior
        $('.modal').on('show.bs.modal', function() {
            $(this).find('input:first').focus();
        });

        // Clear form when modal is hidden
        $('.modal').on('hidden.bs.modal', function() {
            $(this).find('form')[0].reset();
            $(this).find('.is-invalid').removeClass('is-invalid');
        });
    });

    // Function to show loading state
    function showLoading(button) {
        var originalText = button.html();
        button.data('original-text', originalText);
        button.html('<i class="fas fa-spinner fa-spin"></i> Memproses...');
        button.prop('disabled', true);
    }

    // Function to hide loading state
    function hideLoading(button) {
        var originalText = button.data('original-text');
        button.html(originalText);
        button.prop('disabled', false);
    }
    </script>

    <!-- Custom CSS for validation -->
    <style>
    .is-invalid {
        border-color: #dc3545 !important;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
    }

    .btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    .modal-body .alert {
        margin-bottom: 1rem;
    }

    .counter-actions .btn {
        transition: all 0.2s ease-in-out;
    }

    .counter-actions .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .table-hover tbody tr:hover {
        background-color: rgba(0,123,255,0.05);
    }

    .badge {
        font-size: 0.75em;
        padding: 0.375rem 0.5rem;
    }

    .required::after {
        content: " *";
        color: #dc3545;
        font-weight: bold;
    }
    </style>

<?php
// Include footer
include_once 'includes/footer.php';
?>

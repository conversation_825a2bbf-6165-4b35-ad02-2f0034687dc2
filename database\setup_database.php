<?php
// <PERSON><PERSON><PERSON> to set up database for antrian_bank system
require_once '../includes/db.php';

// Create tables if they don't exist
$tables = [
    // Nasabah table
    "CREATE TABLE IF NOT EXISTS nasabah (
        id INT(11) NOT NULL AUTO_INCREMENT,
        nama VARCHAR(100) NOT NULL,
        no_identitas VARCHAR(20) NOT NULL,
        jenis_identitas ENUM('ktp', 'sim', 'passport') NOT NULL DEFAULT 'ktp',
        email VARCHAR(100) NOT NULL,
        password VARCHAR(255) NOT NULL,
        no_hp VARCHAR(15) NOT NULL,
        alamat TEXT NOT NULL,
        status_verifikasi ENUM('belum', 'sudah', 'ditolak') DEFAULT 'belum',
        is_blocked TINYINT(1) DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY (email),
        UNIQUE KEY (no_identitas)
    )",

    // Bookings table
    "CREATE TABLE IF NOT EXISTS bookings (
        id INT(11) NOT NULL AUTO_INCREMENT,
        booking_id VARCHAR(20) NOT NULL,
        nasabah_id INT(11) NOT NULL,
        nama VARCHAR(100) NOT NULL,
        no_identitas VARCHAR(20) NOT NULL,
        email VARCHAR(100) NOT NULL,
        no_hp VARCHAR(15) NOT NULL,
        layanan ENUM('teller', 'cs', 'kredit') NOT NULL,
        tanggal DATE NOT NULL,
        waktu VARCHAR(10) NOT NULL,
        keterangan TEXT,
        file_identitas VARCHAR(255) NOT NULL,
        nomor_antrian VARCHAR(10) NOT NULL,
        status ENUM('pending', 'confirmed', 'completed', 'cancelled') DEFAULT 'pending',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY (booking_id),
        FOREIGN KEY (nasabah_id) REFERENCES nasabah(id) ON DELETE CASCADE
    )",

    // Loket table
    "CREATE TABLE IF NOT EXISTS loket (
        id INT(11) NOT NULL AUTO_INCREMENT,
        nama_loket VARCHAR(50) NOT NULL,
        jenis_layanan ENUM('teller', 'cs', 'kredit') NOT NULL,
        status ENUM('aktif', 'nonaktif') DEFAULT 'aktif',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id)
    )",

    // Antrian table
    "CREATE TABLE IF NOT EXISTS antrian (
        id INT(11) NOT NULL AUTO_INCREMENT,
        booking_id VARCHAR(20) DEFAULT NULL,
        nomor_antrian VARCHAR(10) NOT NULL,
        nasabah_id INT(11) DEFAULT NULL,
        loket_id INT(11) DEFAULT NULL,
        layanan ENUM('teller', 'cs', 'kredit') NOT NULL,
        status ENUM('menunggu', 'dipanggil', 'selesai', 'batal') DEFAULT 'menunggu',
        waktu_ambil DATETIME DEFAULT CURRENT_TIMESTAMP,
        waktu_panggil DATETIME DEFAULT NULL,
        waktu_selesai DATETIME DEFAULT NULL,
        tanggal DATE NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        FOREIGN KEY (nasabah_id) REFERENCES nasabah(id) ON DELETE SET NULL,
        FOREIGN KEY (loket_id) REFERENCES loket(id) ON DELETE SET NULL
    )",

    // Pengaturan table
    "CREATE TABLE IF NOT EXISTS pengaturan (
        id INT(11) NOT NULL AUTO_INCREMENT,
        nama_pengaturan VARCHAR(50) NOT NULL,
        nilai TEXT NOT NULL,
        keterangan TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY (nama_pengaturan)
    )"
];

// Execute each table creation query
$success = true;
$messages = [];

foreach ($tables as $table_query) {
    if ($conn->query($table_query) !== TRUE) {
        $success = false;
        $messages[] = "Error creating table: " . $conn->error;
    }
}

// Insert default settings if pengaturan table is empty
$result = $conn->query("SELECT COUNT(*) as count FROM pengaturan");
$row = $result->fetch_assoc();

if ($row['count'] == 0) {
    $default_settings = [
        ["nama_bank", "Bank BJB", "Nama bank yang ditampilkan di aplikasi"],
        ["alamat_bank", "Jl. Naripan No.12-14, Bandung", "Alamat bank"],
        ["jam_operasional", "08:00-16:00", "Jam operasional bank (format: HH:MM-HH:MM)"],
        ["max_antrian", "100", "Jumlah maksimal antrian per hari"],
        ["estimasi_waktu_teller", "5", "Estimasi waktu layanan teller (dalam menit)"],
        ["estimasi_waktu_cs", "10", "Estimasi waktu layanan customer service (dalam menit)"],
        ["estimasi_waktu_kredit", "15", "Estimasi waktu layanan kredit (dalam menit)"]
    ];

    foreach ($default_settings as $setting) {
        $stmt = $conn->prepare("INSERT INTO pengaturan (nama_pengaturan, nilai, keterangan) VALUES (?, ?, ?)");
        $stmt->bind_param("sss", $setting[0], $setting[1], $setting[2]);
        
        if (!$stmt->execute()) {
            $success = false;
            $messages[] = "Error inserting default setting: " . $stmt->error;
        }
    }
}

// Insert default loket if loket table is empty
$result = $conn->query("SELECT COUNT(*) as count FROM loket");
$row = $result->fetch_assoc();

if ($row['count'] == 0) {
    $default_lokets = [
        ["Teller 1", "teller", "aktif"],
        ["Teller 2", "teller", "aktif"],
        ["Customer Service 1", "cs", "aktif"],
        ["Customer Service 2", "cs", "aktif"],
        ["Kredit 1", "kredit", "aktif"]
    ];

    foreach ($default_lokets as $loket) {
        $stmt = $conn->prepare("INSERT INTO loket (nama_loket, jenis_layanan, status) VALUES (?, ?, ?)");
        $stmt->bind_param("sss", $loket[0], $loket[1], $loket[2]);
        
        if (!$stmt->execute()) {
            $success = false;
            $messages[] = "Error inserting default loket: " . $stmt->error;
        }
    }
}

// Create uploads directory if it doesn't exist
$upload_dir = '../uploads/';
if (!file_exists($upload_dir)) {
    mkdir($upload_dir, 0777, true);
}

// Output result
echo '<html><head><title>Database Setup</title>';
echo '<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">';
echo '</head><body class="p-4">';
echo '<div class="container">';
echo '<h1 class="mb-4">Database Setup</h1>';

if ($success && empty($messages)) {
    echo '<div class="alert alert-success">Database setup completed successfully!</div>';
} else {
    if ($success) {
        echo '<div class="alert alert-warning">Database setup completed with warnings:</div>';
    } else {
        echo '<div class="alert alert-danger">Database setup failed:</div>';
    }
    
    echo '<ul>';
    foreach ($messages as $message) {
        echo '<li>' . $message . '</li>';
    }
    echo '</ul>';
}

echo '<a href="../index.php" class="btn btn-primary">Go to Homepage</a>';
echo '</div></body></html>';
?>

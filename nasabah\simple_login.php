<?php
// Simple login page for testing
require_once '../includes/functions.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$message = '';

// If form submitted
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Set basic session variables
    $_SESSION['nasabah_id'] = 1; 
    $_SESSION['user_id'] = 1;
    $_SESSION['user_role'] = 'nasabah';
    $_SESSION['nasabah_nama'] = 'Nasabah Test';
    $_SESSION['nasabah_email'] = '<EMAIL>';
    
    $message = '<div class="alert alert-success">Login berhasil! Session telah diatur.</div>';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Login Test</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        Simple Login Test
                    </div>
                    <div class="card-body">
                        <?php if (!empty($message)) echo $message; ?>
                        
                        <p>Use this simple form to set session variables for testing the booking system.</p>
                        
                        <form method="post">
                            <div class="form-group">
                                <button type="submit" class="btn btn-success btn-block">Set Session Variables</button>
                            </div>
                        </form>
                        
                        <div class="mt-4">
                            <h5>Current Session:</h5>
                            <pre><?php print_r($_SESSION); ?></pre>
                        </div>
                        
                        <div class="mt-4">
                            <a href="booking.php" class="btn btn-primary">Try Booking</a>
                            <a href="debug_booking.php" class="btn btn-info ml-2">Debug Booking</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
<?php
// Print ticket page
require_once '../includes/functions.php';
require_once '../includes/db.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'nasabah') {
    $_SESSION['error_messages'] = ["Anda harus login terlebih dahulu."];
    header("Location: login.php");
    exit;
}

// Check if booking ID is provided
if (!isset($_GET['id'])) {
    $_SESSION['error_messages'] = ["ID booking tidak valid."];
    header("Location: dashboard.php");
    exit;
}

$booking_id = $_GET['id'];
$user_id = $_SESSION['user_id'];

// Get booking details
$stmt = $conn->prepare("SELECT b.*, n.nama, n.no_identitas, n.email, n.no_hp 
                        FROM bookings b 
                        JOIN nasabah n ON b.nasabah_id = n.id 
                        WHERE b.booking_id = ? AND b.nasabah_id = ?");
$stmt->bind_param("si", $booking_id, $user_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    $_SESSION['error_messages'] = ["Booking tidak ditemukan atau Anda tidak memiliki akses."];
    header("Location: dashboard.php");
    exit;
}

$booking = $result->fetch_assoc();

// Format layanan name for display
$layanan_display = '';
switch ($booking['layanan']) {
    case 'teller':
        $layanan_display = 'Teller';
        break;
    case 'cs':
        $layanan_display = 'Customer Service';
        break;
    case 'kredit':
        $layanan_display = 'Kredit';
        break;
    default:
        $layanan_display = $booking['layanan'];
}

// Format date
$tanggal_display = date('d F Y', strtotime($booking['tanggal']));

// Format status badge
$status_badge_class = '';
$status_display = '';
switch ($booking['status']) {
    case 'pending':
        $status_badge_class = 'warning';
        $status_display = 'Menunggu Konfirmasi';
        break;
    case 'confirmed':
        $status_badge_class = 'success';
        $status_display = 'Terkonfirmasi';
        break;
    case 'completed':
        $status_badge_class = 'info';
        $status_display = 'Selesai';
        break;
    case 'cancelled':
        $status_badge_class = 'danger';
        $status_display = 'Dibatalkan';
        break;
    default:
        $status_badge_class = 'secondary';
        $status_display = $booking['status'];
}

// Page title
$page_title = "Cetak Tiket #" . $booking['booking_id'];
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Bank BJB</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f8f9fa;
        }
        .ticket-container {
            max-width: 800px;
            margin: 30px auto;
            background-color: white;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        .ticket-header {
            background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .ticket-body {
            padding: 30px;
        }
        .ticket-info {
            border: 1px dashed #ddd;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .ticket-number {
            font-size: 48px;
            font-weight: bold;
            color: #4caf50;
            text-align: center;
            margin: 20px 0;
        }
        .ticket-details {
            margin-bottom: 30px;
        }
        .ticket-details table {
            width: 100%;
        }
        .ticket-details th {
            width: 40%;
            padding: 8px 0;
            color: #666;
        }
        .ticket-details td {
            padding: 8px 0;
            font-weight: 500;
        }
        .ticket-footer {
            text-align: center;
            padding: 20px;
            border-top: 1px solid #eee;
            color: #666;
        }
        .print-button {
            margin-top: 20px;
            text-align: center;
        }
        .qr-code {
            text-align: center;
            margin: 20px 0;
        }
        .qr-code img {
            max-width: 150px;
        }
        @media print {
            .no-print {
                display: none;
            }
            body {
                background-color: white;
            }
            .ticket-container {
                box-shadow: none;
                margin: 0;
                max-width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="ticket-container">
            <div class="ticket-header">
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <img src="../assets/img/logo.jpeg" alt="Bank BJB" class="img-fluid" style="max-height: 60px;">
                    </div>
                    <div class="col-md-6">
                        <h2>Bank BJB</h2>
                        <p class="mb-0">Kantor Cabang Khusus Banten</p>
                    </div>
                    <div class="col-md-3">
                        <div class="badge badge-light p-2">
                            <?php echo $booking['booking_id']; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="ticket-body">
                <div class="ticket-info">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Informasi Nasabah</h5>
                            <p><strong>Nama:</strong> <?php echo $booking['nama']; ?></p>
                            <p><strong>No. Identitas:</strong> <?php echo $booking['no_identitas']; ?></p>
                            <p><strong>Email:</strong> <?php echo $booking['email']; ?></p>
                            <p><strong>No. HP:</strong> <?php echo $booking['no_hp']; ?></p>
                        </div>
                        <div class="col-md-6">
                            <h5>Informasi Booking</h5>
                            <p><strong>Layanan:</strong> <?php echo $layanan_display; ?></p>
                            <p><strong>Tanggal:</strong> <?php echo $tanggal_display; ?></p>
                            <p><strong>Waktu:</strong> <?php echo $booking['waktu']; ?></p>
                            <p><strong>Status:</strong> <span class="badge badge-<?php echo $status_badge_class; ?>"><?php echo $status_display; ?></span></p>
                        </div>
                    </div>
                </div>
                
                <div class="text-center">
                    <h4>Nomor Antrian Anda</h4>
                    <div class="ticket-number"><?php echo $booking['nomor_antrian']; ?></div>
                </div>
                
                <div class="qr-code">
                    <img src="https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=<?php echo $booking['booking_id']; ?>" alt="QR Code">
                    <p class="mt-2">Scan QR Code ini saat tiba di bank</p>
                </div>
                
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle mr-2"></i> Harap datang 15 menit sebelum waktu yang dijadwalkan dan bawa dokumen identitas asli.
                </div>
            </div>
            
            <div class="ticket-footer">
                <p>Terima kasih telah menggunakan layanan Bank BJB</p>
                <p class="mb-0"><small>Dicetak pada: <?php echo date('d/m/Y H:i:s'); ?></small></p>
            </div>
        </div>
        
        <div class="print-button no-print">
            <button class="btn btn-primary" onclick="window.print();">
                <i class="fas fa-print mr-2"></i> Cetak Tiket
            </button>
            <a href="dashboard.php" class="btn btn-secondary ml-2">
                <i class="fas fa-arrow-left mr-2"></i> Kembali ke Dashboard
            </a>
        </div>
    </div>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Auto print when page loads
        $(document).ready(function() {
            // Uncomment the line below to automatically print when page loads
            // window.print();
        });
    </script>
</body>
</html>

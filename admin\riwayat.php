<?php
// Admin queue history page
$page_title = 'Riwayat Antrian';
$active_menu = 'riwayat';
$is_admin = true;
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!is_logged_in() || !is_admin()) {
    redirect('../login.php');
}

// Get date filter
$date_filter = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');

// Get status filter
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';

// Get loket filter
$loket_filter = isset($_GET['loket']) ? (int)$_GET['loket'] : 0;

// Get poli filter
$poli_filter = isset($_GET['poli']) ? (int)$_GET['poli'] : 0;

// Build query
$where_clause = "WHERE a.tanggal = '$date_filter'";

if ($status_filter) {
    $where_clause .= " AND a.status = '$status_filter'";
}

if ($loket_filter) {
    $where_clause .= " AND a.loket_id = $loket_filter";
}

if ($poli_filter) {
    $where_clause .= " AND a.poli_id = $poli_filter";
}

// Get queue history
$sql = "SELECT a.*, n.nama, p.nama_poli, l.nama_loket, u.nama_lengkap as nama_petugas,
        TIMESTAMPDIFF(MINUTE, a.waktu_dipanggil, a.waktu_selesai) as durasi_layanan
        FROM antrian a
        LEFT JOIN nasabah n ON a.nasabah_id = n.id
        LEFT JOIN poli p ON a.poli_id = p.id
        LEFT JOIN loket l ON a.loket_id = l.id
        LEFT JOIN users u ON l.user_id = u.id
        $where_clause
        ORDER BY a.waktu_dipanggil DESC";
$result = query($sql);
$queue_history = fetch_all($result);

// Get statistics
$sql = "SELECT COUNT(*) as total_antrian,
        SUM(CASE WHEN a.status = 'selesai' THEN 1 ELSE 0 END) as total_selesai,
        SUM(CASE WHEN a.status = 'dipanggil' THEN 1 ELSE 0 END) as total_dipanggil,
        SUM(CASE WHEN a.status = 'batal' THEN 1 ELSE 0 END) as total_batal,
        SUM(CASE WHEN a.status = 'menunggu' THEN 1 ELSE 0 END) as total_menunggu,
        AVG(TIMESTAMPDIFF(MINUTE, a.waktu_dipanggil, a.waktu_selesai)) as rata_durasi
        FROM antrian a
        $where_clause";
$result = query($sql);
$stats = fetch_assoc($result);

// Get all counters for filter
$sql = "SELECT * FROM loket ORDER BY nama_loket";
$result = query($sql);
$counters = fetch_all($result);

// Get all services for filter
$sql = "SELECT * FROM poli ORDER BY nama_poli";
$result = query($sql);
$services = fetch_all($result);

// Include header
include_once '../includes/header.php';

// Add custom CSS for icon circles
echo '<style>
    .icon-circle {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    }
    
    .bg-gradient-primary {
        background: linear-gradient(135deg, #1a6a83 0%, #0e4b5e 100%);
    }
    
    .bg-gradient-success {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    }
    
    .bg-gradient-warning {
        background: linear-gradient(135deg, #ffc107 0%, #d39e00 100%);
    }
    
    .bg-gradient-danger {
        background: linear-gradient(135deg, #dc3545 0%, #bd2130 100%);
    }
    
    .bg-gradient-dark {
        background: linear-gradient(135deg, #343a40 0%, #1d2124 100%);
    }
    
    .bg-gradient-info {
        background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
    }
</style>';
?>

<div class="row">
    <div class="col-md-12">
        <h1 class="mb-4 text-primary"><i class="fas fa-history mr-2"></i>Riwayat Antrian</h1>

        <div class="card mb-4 shadow-sm">
            <div class="card-header bg-gradient-primary text-white">
                <i class="fas fa-filter mr-2"></i> Filter Data
            </div>
            <div class="card-body p-3">
                <form method="get" action="" class="row">
                    <div class="col-md-3 mb-3">
                        <label for="date" class="font-weight-bold">Tanggal:</label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                            </div>
                            <input type="date" class="form-control" id="date" name="date" value="<?php echo $date_filter; ?>">
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="status" class="font-weight-bold">Status:</label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text"><i class="fas fa-tag"></i></span>
                            </div>
                            <select class="form-control" id="status" name="status">
                                <option value="">Semua Status</option>
                                <option value="selesai" <?php echo $status_filter == 'selesai' ? 'selected' : ''; ?>>Selesai</option>
                                <option value="dipanggil" <?php echo $status_filter == 'dipanggil' ? 'selected' : ''; ?>>Sedang Dilayani</option>
                                <option value="batal" <?php echo $status_filter == 'batal' ? 'selected' : ''; ?>>Batal</option>
                                <option value="menunggu" <?php echo $status_filter == 'menunggu' ? 'selected' : ''; ?>>Menunggu</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="loket" class="font-weight-bold">Loket:</label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text"><i class="fas fa-door-open"></i></span>
                            </div>
                            <select class="form-control" id="loket" name="loket">
                                <option value="">Semua Loket</option>
                                <?php foreach($counters as $counter): ?>
                                <option value="<?php echo $counter['id']; ?>" <?php echo $loket_filter == $counter['id'] ? 'selected' : ''; ?>>
                                    <?php echo $counter['nama_loket']; ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="poli" class="font-weight-bold">Layanan:</label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text"><i class="fas fa-concierge-bell"></i></span>
                            </div>
                            <select class="form-control" id="poli" name="poli">
                                <option value="">Semua Layanan</option>
                                <?php foreach($services as $service): ?>
                                <option value="<?php echo $service['id']; ?>" <?php echo $poli_filter == $service['id'] ? 'selected' : ''; ?>>
                                    <?php echo $service['nama_poli']; ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-12 mb-3 d-flex">
                        <button type="submit" class="btn btn-primary mr-2">
                            <i class="fas fa-search mr-1"></i> Filter
                        </button>
                        <a href="riwayat.php" class="btn btn-secondary mr-2">
                            <i class="fas fa-sync-alt mr-1"></i> Reset
                        </a>
                        <a href="statistik.php" class="btn btn-info ml-auto">
                            <i class="fas fa-chart-bar mr-1"></i> Lihat Statistik
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-lg-2 col-md-6 mb-3">
                <div class="card bg-gradient-primary text-white shadow-sm border-0 h-100">
                    <div class="card-body p-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-uppercase mb-1 font-weight-bold">Total</h6>
                                <h2 class="mb-0 font-weight-bold"><?php echo $stats['total_antrian'] ?: 0; ?></h2>
                            </div>
                            <div class="icon-circle bg-white text-primary">
                                <i class="fas fa-ticket-alt fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-6 mb-3">
                <div class="card bg-gradient-success text-white shadow-sm border-0 h-100">
                    <div class="card-body p-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-uppercase mb-1 font-weight-bold">Selesai</h6>
                                <h2 class="mb-0 font-weight-bold"><?php echo $stats['total_selesai'] ?: 0; ?></h2>
                            </div>
                            <div class="icon-circle bg-white text-success">
                                <i class="fas fa-check-circle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-6 mb-3">
                <div class="card bg-gradient-warning text-dark shadow-sm border-0 h-100">
                    <div class="card-body p-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-uppercase mb-1 font-weight-bold">Dilayani</h6>
                                <h2 class="mb-0 font-weight-bold"><?php echo $stats['total_dipanggil'] ?: 0; ?></h2>
                            </div>
                            <div class="icon-circle bg-white text-warning">
                                <i class="fas fa-user-clock fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-6 mb-3">
                <div class="card bg-gradient-info text-white shadow-sm border-0 h-100">
                    <div class="card-body p-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-uppercase mb-1 font-weight-bold">Menunggu</h6>
                                <h2 class="mb-0 font-weight-bold"><?php echo $stats['total_menunggu'] ?: 0; ?></h2>
                            </div>
                            <div class="icon-circle bg-white text-info">
                                <i class="fas fa-hourglass-half fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-6 mb-3">
                <div class="card bg-gradient-danger text-white shadow-sm border-0 h-100">
                    <div class="card-body p-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-uppercase mb-1 font-weight-bold">Batal</h6>
                                <h2 class="mb-0 font-weight-bold"><?php echo $stats['total_batal'] ?: 0; ?></h2>
                            </div>
                            <div class="icon-circle bg-white text-danger">
                                <i class="fas fa-times-circle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-6 mb-3">
                <div class="card bg-gradient-dark text-white shadow-sm border-0 h-100">
                    <div class="card-body p-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-uppercase mb-1 font-weight-bold">Rata-rata</h6>
                                <h2 class="mb-0 font-weight-bold"><?php echo round($stats['rata_durasi'] ?: 0); ?> <small>min</small></h2>
                            </div>
                            <div class="icon-circle bg-white text-dark">
                                <i class="fas fa-stopwatch fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card shadow-sm">
            <div class="card-header bg-gradient-dark text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-history mr-2"></i> Daftar Riwayat Antrian
                    </div>
                    <div>
                        <span class="badge badge-light">
                            <i class="fas fa-calendar-day mr-1"></i> <?php echo tanggal_indonesia($date_filter); ?>
                        </span>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-striped table-hover mb-0">
                        <thead class="bg-light">
                            <tr>
                                <th class="py-3">No. Antrian</th>
                                <th class="py-3">Nama</th>
                                <th class="py-3">Layanan</th>
                                <th class="py-3">Loket</th>
                                <th class="py-3">Petugas</th>
                                <th class="py-3">Waktu Panggil</th>
                                <th class="py-3">Waktu Selesai</th>
                                <th class="py-3">Durasi</th>
                                <th class="py-3">Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if(count($queue_history) > 0): ?>
                            <?php foreach($queue_history as $queue): ?>
                            <tr>
                                <td class="font-weight-bold"><?php echo $queue['nomor_antrian']; ?></td>
                                <td><?php echo $queue['nama']; ?></td>
                                <td><?php echo $queue['nama_poli']; ?></td>
                                <td><?php echo $queue['nama_loket']; ?></td>
                                <td><?php echo $queue['nama_petugas'] ?: '-'; ?></td>
                                <td>
                                    <i class="fas fa-clock text-muted mr-1"></i>
                                    <?php echo format_datetime($queue['waktu_dipanggil']); ?>
                                </td>
                                <td>
                                    <?php if($queue['waktu_selesai']): ?>
                                    <i class="fas fa-check-circle text-success mr-1"></i>
                                    <?php echo format_datetime($queue['waktu_selesai']); ?>
                                    <?php else: ?>
                                    <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($queue['durasi_layanan']): ?>
                                    <span class="badge badge-info">
                                        <i class="fas fa-stopwatch mr-1"></i>
                                        <?php echo $queue['durasi_layanan']; ?> menit
                                    </span>
                                    <?php else: ?>
                                    <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($queue['status'] == 'selesai'): ?>
                                    <span class="badge badge-success">
                                        <i class="fas fa-check mr-1"></i> Selesai
                                    </span>
                                    <?php elseif($queue['status'] == 'dipanggil'): ?>
                                    <span class="badge badge-warning">
                                        <i class="fas fa-user-clock mr-1"></i> Sedang Dilayani
                                    </span>
                                    <?php elseif($queue['status'] == 'batal'): ?>
                                    <span class="badge badge-danger">
                                        <i class="fas fa-times mr-1"></i> Batal
                                    </span>
                                    <?php else: ?>
                                    <span class="badge badge-secondary">
                                        <i class="fas fa-hourglass-half mr-1"></i> Menunggu
                                    </span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            <?php else: ?>
                            <tr>
                                <td colspan="9" class="text-center py-4">
                                    <div class="my-3 text-muted">
                                        <i class="fas fa-info-circle fa-3x mb-3"></i>
                                        <p>Tidak ada data riwayat antrian untuk tanggal ini.</p>
                                    </div>
                                </td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>

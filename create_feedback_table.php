<?php
// Create feedback table
require_once 'includes/functions.php';

echo "<h1>Create Feedback & Rating Table</h1>";

// Create feedback table
$sql = "CREATE TABLE IF NOT EXISTS feedback (
    id INT AUTO_INCREMENT PRIMARY KEY,
    antrian_id INT,
    nama_customer VARCHAR(100) NOT NULL,
    email_customer VARCHAR(100),
    no_hp_customer VARCHAR(20),
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    komentar TEXT,
    kategori_feedback ENUM('pelayanan', 'fasilitas', 'kecepatan', 'keramahan', 'lainnya') DEFAULT 'pelayanan',
    loket_id INT,
    tanggal_feedback DATE NOT NULL,
    waktu_feedback TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('baru', 'dibaca', 'ditindaklanjuti') DEFAULT 'baru',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (antrian_id) REFERENCES antrian(id) ON DELETE SET NULL,
    FOREIGN KEY (loket_id) REFERENCES loket(id) ON DELETE SET NULL
)";

if (query($sql)) {
    echo "<p>✅ Tabel feedback berhasil dibuat!</p>";
} else {
    echo "<p>❌ Error membuat tabel feedback</p>";
}

// Insert sample feedback data
$sample_feedback = [
    [
        'nama' => 'Ahmad Fauzi',
        'email' => '<EMAIL>',
        'no_hp' => '************',
        'rating' => 5,
        'komentar' => 'Pelayanan sangat memuaskan, petugas ramah dan cepat.',
        'kategori' => 'pelayanan',
        'loket_id' => 1
    ],
    [
        'nama' => 'Siti Nurhaliza',
        'email' => '<EMAIL>',
        'no_hp' => '************',
        'rating' => 4,
        'komentar' => 'Fasilitas bank cukup nyaman, tapi antrian agak lama.',
        'kategori' => 'fasilitas',
        'loket_id' => 4
    ],
    [
        'nama' => 'Budi Santoso',
        'email' => '<EMAIL>',
        'no_hp' => '************',
        'rating' => 3,
        'komentar' => 'Pelayanan standar, perlu peningkatan kecepatan.',
        'kategori' => 'kecepatan',
        'loket_id' => 6
    ],
    [
        'nama' => 'Dewi Lestari',
        'email' => '<EMAIL>',
        'no_hp' => '************',
        'rating' => 5,
        'komentar' => 'Petugas sangat ramah dan membantu. Terima kasih!',
        'kategori' => 'keramahan',
        'loket_id' => 2
    ],
    [
        'nama' => 'Rudi Hartono',
        'email' => '<EMAIL>',
        'no_hp' => '************',
        'rating' => 2,
        'komentar' => 'Sistem antrian perlu diperbaiki, terlalu lama menunggu.',
        'kategori' => 'lainnya',
        'loket_id' => 3
    ]
];

foreach ($sample_feedback as $feedback) {
    $sql = "INSERT INTO feedback (nama_customer, email_customer, no_hp_customer, rating, komentar, kategori_feedback, loket_id, tanggal_feedback) 
            VALUES ('{$feedback['nama']}', '{$feedback['email']}', '{$feedback['no_hp']}', {$feedback['rating']}, '{$feedback['komentar']}', '{$feedback['kategori']}', {$feedback['loket_id']}, CURDATE())";
    
    if (query($sql)) {
        echo "<p>✅ Sample feedback dari {$feedback['nama']} berhasil ditambahkan</p>";
    } else {
        echo "<p>❌ Error menambahkan feedback dari {$feedback['nama']}</p>";
    }
}

echo "<h2>Tabel Feedback & Rating berhasil dibuat!</h2>";
echo "<p><a href='admin/feedback.php'>Lihat Halaman Feedback</a></p>";
echo "<p><a href='customer/feedback.php'>Form Feedback Customer</a></p>";
?>

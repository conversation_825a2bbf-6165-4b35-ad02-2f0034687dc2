<?php
// Ticket display page
$page_title = 'Tike<PERSON> An<PERSON>an';
require_once '../includes/functions.php';

// Check if ticket exists in session
if (!isset($_SESSION['ticket'])) {
    redirect('index.php');
}

$ticket = $_SESSION['ticket'];
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - XBX TEAM</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">

    <style>
        body {
            background: linear-gradient(135deg, #1a6a83 0%, #0e4b5e 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .xbx-ticket-container {
            background-color: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            width: 100%;
            max-width: 500px;
        }
        .xbx-ticket-header {
            text-align: center;
            margin-bottom: 25px;
        }
        .xbx-ticket-header img {
            max-width: 200px;
            margin-bottom: 15px;
        }
        .xbx-ticket-header h3 {
            color: #333;
            font-weight: 600;
            margin-bottom: 5px;
        }
        .xbx-ticket-header p {
            color: #777;
            font-size: 0.9rem;
        }
        .xbx-ticket-content {
            border: 2px dashed #ddd;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 25px;
            background-color: #f8f9fa;
        }
        .xbx-ticket-number {
            font-size: 80px;
            font-weight: bold;
            text-align: center;
            color: var(--primary-color);
            margin: 25px 0;
        }
        .xbx-ticket-info {
            margin-bottom: 10px;
            color: #555;
            font-size: 16px;
        }
        .xbx-ticket-info strong {
            color: #333;
        }
        .xbx-print-section {
            text-align: center;
            margin-top: 25px;
        }
        .xbx-print-section .btn {
            padding: 10px 20px;
            border-radius: 4px;
            font-weight: 600;
            margin: 0 5px;
        }
        .xbx-print-section .btn-print {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            box-shadow: 0 4px 6px rgba(40, 167, 69, 0.2);
        }
        .xbx-print-section .btn-view {
            background: linear-gradient(135deg, #1a6a83 0%, #0e4b5e 100%);
            border: none;
            box-shadow: 0 4px 6px rgba(26, 106, 131, 0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="xbx-ticket-container">
                    <div class="xbx-ticket-header">
                        <img src="../assets/img/logo.jpeg" alt="Bank BJB" style="max-width: 200px;">
                        <h3>Bank BJB</h3>
                        <p>Tiket Antrian Anda</p>
                    </div>

                    <div class="xbx-ticket-content" id="ticket-print">
                        <div class="text-center mb-3">
                            <h4><?php echo get_setting('nama_instansi'); ?></h4>
                            <p><?php echo tanggal_indonesia(); ?></p>
                        </div>

                        <div class="xbx-ticket-number">
                            <?php echo explode('-', $ticket['nomor'])[1]; ?>
                        </div>

                        <div class="xbx-ticket-info">
                            <strong>Nomor Antrian:</strong> <?php echo $ticket['nomor']; ?>
                        </div>

                        <div class="xbx-ticket-info">
                            <strong>Nama:</strong> <?php echo $ticket['nama']; ?>
                        </div>

                        <div class="xbx-ticket-info">
                            <strong>Layanan:</strong> <?php echo $ticket['poli']; ?>
                        </div>

                        <div class="xbx-ticket-info">
                            <strong>Estimasi Waktu Tunggu:</strong> <?php echo $ticket['estimasi']; ?> menit
                        </div>

                        <div class="text-center mt-4">
                            <p>Silahkan tunggu nomor Anda dipanggil</p>
                            <p><small>Tiket ini dicetak pada: <?php echo date('d/m/Y H:i:s'); ?></small></p>
                        </div>
                    </div>

                    <div class="xbx-print-section">
                        <button class="btn" onclick="printTicket()" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; box-shadow: 0 4px 6px rgba(40, 167, 69, 0.2); padding: 10px 20px; border-radius: 4px; font-weight: 600; margin: 0 5px;">
                            <i class="fas fa-print"></i> Cetak Tiket
                        </button>

                        <a href="../display/index.php" class="btn" style="background: linear-gradient(135deg, #1a6a83 0%, #0e4b5e 100%); color: white; border: none; box-shadow: 0 4px 6px rgba(26, 106, 131, 0.2); padding: 10px 20px; border-radius: 4px; font-weight: 600; margin: 0 5px;">
                            <i class="fas fa-desktop"></i> Lihat Antrian
                        </a>
                    </div>

                    <div class="text-center mt-3">
                        <a href="../index.php" class="btn btn-link" style="color: #1a6a83;">
                            <i class="fas fa-arrow-left"></i> Kembali ke Halaman Utama
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function printTicket() {
            var printContents = document.getElementById('ticket-print').innerHTML;
            var originalContents = document.body.innerHTML;

            document.body.innerHTML = '<div style="padding: 20px;">' + printContents + '</div>';

            window.print();

            document.body.innerHTML = originalContents;
        }
    </script>
</body>
</html>

<?php
// Clear ticket from session after display
unset($_SESSION['ticket']);
?>

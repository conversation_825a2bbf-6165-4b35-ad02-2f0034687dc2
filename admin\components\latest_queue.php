<?php
/**
 * Latest Queue Component for Admin Dashboard
 *
 * This component displays the latest queue data from the database
 */

// Include database configuration if not already included
if (!function_exists('query')) {
    require_once __DIR__ . '/../../config/database.php';
    require_once __DIR__ . '/../../includes/functions.php';
}

// Define get_all_queues_today function if not exists
if (!function_exists('get_all_queues_today')) {
    function get_all_queues_today() {
        global $conn;

        // Try to get data from antrian table
        $sql = "SELECT a.id, a.nomor_antrian,
                COALESCE(n.nama, 'Walk-in Customer') as nama,
                CASE
                    WHEN a.jenis_layanan = 'teller' THEN 'Teller'
                    WHEN a.jenis_layanan = 'cs' THEN 'Customer Service'
                    WHEN a.jenis_layanan = 'kredit' THEN 'Kredit'
                    ELSE 'Layanan Lain'
                END as layanan,
                COALESCE(l.nama_loket, '-') as loket,
                a.status,
                TIME_FORMAT(a.waktu_booking, '%H:%i') as waktu_booking
                FROM antrian a
                LEFT JOIN nasabah n ON a.nasabah_id = n.id
                LEFT JOIN loket l ON a.loket_id = l.id
                WHERE a.tanggal = CURDATE()
                ORDER BY a.id DESC
                LIMIT 10";

        try {
            $result = query($sql);
            if ($result && num_rows($result) > 0) {
                return fetch_all($result);
            }
        } catch (Exception $e) {
            error_log("Error in get_all_queues_today: " . $e->getMessage());
        }

        // Return empty array if no data
        return [];
    }
}

// Get latest queues from database
$latest_queues = [];

try {
    // Check if antrian table exists first
    $table_check = query("SHOW TABLES LIKE 'antrian'");

    if ($table_check && num_rows($table_check) > 0) {
        // Get all queues for today - use simple query first
        $sql = "SELECT a.id, a.nomor_antrian, a.nama,
                CASE
                    WHEN a.jenis_layanan = 'teller' THEN 'Teller'
                    WHEN a.jenis_layanan = 'cs' THEN 'Customer Service'
                    WHEN a.jenis_layanan = 'kredit' THEN 'Kredit'
                    ELSE 'Layanan Lain'
                END as layanan,
                CASE
                    WHEN a.loket_id IS NOT NULL THEN CONCAT('Loket ', a.loket_id)
                    ELSE '-'
                END as loket,
                a.status,
                TIME_FORMAT(a.waktu_booking, '%H:%i') as waktu_booking
                FROM antrian a
                WHERE a.tanggal = CURDATE()
                ORDER BY a.id DESC
                LIMIT 10";

        $result = query($sql);

        if ($result && num_rows($result) > 0) {
            // Use while loop to get all data manually
            $latest_queues = [];
            while ($row = fetch_assoc($result)) {
                if ($row === null) break;
                $latest_queues[] = $row;
            }
        } else {
            $latest_queues = [];
        }
    } else {
        // Table doesn't exist
        $latest_queues = [];
    }
} catch (Exception $e) {
    // Log error
    error_log("Error fetching latest queues for admin: " . $e->getMessage());
    $latest_queues = [];
}

// Only show real data from database - no mock data
?>

<div class="card shadow-sm mb-4">
    <div class="card-header bg-white">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0 d-flex align-items-center">
                <i class="fas fa-list-alt text-primary mr-2"></i> Antrian Terbaru
            </h5>
            <a href="../latest_queue.php" target="_blank" class="btn btn-sm btn-outline-primary">
                <i class="fas fa-external-link-alt mr-1"></i> Lihat Semua
            </a>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="bg-light">
                    <tr>
                        <th>No. Antrian</th>
                        <th>Nama</th>
                        <th>Layanan</th>
                        <th>Loket</th>
                        <th>Status</th>
                        <th>Waktu Booking</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($latest_queues)): ?>
                        <?php foreach ($latest_queues as $queue): ?>
                        <tr>
                            <td class="font-weight-bold"><?php echo htmlspecialchars($queue['nomor_antrian']); ?></td>
                            <td><?php echo htmlspecialchars($queue['nama']); ?></td>
                            <td><?php echo htmlspecialchars($queue['layanan']); ?></td>
                            <td><?php echo htmlspecialchars($queue['loket']); ?></td>
                            <td>
                                <?php if ($queue['status'] == 'selesai'): ?>
                                <span class="badge badge-success">Selesai</span>
                                <?php elseif ($queue['status'] == 'dipanggil'): ?>
                                <span class="badge badge-info">Dipanggil</span>
                                <?php elseif ($queue['status'] == 'menunggu'): ?>
                                <span class="badge badge-warning">Menunggu</span>
                                <?php elseif ($queue['status'] == 'batal'): ?>
                                <span class="badge badge-danger">Batal</span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo htmlspecialchars($queue['waktu_booking']); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="6" class="text-center text-muted py-4">
                                <i class="fas fa-inbox fa-2x mb-2"></i><br>
                                Belum ada data antrian hari ini
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

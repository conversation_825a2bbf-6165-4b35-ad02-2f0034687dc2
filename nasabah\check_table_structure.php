<?php
// <PERSON><PERSON><PERSON> to check nasabah table structure
require_once '../includes/db.php';

// Get table structure
$result = $conn->query("DESCRIBE nasabah");

if ($result) {
    echo "<h2>Struktur Tabel Nasabah</h2>";
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "Error getting table structure: " . $conn->error;
}

// Check required columns for registration
$required_columns = ['nama', 'no_identitas', 'jenis_identitas', 'email', 'no_hp', 'password', 'alamat', 'status_verifikasi'];
$missing_columns = [];

foreach ($required_columns as $column) {
    $result = $conn->query("SHOW COLUMNS FROM nasabah LIKE '$column'");
    if ($result->num_rows == 0) {
        $missing_columns[] = $column;
    }
}

if (empty($missing_columns)) {
    echo "<p style='color: green;'>All required columns exist in the nasabah table.</p>";
} else {
    echo "<p style='color: red;'>Missing columns: " . implode(", ", $missing_columns) . "</p>";
}

echo "<br><br><a href='register.php'>Go to Registration Page</a>";
?>

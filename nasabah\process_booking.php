<?php
// Process booking form
require_once '../includes/functions.php';
require_once '../includes/db.php';
require_once '../includes/queue_connector.php';

// Check if user is logged in as nasabah
if (!isset($_SESSION['nasabah_id'])) {
    // Redirect to login page
    redirect('login.php');
}

// Check if form is submitted
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Get form data
    $nasabah_id = isset($_POST['nasabah_id']) ? $_POST['nasabah_id'] : $_SESSION['nasabah_id'];
    $layanan = isset($_POST['layanan']) ? $_POST['layanan'] : '';
    $tanggal = isset($_POST['tanggal']) ? $_POST['tanggal'] : '';
    $waktu = isset($_POST['waktu']) ? $_POST['waktu'] : '';
    $keterangan = isset($_POST['keterangan']) ? $_POST['keterangan'] : '';

    // Validate form data
    if (empty($layanan) || empty($tanggal) || empty($waktu)) {
        $_SESSION['error_message'] = 'Semua field harus diisi.';
        redirect('dashboard.php');
    }

    // Check if file is uploaded
    if (isset($_FILES['file_upload']) && $_FILES['file_upload']['error'] == 0) {
        // In a real application, you would process the file upload
        // For mock version, we'll just assume it's successful
        $file_name = $_FILES['file_upload']['name'];
    } else {
        // File upload is required
        $_SESSION['error_message'] = 'File identitas harus diupload.';
        redirect('dashboard.php');
    }

    // Generate booking ID and queue number
    $booking_id = 'BK' . date('Ymd') . rand(1000, 9999);
    $nomor_antrian = generate_queue_number($layanan);

    // Dapatkan data nasabah
    $sql = "SELECT * FROM nasabah WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $nasabah_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $nasabah = $result->fetch_assoc();

    // Simpan booking ke database
    $sql = "INSERT INTO bookings (
        booking_id,
        nasabah_id,
        nama,
        no_identitas,
        email,
        no_hp,
        layanan,
        tanggal,
        waktu,
        keterangan,
        file_identitas,
        nomor_antrian,
        status,
        created_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', NOW())";

    $stmt = $conn->prepare($sql);
    $stmt->bind_param(
        "sissssssssss",
        $booking_id,
        $nasabah_id,
        $nasabah['nama'],
        $nasabah['no_identitas'],
        $nasabah['email'],
        $nasabah['no_hp'],
        $layanan,
        $tanggal,
        $waktu,
        $keterangan,
        $file_name,
        $nomor_antrian
    );

    if ($stmt->execute()) {
        // Buat antrian dari booking
        $antrian_id = create_queue_from_booking($booking_id, $nomor_antrian, $tanggal, $nasabah_id, $layanan);

        if ($antrian_id) {
            $_SESSION['booking_id'] = $booking_id;
            $_SESSION['nomor_antrian'] = $nomor_antrian;
            $_SESSION['success_message'] = 'Booking berhasil dibuat. Nomor antrian Anda: ' . $nomor_antrian;
        } else {
            $_SESSION['success_message'] = 'Booking berhasil dibuat, tetapi antrian belum dibuat. Silahkan hubungi admin.';
        }
    } else {
        $_SESSION['error_message'] = 'Gagal membuat booking. Silahkan coba lagi.';
        redirect('dashboard.php');
    }

    // Redirect to dashboard
    redirect('dashboard.php');
} else {
    // If not submitted via POST, redirect to dashboard
    redirect('dashboard.php');
}

/**
 * Function to generate queue number based on service type
 *
 * @param string $layanan
 * @return string
 */
function generate_queue_number($layanan) {
    $prefix = '';

    switch ($layanan) {
        case 'teller':
            $prefix = 'T';
            break;
        case 'cs':
            $prefix = 'C';
            break;
        case 'kredit':
            $prefix = 'K';
            break;
        default:
            $prefix = 'A';
    }

    // Generate a random number between 1 and 100
    $number = rand(1, 100);

    // Format the number with leading zeros
    return $prefix . str_pad($number, 3, '0', STR_PAD_LEFT);
}
?>

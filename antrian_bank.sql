-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.0
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: May 12, 2025 at 04:56 PM
-- Server version: 8.0.30
-- PHP Version: 8.1.10

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `antrian_bank`
--

-- --------------------------------------------------------

--
-- Table structure for table `antrian`
--

CREATE TABLE `antrian` (
  `id` int NOT NULL,
  `booking_id` varchar(50) DEFAULT NULL,
  `nomor_antrian` varchar(10) NOT NULL,
  `tanggal` date NOT NULL,
  `nasabah_id` int DEFAULT NULL,
  `poli_id` int NOT NULL,
  `jenis_layanan` enum('teller','cs','kredit') DEFAULT NULL,
  `loket_id` int DEFAULT NULL,
  `status` enum('menunggu','dipanggil','selesai','batal') DEFAULT 'menunggu',
  `waktu_booking` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `waktu_dipanggil` timestamp NULL DEFAULT NULL,
  `waktu_selesai` timestamp NULL DEFAULT NULL,
  `estimasi_waktu` int DEFAULT NULL,
  `booking_dari` enum('lokal','online') DEFAULT 'lokal',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `antrian`
--

INSERT INTO `antrian` (`id`, `booking_id`, `nomor_antrian`, `tanggal`, `nasabah_id`, `poli_id`, `jenis_layanan`, `loket_id`, `status`, `waktu_booking`, `waktu_dipanggil`, `waktu_selesai`, `estimasi_waktu`, `booking_dari`, `created_at`, `updated_at`) VALUES
(1, 'BK202505121490', 'C001', '2025-05-13', NULL, 2, 'cs', 4, 'menunggu', '2025-05-12 13:29:37', NULL, NULL, NULL, 'online', '2025-05-12 13:29:37', '2025-05-12 13:29:37'),
(2, 'BK202505126488', 'T001', '2025-05-12', NULL, 1, 'teller', 1, 'selesai', '2025-05-12 13:29:43', '2025-05-12 13:30:23', '2025-05-12 13:47:12', NULL, 'online', '2025-05-12 13:29:43', '2025-05-12 13:47:12'),
(3, 'BK202505128136', 'C001', '2025-05-12', NULL, 2, 'cs', 4, 'dipanggil', '2025-05-12 13:34:26', '2025-05-12 13:34:44', NULL, NULL, 'online', '2025-05-12 13:34:26', '2025-05-12 13:34:44'),
(4, 'BK202505129635', 'T001', '2025-05-12', 4, 1, 'teller', 1, 'selesai', '2025-05-12 13:46:28', '2025-05-12 13:47:24', '2025-05-12 13:47:44', NULL, 'online', '2025-05-12 13:46:28', '2025-05-12 13:47:44');

-- --------------------------------------------------------

--
-- Table structure for table `blocked_ips`
--

CREATE TABLE `blocked_ips` (
  `id` int NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `alasan` text,
  `tanggal_blokir` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Table structure for table `bookings`
--

CREATE TABLE `bookings` (
  `id` int NOT NULL,
  `booking_id` varchar(20) NOT NULL,
  `nasabah_id` int NOT NULL,
  `nama` varchar(100) NOT NULL,
  `no_identitas` varchar(20) NOT NULL,
  `email` varchar(100) NOT NULL,
  `no_hp` varchar(15) NOT NULL,
  `layanan` varchar(20) NOT NULL,
  `tanggal` date NOT NULL,
  `waktu` varchar(20) NOT NULL,
  `keterangan` text,
  `file_identitas` varchar(255) NOT NULL,
  `nomor_antrian` varchar(10) NOT NULL,
  `status` enum('pending','confirmed','completed','cancelled') NOT NULL DEFAULT 'pending',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `bookings`
--

INSERT INTO `bookings` (`id`, `booking_id`, `nasabah_id`, `nama`, `no_identitas`, `email`, `no_hp`, `layanan`, `tanggal`, `waktu`, `keterangan`, `file_identitas`, `nomor_antrian`, `status`, `created_at`, `updated_at`) VALUES
(4, 'BK202505129635', 4, 'vina', '2222222222222222', '<EMAIL>', '0858857211444', 'teller', '2025-05-12', '14:00', 'sfsafs', 'doc_6821fb702cfa7.jpg', 'T001', 'confirmed', '2025-05-12 13:45:20', '2025-05-12 13:46:28');

-- --------------------------------------------------------

--
-- Table structure for table `loket`
--

CREATE TABLE `loket` (
  `id` int NOT NULL,
  `nama_loket` varchar(50) NOT NULL,
  `jenis_layanan` enum('teller','cs','kredit') DEFAULT NULL,
  `status` enum('aktif','nonaktif') DEFAULT 'aktif',
  `user_id` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `loket`
--

INSERT INTO `loket` (`id`, `nama_loket`, `jenis_layanan`, `status`, `user_id`, `created_at`, `updated_at`) VALUES
(1, 'Loket 1', 'teller', 'aktif', 3, '2025-05-12 12:32:22', '2025-05-12 13:19:35'),
(2, 'Loket 2', 'teller', 'aktif', NULL, '2025-05-12 12:32:22', '2025-05-12 12:35:20'),
(3, 'Loket 3', 'teller', 'aktif', NULL, '2025-05-12 12:32:22', '2025-05-12 12:35:20'),
(4, 'Loket 4', 'cs', 'aktif', 4, '2025-05-12 12:32:22', '2025-05-12 13:19:44'),
(5, 'Loket 5', 'cs', 'aktif', NULL, '2025-05-12 12:35:43', '2025-05-12 12:35:43'),
(6, 'Loket 6', 'kredit', 'aktif', 5, '2025-05-12 12:35:43', '2025-05-12 13:19:52'),
(7, 'Loket 7', 'kredit', 'aktif', NULL, '2025-05-12 12:35:43', '2025-05-12 12:35:43');

-- --------------------------------------------------------

--
-- Table structure for table `nasabah`
--

CREATE TABLE `nasabah` (
  `id` int NOT NULL,
  `nama` varchar(100) NOT NULL,
  `no_identitas` varchar(20) NOT NULL,
  `jenis_identitas` enum('ktp','sim','passport') NOT NULL DEFAULT 'ktp',
  `file_identitas` varchar(255) DEFAULT NULL,
  `no_hp` varchar(15) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `alamat` text NOT NULL,
  `status_verifikasi` enum('belum','sudah','ditolak') DEFAULT 'belum',
  `alasan_penolakan` text,
  `is_blocked` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `nasabah`
--

INSERT INTO `nasabah` (`id`, `nama`, `no_identitas`, `jenis_identitas`, `file_identitas`, `no_hp`, `email`, `password`, `alamat`, `status_verifikasi`, `alasan_penolakan`, `is_blocked`, `created_at`, `updated_at`) VALUES
(4, 'vina', '2222222222222222', 'ktp', NULL, '0858857211444', '<EMAIL>', '$2y$10$qyJ15rip98nQuLpWF1TzDuUX5gy60hRglsXTtF5Oq9jOgl7xcmIrm', 'serang', 'sudah', NULL, 0, '2025-05-12 13:42:53', '2025-05-12 13:44:45');

-- --------------------------------------------------------

--
-- Table structure for table `pengaturan`
--

CREATE TABLE `pengaturan` (
  `id` int NOT NULL,
  `nama_pengaturan` varchar(50) NOT NULL,
  `nilai` text NOT NULL,
  `deskripsi` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `pengaturan`
--

INSERT INTO `pengaturan` (`id`, `nama_pengaturan`, `nilai`, `deskripsi`, `created_at`, `updated_at`) VALUES
(1, 'nama_instansi', 'Bank BJB Kantor Cabang Khusus Banten', 'Nama instansi yang ditampilkan di aplikasi', '2025-05-12 12:32:22', '2025-05-12 12:32:22'),
(2, 'logo_instansi', 'logo.png', 'Logo instansi', '2025-05-12 12:32:22', '2025-05-12 12:32:22'),
(3, 'jam_operasional', '08:00-16:00', 'Jam operasional pelayanan', '2025-05-12 12:32:22', '2025-05-12 12:32:22'),
(4, 'max_antrian', '100', 'Jumlah maksimal antrian per hari', '2025-05-12 12:32:22', '2025-05-12 12:32:22'),
(5, 'verifikasi_wajib', '1', 'Apakah verifikasi identitas wajib (1=ya, 0=tidak)', '2025-05-12 12:32:22', '2025-05-12 12:32:22');

-- --------------------------------------------------------

--
-- Table structure for table `poli`
--

CREATE TABLE `poli` (
  `id` int NOT NULL,
  `nama_poli` varchar(100) NOT NULL,
  `deskripsi` text,
  `estimasi_waktu` int DEFAULT '10',
  `status` enum('aktif','nonaktif') DEFAULT 'aktif',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `poli`
--

INSERT INTO `poli` (`id`, `nama_poli`, `deskripsi`, `estimasi_waktu`, `status`, `created_at`, `updated_at`) VALUES
(1, 'Teller', 'Layanan transaksi tunai dan non-tunai', 10, 'aktif', '2025-05-12 12:32:22', '2025-05-12 12:32:22'),
(2, 'Customer Service', 'Layanan pembukaan rekening dan keluhan', 15, 'aktif', '2025-05-12 12:32:22', '2025-05-12 12:32:22'),
(3, 'Kredit', 'Layanan pengajuan kredit', 20, 'aktif', '2025-05-12 12:32:22', '2025-05-12 12:32:22');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `nama_lengkap` varchar(100) NOT NULL,
  `role` enum('admin','staff') NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `username`, `password`, `nama_lengkap`, `role`, `created_at`, `updated_at`) VALUES
(1, 'admin', '$2y$10$8tDrKCDlGK6.Qqz3OMO.9.Yw0rDEhV.IYrTIHqOeQQqkJjrUFQcJi', 'Administrator', 'admin', '2025-05-12 12:32:22', '2025-05-12 12:32:22'),
(2, 'petugas', '$2y$10$Ql9XZz3MXZzrTWsD5dF9Ue9Q7rDIqSjIJQKY3jTYxLB1bVRY0qJHe', 'Petugas Loket', 'staff', '2025-05-12 12:32:22', '2025-05-12 12:32:22'),
(3, 'siti', '$2y$10$uj8oBigUThxXfwRo7UuNcuoKOk3SBcqKrp.bsNzSHTo07rRdij9ca', 'siti munawaroh', 'staff', '2025-05-12 13:19:02', '2025-05-12 13:19:02'),
(4, 'vina', '$2y$10$n4Gr7BlzNAaSfoGccsvc/OP4f.7Ua5kwkZUub4nnVF5QyF8M8mjSC', 'vinaaaaaaa', 'staff', '2025-05-12 13:19:13', '2025-05-12 13:19:13'),
(5, 'srii', '$2y$10$kG75jGoeDGn3Sxwoh./oO.0xUzkGrIYl6okFPexK/mUtIBKTTFOpe', 'sri wah', 'staff', '2025-05-12 13:19:25', '2025-05-12 13:19:25');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `antrian`
--
ALTER TABLE `antrian`
  ADD PRIMARY KEY (`id`),
  ADD KEY `nasabah_id` (`nasabah_id`),
  ADD KEY `poli_id` (`poli_id`),
  ADD KEY `loket_id` (`loket_id`);

--
-- Indexes for table `blocked_ips`
--
ALTER TABLE `blocked_ips`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `bookings`
--
ALTER TABLE `bookings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `booking_id` (`booking_id`),
  ADD KEY `nasabah_id` (`nasabah_id`);

--
-- Indexes for table `loket`
--
ALTER TABLE `loket`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `nasabah`
--
ALTER TABLE `nasabah`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD UNIQUE KEY `no_identitas` (`no_identitas`);

--
-- Indexes for table `pengaturan`
--
ALTER TABLE `pengaturan`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `nama_pengaturan` (`nama_pengaturan`);

--
-- Indexes for table `poli`
--
ALTER TABLE `poli`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `antrian`
--
ALTER TABLE `antrian`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `blocked_ips`
--
ALTER TABLE `blocked_ips`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `bookings`
--
ALTER TABLE `bookings`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `loket`
--
ALTER TABLE `loket`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `nasabah`
--
ALTER TABLE `nasabah`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `pengaturan`
--
ALTER TABLE `pengaturan`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `poli`
--
ALTER TABLE `poli`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `antrian`
--
ALTER TABLE `antrian`
  ADD CONSTRAINT `antrian_ibfk_1` FOREIGN KEY (`nasabah_id`) REFERENCES `nasabah` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `antrian_ibfk_2` FOREIGN KEY (`poli_id`) REFERENCES `poli` (`id`),
  ADD CONSTRAINT `antrian_ibfk_3` FOREIGN KEY (`loket_id`) REFERENCES `loket` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `bookings`
--
ALTER TABLE `bookings`
  ADD CONSTRAINT `bookings_ibfk_1` FOREIGN KEY (`nasabah_id`) REFERENCES `nasabah` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `loket`
--
ALTER TABLE `loket`
  ADD CONSTRAINT `loket_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;

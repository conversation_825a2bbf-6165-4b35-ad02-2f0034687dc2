<?php
// Settings page
$page_title = 'Pengaturan Sistem';
$active_menu = 'pengaturan';
$is_admin = true;
require_once '../includes/functions.php';

// Check if user is logged in
if (!is_logged_in()) {
    redirect('../login_admin.php');
}

// Check if user is admin
if (!is_admin()) {
    redirect('../index.php');
}

// Process form submission (mock)
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Update general settings
    if (isset($_POST['update_settings'])) {
        $_SESSION['success_message'] = "Pengaturan berhasil diperbarui.";
        redirect('./pengaturan.php');
    }

    // Update logo
    if (isset($_POST['update_logo'])) {
        $_SESSION['success_message'] = "Logo berhasil diperbarui.";
        redirect('./pengaturan.php');
    }
}

// Mock settings
$nama_instansi = 'Bank BJB Kantor Cabang Khus<PERSON>';
$logo_instansi = 'logo.jpeg';
$jam_operasional = '08:00-16:00';
$max_antrian = '100';
$verifikasi_wajib = '1';

// Parse jam operasional
$jam = explode('-', $jam_operasional);
$jam_buka = isset($jam[0]) ? $jam[0] : '08:00';
$jam_tutup = isset($jam[1]) ? $jam[1] : '16:00';

// Include header
include_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <h1 class="mb-4">Pengaturan Sistem</h1>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <i class="fas fa-cogs"></i> Pengaturan Umum
                </div>
                <div class="card-body">
                    <form method="post" action="">
                        <div class="form-group">
                            <label for="nama_instansi">Nama Instansi</label>
                            <input type="text" class="form-control" id="nama_instansi" name="nama_instansi" value="<?php echo $nama_instansi; ?>" required>
                        </div>

                        <div class="form-group">
                            <label>Jam Operasional</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="jam_buka">Jam Buka</label>
                                    <input type="time" class="form-control" id="jam_buka" name="jam_buka" value="<?php echo $jam_buka; ?>" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="jam_tutup">Jam Tutup</label>
                                    <input type="time" class="form-control" id="jam_tutup" name="jam_tutup" value="<?php echo $jam_tutup; ?>" required>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="max_antrian">Maksimal Antrian per Hari</label>
                            <input type="number" class="form-control" id="max_antrian" name="max_antrian" value="<?php echo $max_antrian; ?>" required>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="verifikasi_wajib" name="verifikasi_wajib" <?php echo $verifikasi_wajib == 1 ? 'checked' : ''; ?>>
                                <label class="custom-control-label" for="verifikasi_wajib">Verifikasi Identitas Wajib</label>
                            </div>
                            <small class="form-text text-muted">Jika diaktifkan, nasabah harus diverifikasi sebelum dapat mengambil antrian.</small>
                        </div>

                        <button type="submit" name="update_settings" class="btn btn-primary">
                            <i class="fas fa-save"></i> Simpan Pengaturan
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <i class="fas fa-image"></i> Logo Instansi
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <img src="<?php echo '../assets/img/' . $logo_instansi; ?>" alt="Logo" class="img-thumbnail" style="max-height: 150px;">
                    </div>

                    <form method="post" action="" enctype="multipart/form-data">
                        <div class="form-group">
                            <label for="logo">Upload Logo Baru</label>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input" id="logo" name="logo" required>
                                <label class="custom-file-label" for="logo">Pilih file...</label>
                            </div>
                            <small class="form-text text-muted">Format yang didukung: JPG, PNG, GIF. Ukuran maksimal: 2MB.</small>
                        </div>

                        <button type="submit" name="update_logo" class="btn btn-info">
                            <i class="fas fa-upload"></i> Upload Logo
                        </button>
                    </form>
                </div>
            </div>

            <div class="card">
                <div class="card-header bg-success text-white">
                    <i class="fas fa-info-circle"></i> Informasi Sistem
                </div>
                <div class="card-body">
                    <table class="table table-striped">
                        <tr>
                            <th>Versi Aplikasi</th>
                            <td>1.0.0</td>
                        </tr>
                        <tr>
                            <th>PHP Version</th>
                            <td><?php echo phpversion(); ?></td>
                        </tr>
                        <tr>
                            <th>Database</th>
                            <td>MySQL</td>
                        </tr>
                        <tr>
                            <th>Server</th>
                            <td><?php echo $_SERVER['SERVER_SOFTWARE']; ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Update file input label with selected filename
    document.querySelector('.custom-file-input').addEventListener('change', function(e) {
        var fileName = e.target.files[0].name;
        var nextSibling = e.target.nextElementSibling;
        nextSibling.innerText = fileName;
    });
</script>

<?php
// Include footer
include_once 'includes/footer.php';
?>

<?php
/**
 * Password Hash Generator
 * 
 * This script generates a password hash that can be used in the database.
 * For security reasons, this file should be deleted after use.
 */

// Check if form is submitted
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['password'])) {
    $password = $_POST['password'];
    $hash = password_hash($password, PASSWORD_DEFAULT);
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generate Password Hash</title>
    
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    
    <style>
        body {
            background-color: #f8f9fa;
            padding: 50px 0;
        }
        .container {
            max-width: 500px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Password Hash Generator</h1>
            <p class="text-muted">Generate a password hash for database insertion</p>
        </div>
        
        <form method="post">
            <div class="form-group">
                <label for="password">Password</label>
                <input type="text" class="form-control" id="password" name="password" required>
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn btn-primary btn-block">Generate Hash</button>
            </div>
        </form>
        
        <?php if (isset($hash)): ?>
        <div class="alert alert-success mt-4">
            <h5>Generated Hash:</h5>
            <div class="form-group">
                <input type="text" class="form-control" value="<?php echo $hash; ?>" readonly>
            </div>
            <p class="mb-0">
                <small>You can use this hash in your database for the password field.</small>
            </p>
        </div>
        
        <div class="alert alert-info">
            <h5>SQL Insert Example:</h5>
            <pre>INSERT INTO users (username, password, nama_lengkap, role) 
VALUES ('username', '<?php echo $hash; ?>', 'Nama Lengkap', 'staff');</pre>
        </div>
        <?php endif; ?>
        
        <div class="alert alert-warning mt-4">
            <strong>Warning:</strong> Delete this file after use for security reasons.
        </div>
    </div>
</body>
</html>

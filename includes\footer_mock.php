<?php if(!isset($no_header) && is_logged_in()): ?>
        </div>
    </div>
<?php else: ?>
        </div>
    </main>
<?php endif; ?>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

<!-- Custom JS -->
<script>
    $(document).ready(function() {
        // Sidebar toggle
        $('#sidebarCollapse').on('click', function() {
            $('#sidebar').toggleClass('active');
            $('#content').toggleClass('active');
        });
        
        // Tooltip initialization
        $('[data-toggle="tooltip"]').tooltip();
        
        // Popover initialization
        $('[data-toggle="popover"]').popover();
        
        // Custom file input
        $('.custom-file-input').on('change', function() {
            var fileName = $(this).val().split('\\').pop();
            $(this).next('.custom-file-label').html(fileName);
        });
    });
</script>

<?php if(isset($extra_js)): ?>
<?php echo $extra_js; ?>
<?php endif; ?>

</body>
</html>

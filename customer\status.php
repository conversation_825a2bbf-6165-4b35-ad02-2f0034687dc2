<?php
// Status page for customers to check their queue status
$page_title = 'Cek Status Antrian';
require_once '../includes/functions.php';

// Initialize variables
$queue_number = '';
$queue_info = null;
$errors = [];

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate input
    $queue_number = trim($_POST['queue_number'] ?? '');

    // Validation
    if (empty($queue_number)) {
        $errors[] = 'Nomor antrian harus diisi';
    }

    // If no errors, proceed with checking status
    if (empty($errors)) {
        // Connect to database
        $conn = db_connect();
        
        // Get queue information
        $stmt = $conn->prepare("
            SELECT q.*, s.name as service_name, c.name as counter_name, 
                   (SELECT COUNT(*) FROM queue 
                    WHERE service_id = q.service_id 
                    AND date = q.date 
                    AND number < q.number 
                    AND status = 'waiting') as queue_ahead
            FROM queue q
            LEFT JOIN services s ON q.service_id = s.id
            LEFT JOIN counters c ON q.counter_id = c.id
            WHERE q.queue_number = ? AND q.date = CURDATE()
        ");
        $stmt->bind_param("s", $queue_number);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $queue_info = $result->fetch_assoc();
        } else {
            $errors[] = 'Nomor antrian tidak ditemukan atau sudah tidak berlaku.';
        }
        
        $stmt->close();
        $conn->close();
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cek Status Antrian - Bank BJB</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">

    <style>
        body {
            background: linear-gradient(135deg, #1a6a83 0%, #0e4b5e 100%);
            min-height: 100vh;
        }
        .status-box {
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 8px;
            padding: 30px;
            margin-top: 50px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        .status-header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .status-header img {
            height: 80px;
            margin-bottom: 20px;
        }
        .queue-info {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        .queue-number {
            font-size: 36px;
            font-weight: bold;
            color: #1a6a83;
            text-align: center;
            margin-bottom: 20px;
        }
        .queue-status {
            font-size: 18px;
            text-align: center;
            margin-bottom: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .status-waiting {
            background-color: #fff3cd;
            color: #856404;
        }
        .status-serving {
            background-color: #d4edda;
            color: #155724;
        }
        .status-completed {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        .status-skipped {
            background-color: #f8d7da;
            color: #721c24;
        }
        .queue-details {
            margin-top: 20px;
        }
        .queue-details p {
            margin-bottom: 10px;
        }
        .estimated-time {
            font-weight: bold;
            color: #1a6a83;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="status-box">
                    <div class="status-header">
                        <img src="../assets/img/logo.jpeg" alt="Bank BJB" style="max-width: 200px;">
                        <h2>Cek Status Antrian</h2>
                        <p class="text-muted">Masukkan nomor antrian Anda untuk melihat status</p>
                    </div>

                    <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                            <li><?php echo $error; ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <?php endif; ?>

                    <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>">
                        <div class="form-group">
                            <label for="queue_number"><i class="fas fa-ticket-alt mr-2"></i>Nomor Antrian</label>
                            <input type="text" class="form-control" id="queue_number" name="queue_number" value="<?php echo htmlspecialchars($queue_number); ?>" placeholder="Contoh: A001" required>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary btn-block"><i class="fas fa-search mr-2"></i>Cek Status</button>
                        </div>
                    </form>

                    <?php if ($queue_info): ?>
                    <div class="queue-info">
                        <div class="queue-number"><?php echo htmlspecialchars($queue_info['queue_number']); ?></div>
                        
                        <?php
                        $status_class = '';
                        $status_text = '';
                        
                        switch ($queue_info['status']) {
                            case 'waiting':
                                $status_class = 'status-waiting';
                                $status_text = 'Menunggu';
                                break;
                            case 'serving':
                                $status_class = 'status-serving';
                                $status_text = 'Sedang Dilayani';
                                break;
                            case 'completed':
                                $status_class = 'status-completed';
                                $status_text = 'Selesai';
                                break;
                            case 'skipped':
                                $status_class = 'status-skipped';
                                $status_text = 'Terlewat';
                                break;
                            default:
                                $status_class = '';
                                $status_text = $queue_info['status'];
                        }
                        ?>
                        
                        <div class="queue-status <?php echo $status_class; ?>">
                            <i class="fas fa-info-circle mr-2"></i>Status: <?php echo $status_text; ?>
                        </div>
                        
                        <div class="queue-details">
                            <p><i class="fas fa-cogs mr-2"></i><strong>Layanan:</strong> <?php echo htmlspecialchars($queue_info['service_name']); ?></p>
                            
                            <?php if ($queue_info['status'] == 'waiting'): ?>
                            <p><i class="fas fa-users mr-2"></i><strong>Jumlah Antrian di Depan Anda:</strong> <?php echo $queue_info['queue_ahead']; ?></p>
                            <p><i class="fas fa-clock mr-2"></i><strong>Estimasi Waktu Tunggu:</strong> <span class="estimated-time"><?php echo $queue_info['queue_ahead'] * 5; ?> menit</span></p>
                            <?php endif; ?>
                            
                            <?php if ($queue_info['status'] == 'serving'): ?>
                            <p><i class="fas fa-desktop mr-2"></i><strong>Counter:</strong> <?php echo htmlspecialchars($queue_info['counter_name']); ?></p>
                            <?php endif; ?>
                            
                            <p><i class="fas fa-calendar-alt mr-2"></i><strong>Tanggal:</strong> <?php echo date('d F Y', strtotime($queue_info['date'])); ?></p>
                            <p><i class="fas fa-clock mr-2"></i><strong>Waktu Pengambilan Nomor:</strong> <?php echo date('H:i', strtotime($queue_info['created_at'])); ?></p>
                            
                            <?php if ($queue_info['called_at']): ?>
                            <p><i class="fas fa-bell mr-2"></i><strong>Waktu Dipanggil:</strong> <?php echo date('H:i', strtotime($queue_info['called_at'])); ?></p>
                            <?php endif; ?>
                            
                            <?php if ($queue_info['completed_at']): ?>
                            <p><i class="fas fa-check-circle mr-2"></i><strong>Waktu Selesai:</strong> <?php echo date('H:i', strtotime($queue_info['completed_at'])); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <div class="text-center mt-4">
                        <a href="../index.php" class="btn btn-outline-secondary"><i class="fas fa-home mr-2"></i>Kembali ke Beranda</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

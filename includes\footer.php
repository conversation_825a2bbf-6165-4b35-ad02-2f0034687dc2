<?php if(!isset($no_header) && is_logged_in()): ?>
        </div>
    </div>
<?php else: ?>
        </div>
    </main>
<?php endif; ?>

    <footer class="bg-xbx text-white" style="position: fixed; bottom: 0; width: 100%; z-index: 100; left: 0; right: 0; height: 25px; display: flex; align-items: center;">
        <div class="container-fluid">
            <div class="row align-items-center">
                <?php if(is_logged_in()): ?>
                <div class="col-md-6 text-left" style="padding-left: 270px;">
                    <span style="font-size: 11px;">&copy; <?php echo date('Y'); ?> Bank BJB</span>
                </div>
                <div class="col-md-6 text-right" style="padding-right: 20px;">
                    <span style="font-size: 11px;">Sistem Antrian Bank BJB</span>
                </div>
                <?php else: ?>
                <div class="col-md-6 text-left" style="padding-left: 20px;">
                    <span style="font-size: 11px;">&copy; <?php echo date('Y'); ?> Bank BJB</span>
                </div>
                <div class="col-md-6 text-right" style="padding-right: 20px;">
                    <span style="font-size: 11px;">Sistem Antrian Bank BJB</span>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS (jQuery already loaded in header) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Adjust footer when sidebar is toggled
        $(document).ready(function() {
            function adjustFooter() {
                if ($(window).width() <= 768 || $('#sidebar').hasClass('active')) {
                    $('footer .col-md-6.text-left').css('padding-left', '20px');
                } else {
                    $('footer .col-md-6.text-left').css('padding-left', '270px');
                }
            }

            // Initial adjustment
            adjustFooter();

            // Adjust when sidebar is toggled
            $('#sidebarCollapse').on('click', function() {
                setTimeout(adjustFooter, 50);
            });

            // Adjust on window resize
            $(window).resize(function() {
                adjustFooter();
            });
        });
    </script>

    <!-- Custom JS -->
    <script src="<?php echo isset($is_admin) ? '../' : ''; ?>assets/js/script.js"></script>

    <!-- Navbar Clock Script -->
    <?php if(is_logged_in()): ?>
    <script>
        $(document).ready(function () {
            // Update navbar clock
            function updateNavbarClock() {
                var now = new Date();
                var hours = now.getHours();
                var minutes = now.getMinutes();
                var seconds = now.getSeconds();

                // Add leading zeros
                hours = (hours < 10) ? "0" + hours : hours;
                minutes = (minutes < 10) ? "0" + minutes : minutes;
                seconds = (seconds < 10) ? "0" + seconds : seconds;

                // Display the time
                $('#navbar-time').text(hours + ":" + minutes + ":" + seconds);

                // Update every second
                setTimeout(updateNavbarClock, 1000);
            }

            // Start the clock
            updateNavbarClock();
        });
    </script>
    <?php endif; ?>

    <?php if(isset($extra_js)): ?>
    <?php echo $extra_js; ?>
    <?php endif; ?>
</body>
</html>

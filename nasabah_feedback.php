<?php
// Nasabah feedback form - Public access
$page_title = 'Feedback & Rating Pelayanan';
require_once 'includes/functions.php';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $nama = sanitize($_POST['nama']);
    $email = sanitize($_POST['email']);
    $no_hp = sanitize($_POST['no_hp']);
    $rating = (int)$_POST['rating'];
    $komentar = sanitize($_POST['komentar']);
    $kategori = sanitize($_POST['kategori']);
    $loket_id = !empty($_POST['loket_id']) ? (int)$_POST['loket_id'] : null;
    
    // Validate input
    $errors = [];
    if (empty($nama)) $errors[] = 'Nama harus diisi';
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) $errors[] = 'Email tidak valid';
    if (empty($no_hp)) $errors[] = 'Nomor HP harus diisi';
    if ($rating < 1 || $rating > 5) $errors[] = 'Rating harus antara 1-5';
    if (empty($komentar)) $errors[] = 'Komentar harus diisi';
    if (empty($kategori)) $errors[] = 'Kategori feedback harus dipilih';
    
    if (empty($errors)) {
        $sql = "INSERT INTO feedback (nama_customer, email_customer, no_hp_customer, rating, komentar, kategori_feedback, loket_id, tanggal_feedback) 
                VALUES ('$nama', '$email', '$no_hp', $rating, '$komentar', '$kategori', " . ($loket_id ? $loket_id : 'NULL') . ", CURDATE())";
        
        if (query($sql)) {
            $_SESSION['success_message'] = 'Terima kasih! Feedback Anda telah berhasil dikirim dan akan ditinjau oleh tim manajemen.';
            redirect('nasabah_feedback.php');
        } else {
            $_SESSION['error_message'] = 'Gagal mengirim feedback. Silakan coba lagi.';
        }
    } else {
        $_SESSION['error_message'] = implode('<br>', $errors);
    }
}

// Get active counters for loket selection
$sql = "SELECT * FROM loket WHERE status = 'aktif' ORDER BY id";
$result = query($sql);
$loket_list = fetch_all($result);
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $page_title ?> - Bank BJB</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #1a6a83 0%, #0d3c4e 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .feedback-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
            margin: 50px auto;
            max-width: 800px;
        }
        
        .feedback-header {
            background: linear-gradient(135deg, #1a6a83 0%, #0d3c4e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .feedback-header h1 {
            margin: 0;
            font-size: 2rem;
            font-weight: 600;
        }
        
        .feedback-header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        
        .star-rating {
            display: flex;
            flex-direction: row-reverse;
            justify-content: center;
            gap: 5px;
        }

        .star-rating input {
            display: none;
        }

        .star-rating label {
            font-size: 2.5rem;
            color: #ddd;
            cursor: pointer;
            transition: all 0.3s;
        }

        .star-rating label:hover,
        .star-rating label:hover ~ label,
        .star-rating input:checked ~ label {
            color: #ffc107;
            transform: scale(1.1);
        }

        .rating-container {
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            background-color: #f8f9fa;
            text-align: center;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #1a6a83 0%, #0d3c4e 100%);
            border: none;
            padding: 12px 30px;
            font-weight: 600;
            border-radius: 8px;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #0d3c4e 0%, #1a6a83 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #1a6a83;
            box-shadow: 0 0 0 0.2rem rgba(26, 106, 131, 0.25);
        }
        
        .info-card {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: none;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="feedback-container">
            <div class="feedback-header">
                <div class="d-flex align-items-center justify-content-center mb-3">
                    <img src="assets/img/logo.jpeg" alt="Bank BJB" style="height: 60px; margin-right: 15px;">
                    <div>
                        <h1>Feedback & Rating</h1>
                        <p>Bank BJB Kantor Cabang Khusus Banten</p>
                    </div>
                </div>
            </div>
            
            <div class="p-4">
                <?php if (isset($_SESSION['success_message'])): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i><?= $_SESSION['success_message'] ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php unset($_SESSION['success_message']); ?>
                <?php endif; ?>

                <?php if (isset($_SESSION['error_message'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i><?= $_SESSION['error_message'] ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php unset($_SESSION['error_message']); ?>
                <?php endif; ?>

                <form method="POST" id="feedbackForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="nama" class="form-label">Nama Lengkap <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="nama" name="nama" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="no_hp" class="form-label">Nomor HP <span class="text-danger">*</span></label>
                            <input type="tel" class="form-control" id="no_hp" name="no_hp" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="loket_id" class="form-label">Loket yang Dilayani</label>
                            <select class="form-select" id="loket_id" name="loket_id">
                                <option value="">Pilih Loket (Opsional)</option>
                                <?php foreach ($loket_list as $loket): ?>
                                    <option value="<?= $loket['id'] ?>"><?= $loket['nama_loket'] ?> - <?= ucfirst($loket['jenis_layanan']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="kategori" class="form-label">Kategori Feedback <span class="text-danger">*</span></label>
                        <select class="form-select" id="kategori" name="kategori" required>
                            <option value="">Pilih Kategori</option>
                            <option value="pelayanan">Pelayanan</option>
                            <option value="fasilitas">Fasilitas</option>
                            <option value="kecepatan">Kecepatan</option>
                            <option value="keramahan">Keramahan</option>
                            <option value="lainnya">Lainnya</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Rating Pelayanan <span class="text-danger">*</span></label>
                        <div class="rating-container">
                            <div class="star-rating">
                                <input type="radio" id="star5" name="rating" value="5" required>
                                <label for="star5" title="Sangat Baik">⭐</label>
                                <input type="radio" id="star4" name="rating" value="4" required>
                                <label for="star4" title="Baik">⭐</label>
                                <input type="radio" id="star3" name="rating" value="3" required>
                                <label for="star3" title="Cukup">⭐</label>
                                <input type="radio" id="star2" name="rating" value="2" required>
                                <label for="star2" title="Kurang">⭐</label>
                                <input type="radio" id="star1" name="rating" value="1" required>
                                <label for="star1" title="Sangat Kurang">⭐</label>
                            </div>
                            <div class="rating-text mt-3">
                                <span id="ratingText" class="text-muted">Pilih rating Anda</span>
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label for="komentar" class="form-label">Komentar & Saran <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="komentar" name="komentar" rows="4" placeholder="Berikan komentar dan saran Anda untuk membantu kami meningkatkan pelayanan..." required></textarea>
                        <div class="form-text">Minimal 10 karakter</div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-between">
                        <a href="index.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Kembali ke Beranda
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-1"></i>Kirim Feedback
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Info Card -->
        <div class="card info-card mt-4" style="max-width: 800px; margin: 20px auto;">
            <div class="card-body">
                <h5 class="card-title"><i class="fas fa-info-circle me-2 text-primary"></i>Informasi Penting</h5>
                <div class="row">
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Feedback bersifat anonim dan rahasia</li>
                            <li><i class="fas fa-check text-success me-2"></i>Respon dalam 1-3 hari kerja</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Data pribadi dijaga kerahasiaannya</li>
                            <li><i class="fas fa-check text-success me-2"></i>Membantu meningkatkan pelayanan</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const ratingInputs = document.querySelectorAll('input[name="rating"]');
            const ratingText = document.getElementById('ratingText');
            
            const ratingTexts = {
                '1': 'Sangat Kurang ⭐',
                '2': 'Kurang ⭐⭐',
                '3': 'Cukup ⭐⭐⭐',
                '4': 'Baik ⭐⭐⭐⭐',
                '5': 'Sangat Baik ⭐⭐⭐⭐⭐'
            };
            
            ratingInputs.forEach(input => {
                input.addEventListener('change', function() {
                    ratingText.textContent = ratingTexts[this.value];
                    ratingText.className = 'text-warning fw-bold';
                });
            });
            
            // Form validation
            document.getElementById('feedbackForm').addEventListener('submit', function(e) {
                const komentar = document.getElementById('komentar').value;
                if (komentar.length < 10) {
                    e.preventDefault();
                    alert('Komentar minimal 10 karakter');
                    return false;
                }
            });
        });
    </script>
</body>
</html>

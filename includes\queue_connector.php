<?php
/**
 * Queue Connector
 * 
 * File ini berfungsi untuk menghubungkan antrian antara nasabah, admin, dan loket
 * Memastikan data antrian konsisten di seluruh sistem
 */

require_once __DIR__ . '/functions.php';
require_once __DIR__ . '/db.php';

/**
 * Fungsi untuk membuat antrian baru dari booking nasabah
 * 
 * @param string $booking_id ID booking
 * @param string $nomor_antrian Nomor antrian
 * @param string $tanggal Tanggal antrian
 * @param int $nasabah_id ID nasabah
 * @param string $jenis_layanan Jenis layanan (teller, cs, kredit)
 * @return bool|int ID antrian baru atau false jika gagal
 */
function create_queue_from_booking($booking_id, $nomor_antrian, $tanggal, $nasabah_id, $jenis_layanan) {
    global $conn;
    
    // Tentukan poli_id berdasarkan jenis layanan
    $poli_id = 0;
    switch ($jenis_layanan) {
        case 'teller':
            $poli_id = 1; // ID poli untuk Teller
            break;
        case 'cs':
            $poli_id = 2; // ID poli untuk Customer Service
            break;
        case 'kredit':
            $poli_id = 3; // ID poli untuk Kredit
            break;
        default:
            return false;
    }
    
    // Cek apakah antrian sudah ada
    $sql = "SELECT id FROM antrian WHERE booking_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $booking_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        // Antrian sudah ada, return ID antrian
        $row = $result->fetch_assoc();
        return $row['id'];
    }
    
    try {
    // Buat antrian baru
    $sql = "INSERT INTO antrian (
        booking_id,
        nomor_antrian,
        tanggal,
        nasabah_id,
        poli_id,
        jenis_layanan,
        status,
        waktu_booking,
        booking_dari
    ) VALUES (?, ?, ?, ?, ?, ?, 'menunggu', NOW(), 'online')";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("sssiss", $booking_id, $nomor_antrian, $tanggal, $nasabah_id, $poli_id, $jenis_layanan);
    
    if ($stmt->execute()) {
        // Update status booking menjadi confirmed
        $sql = "UPDATE bookings SET status = 'confirmed', updated_at = NOW() WHERE booking_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $booking_id);
        $stmt->execute();
        
        return $conn->insert_id;
    }
    } catch (Exception $e) {
        // Log the error
        error_log("Error creating queue from booking: " . $e->getMessage());
}

        return false;
    }
    
/**
 * Fungsi untuk mendapatkan antrian yang menunggu berdasarkan jenis layanan
 * 
 * @param string $jenis_layanan Jenis layanan (teller, cs, kredit)
 * @return array Daftar antrian yang menunggu
 */
function get_waiting_queues($jenis_layanan = null) {
    global $conn;
    
    $sql = "SELECT a.*, n.nama, p.nama_poli
            FROM antrian a
            LEFT JOIN nasabah n ON a.nasabah_id = n.id
            LEFT JOIN poli p ON a.poli_id = p.id
            WHERE a.status = 'menunggu'
            AND a.tanggal = CURDATE()";
    
    // Filter berdasarkan jenis layanan jika ada
    if (!empty($jenis_layanan)) {
        $sql .= " AND a.jenis_layanan = ?";
    }
    $sql .= " ORDER BY a.created_at ASC";
    
    $stmt = $conn->prepare($sql);
    
    if (!empty($jenis_layanan)) {
        $stmt->bind_param("s", $jenis_layanan);
    }
    
    $stmt->execute();
    $result = $stmt->get_result();
    
    $queues = [];
    while ($row = $result->fetch_assoc()) {
        $queues[] = $row;
}

    return $queues;
}

/**
 * Fungsi untuk memanggil antrian berikutnya
 * 
 * @param int $loket_id ID loket
 * @return array|bool Data antrian yang dipanggil atau false jika gagal
 */
function call_next_queue($loket_id) {
    global $conn;
    
    // Dapatkan informasi loket
    $sql = "SELECT * FROM loket WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $loket_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        return false;
    }
    
    $loket = $result->fetch_assoc();
    $jenis_layanan = $loket['jenis_layanan'];
    
    // Cek apakah ada antrian yang sedang dilayani
    $sql = "SELECT * FROM antrian
            WHERE loket_id = ?
            AND status = 'dipanggil'
            AND tanggal = CURDATE()";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $loket_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        return false;
    }
    
    // Dapatkan antrian berikutnya berdasarkan FIFO
    $sql = "SELECT a.*, n.nama, p.nama_poli
            FROM antrian a
            LEFT JOIN nasabah n ON a.nasabah_id = n.id
            LEFT JOIN poli p ON a.poli_id = p.id
            WHERE a.status = 'menunggu'
            AND a.tanggal = CURDATE()
            AND a.jenis_layanan = ?
            ORDER BY a.created_at ASC
            LIMIT 1";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $jenis_layanan);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        return false;
    }
    
    $queue = $result->fetch_assoc();
    $queue_id = $queue['id'];
    
    // Update status antrian
    $sql = "UPDATE antrian
            SET status = 'dipanggil',
                loket_id = ?,
                waktu_dipanggil = NOW()
            WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ii", $loket_id, $queue_id);
    
    if ($stmt->execute()) {
        return $queue;
    }
    
    return false;
}

/**
 * Fungsi untuk menyelesaikan antrian
 * 
 * @param int $queue_id ID antrian
 * @return bool True jika berhasil, false jika gagal
 */
function complete_queue($queue_id) {
    global $conn;
    
    $sql = "UPDATE antrian
            SET status = 'selesai',
                waktu_selesai = NOW()
            WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $queue_id);
    
    return $stmt->execute();
}

/**
 * Fungsi untuk mendapatkan semua antrian hari ini
 * 
 * @return array Daftar semua antrian hari ini
 */
function get_all_queues_today() {
    global $conn;
    
    $sql = "SELECT a.*, n.nama, p.nama_poli, l.nama_loket
            FROM antrian a
            LEFT JOIN nasabah n ON a.nasabah_id = n.id
            LEFT JOIN poli p ON a.poli_id = p.id
            LEFT JOIN loket l ON a.loket_id = l.id
            WHERE a.tanggal = CURDATE()
            ORDER BY a.created_at DESC";
    
    $result = $conn->query($sql);
    
    $queues = [];
    while ($row = $result->fetch_assoc()) {
        $queues[] = $row;
    }
    
    return $queues;
}
?>

<?php
// Staff profile page
$page_title = 'Profil <PERSON>';
$active_menu = 'profil';
require_once '../includes/functions.php';

// Check if user is logged in and is staff
if (!is_logged_in() || !is_staff()) {
    redirect('../login.php');
}

// Get user ID
$user_id = $_SESSION['user_id'];

// Get user data
$sql = "SELECT u.*, l.id as loket_id, l.nama_loket
        FROM users u
        LEFT JOIN loket l ON u.id = l.user_id
        WHERE u.id = $user_id";
$result = query($sql);
$user = fetch_assoc($result);

// Process profile update
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_profile'])) {
    $nama_lengkap = sanitize($_POST['nama_lengkap']);
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];

    // Validate input
    if (empty($nama_lengkap)) {
        $_SESSION['error_message'] = 'Nama lengkap harus diisi.';
    } elseif (!empty($new_password) && $new_password != $confirm_password) {
        $_SESSION['error_message'] = 'Password baru dan konfirmasi password tidak cocok.';
    } elseif (!empty($new_password) && empty($current_password)) {
        $_SESSION['error_message'] = 'Password saat ini harus diisi untuk mengubah password.';
    } else {
        // Check current password if changing password
        if (!empty($new_password)) {
            // Verify current password
            if (!password_verify($current_password, $user['password'])) {
                $_SESSION['error_message'] = 'Password saat ini tidak valid.';
                redirect('profil.php');
            }

            // Hash new password
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);

            // Update user with new password
            $sql = "UPDATE users SET nama_lengkap = '$nama_lengkap', password = '$hashed_password' WHERE id = $user_id";
        } else {
            // Update user without changing password
            $sql = "UPDATE users SET nama_lengkap = '$nama_lengkap' WHERE id = $user_id";
        }

        if (query($sql)) {
            // Update session
            $_SESSION['nama_lengkap'] = $nama_lengkap;

            $_SESSION['success_message'] = 'Profil berhasil diperbarui.';
            redirect('profil.php');
        } else {
            $_SESSION['error_message'] = 'Gagal memperbarui profil.';
        }
    }
}

// Get service statistics
$sql = "SELECT p.nama_poli, COUNT(a.id) as total
        FROM antrian a
        JOIN poli p ON a.poli_id = p.id
        JOIN loket l ON a.loket_id = l.id
        WHERE l.user_id = $user_id
        GROUP BY a.poli_id
        ORDER BY total DESC";
$result = query($sql);
$service_stats = fetch_all($result);

// Get today's statistics
$today = date('Y-m-d');
$sql = "SELECT COUNT(*) as total_today
        FROM antrian a
        JOIN loket l ON a.loket_id = l.id
        WHERE l.user_id = $user_id AND a.tanggal = '$today'";
$result = query($sql);
$today_stats = fetch_assoc($result);

// Include header
include_once '../includes/header.php';
?>

<div class="row">
    <div class="col-md-12">
        <h1 class="mb-4">Profil Petugas</h1>

        <?php include_once '../includes/alerts.php'; ?>

        <div class="row">
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-user-circle"></i> Informasi Petugas
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-4">
                            <i class="fas fa-user-circle fa-5x text-primary"></i>
                        </div>
                        <h5 class="text-center mb-3"><?php echo $user['nama_lengkap']; ?></h5>
                        <p><strong>Username:</strong> <?php echo $user['username']; ?></p>
                        <p><strong>Role:</strong> <?php echo ucfirst($user['role']); ?></p>
                        <p>
                            <strong>Loket:</strong>
                            <?php if($user['loket_id']): ?>
                            <span class="badge badge-success"><?php echo $user['nama_loket']; ?></span>
                            <?php else: ?>
                            <span class="badge badge-secondary">Tidak ada</span>
                            <?php endif; ?>
                        </p>
                        <p><strong>Bergabung Sejak:</strong> <?php echo format_date($user['created_at']); ?></p>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <i class="fas fa-chart-pie"></i> Statistik Layanan
                    </div>
                    <div class="card-body">
                        <p><strong>Total Antrian Hari Ini:</strong> <?php echo $today_stats['total_today'] ?: 0; ?></p>

                        <?php if(count($service_stats) > 0): ?>
                        <h6 class="mt-4">Layanan yang Ditangani:</h6>
                        <ul class="list-group">
                            <?php foreach($service_stats as $stat): ?>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <?php echo $stat['nama_poli']; ?>
                                <span class="badge badge-primary badge-pill"><?php echo $stat['total']; ?></span>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                        <?php else: ?>
                        <p class="text-muted">Belum ada data layanan.</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-dark text-white">
                        <i class="fas fa-edit"></i> Edit Profil
                    </div>
                    <div class="card-body">
                        <form method="post" action="">
                            <div class="form-group">
                                <label for="username">Username</label>
                                <input type="text" class="form-control" id="username" value="<?php echo $user['username']; ?>" readonly>
                                <small class="form-text text-muted">Username tidak dapat diubah.</small>
                            </div>
                            <div class="form-group">
                                <label for="nama_lengkap">Nama Lengkap</label>
                                <input type="text" class="form-control" id="nama_lengkap" name="nama_lengkap" value="<?php echo $user['nama_lengkap']; ?>" required>
                            </div>

                            <h5 class="mt-4 mb-3">Ubah Password</h5>
                            <div class="form-group">
                                <label for="current_password">Password Saat Ini</label>
                                <input type="password" class="form-control" id="current_password" name="current_password">
                                <small class="form-text text-muted">Isi hanya jika ingin mengubah password.</small>
                            </div>
                            <div class="form-group">
                                <label for="new_password">Password Baru</label>
                                <input type="password" class="form-control" id="new_password" name="new_password">
                            </div>
                            <div class="form-group">
                                <label for="confirm_password">Konfirmasi Password Baru</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                            </div>

                            <button type="submit" name="update_profile" class="btn btn-primary">Simpan Perubahan</button>
                            <a href="../logout.php" class="btn btn-danger ml-2">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>

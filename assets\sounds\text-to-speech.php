<?php
/**
 * Text-to-Speech Generator for Queue Voice System
 *
 * This script generates audio files for the queue voice system using Google Text-to-Speech API.
 * It creates all the necessary audio files for the Indonesian queue announcement system.
 */

// Set headers to prevent caching
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Create sounds directory if it doesn't exist
$sounds_dir = __DIR__;
if (!file_exists($sounds_dir)) {
    mkdir($sounds_dir, 0777, true);
}

// Function to generate audio file using Google Text-to-Speech API
function generateAudio($text, $filename) {
    global $sounds_dir;

    // Full path to the output file
    $output_file = $sounds_dir . '/' . $filename;

    // Check if file already exists
    if (file_exists($output_file)) {
        echo "File $filename already exists.<br>";
        return;
    }

    // Use Google Translate TTS API (this is not an official API, but works for simple cases)
    $url = 'https://translate.google.com/translate_tts?ie=UTF-8&client=tw-ob&tl=id&q=' . urlencode($text);

    // Get audio content
    $audio_content = @file_get_contents($url);

    if ($audio_content === false) {
        echo "Failed to generate audio for: $text<br>";
        return;
    }

    // Save to file
    file_put_contents($output_file, $audio_content);
    echo "Generated audio file: $filename<br>";
}

// Generate all required audio files
$audio_files = [
    // Bell sound (we'll use a different approach for this)

    // Basic announcements
    ['Nomor antrian', 'nomor-antrian.mp3'],
    ['Silahkan ke', 'silahkan-ke.mp3'],
    ['Loket', 'loket.mp3'],
    ['Teller', 'teller.mp3'],
    ['Customer Service', 'customer-service.mp3'],
    ['Kredit', 'kredit.mp3'],

    // Numbers
    ['Nol', '0.mp3'],
    ['Satu', '1.mp3'],
    ['Dua', '2.mp3'],
    ['Tiga', '3.mp3'],
    ['Empat', '4.mp3'],
    ['Lima', '5.mp3'],
    ['Enam', '6.mp3'],
    ['Tujuh', '7.mp3'],
    ['Delapan', '8.mp3'],
    ['Sembilan', '9.mp3'],

    // Number components
    ['Sepuluh', 'sepuluh.mp3'],
    ['Sebelas', 'sebelas.mp3'],
    ['Belas', 'belas.mp3'],
    ['Puluh', 'puluh.mp3'],
    ['Seratus', 'seratus.mp3'],
    ['Ratus', 'ratus.mp3'],
    ['Seribu', 'seribu.mp3'],
    ['Ribu', 'ribu.mp3']
];

// Generate bank bell sound (we'll use a typical bank bell/ding sound)
$bell_url = 'https://soundbible.com/grab.php?id=1599&type=mp3'; // Electronic Chime sound (more like a bank bell)
$bell_content = @file_get_contents($bell_url);
if ($bell_content !== false) {
    file_put_contents($sounds_dir . '/bell.mp3', $bell_content);
    echo "Generated bank bell sound: bell.mp3<br>";
} else {
    // Try alternative bell sound
    $alt_bell_url = 'https://soundbible.com/grab.php?id=1628&type=mp3'; // Service Bell sound
    $alt_bell_content = @file_get_contents($alt_bell_url);
    if ($alt_bell_content !== false) {
        file_put_contents($sounds_dir . '/bell.mp3', $alt_bell_content);
        echo "Generated alternative bank bell sound: bell.mp3<br>";
    } else {
        echo "Failed to generate bell sound. Please download a bank bell sound manually.<br>";
    }
}

// Generate all audio files
foreach ($audio_files as $audio) {
    generateAudio($audio[0], $audio[1]);
}

echo "<br>Audio generation complete. Please check the sounds directory for the generated files.";
?>

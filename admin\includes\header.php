<?php
// Check if user is logged in
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] != 'admin') {
    $_SESSION['error_message'] = 'Anda harus login terlebih dahulu untuk mengakses halaman ini.';
    redirect('../login_admin.php');
}

// Initialize variables
if (!isset($page_title)) {
    $page_title = 'Admin Dashboard';
}

if (!isset($active_menu)) {
    $active_menu = 'dashboard';
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Bank BJB</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/menu-bjb.css">
    <style>
        .dashboard-card {
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .dashboard-card .icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .dashboard-card .count {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .dashboard-card .title {
            font-size: 16px;
            opacity: 0.8;
        }

        .latest-queues {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .latest-queues h5 {
            margin-bottom: 20px;
            color: #1a6a83;
            font-weight: 600;
        }

        .latest-queues .table th {
            border-top: none;
            color: #6c757d;
            font-weight: 600;
        }

        .badge-menunggu {
            background-color: #ffc107;
            color: #212529;
        }

        .badge-dipanggil {
            background-color: #17a2b8;
            color: #fff;
        }

        .badge-selesai {
            background-color: #28a745;
            color: #fff;
        }

        .badge-batal {
            background-color: #dc3545;
            color: #fff;
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <!-- Sidebar -->
        <nav id="sidebar">
            <div class="sidebar-header">
                <div class="logo-container">
                    <img src="../assets/img/logo.jpeg" alt="Bank BJB" class="logo-bjb">
                </div>
                <h4>Bank BJB Kantor<br>Cabang Khusus<br>Banten</h4>
                <span class="badge">Administrator</span>
            </div>

            <ul class="list-unstyled components">
                <li class="<?php echo ($active_menu == 'dashboard') ? 'active' : ''; ?>">
                    <a href="index.php">
                        <div class="icon-circle <?php echo ($active_menu == 'dashboard') ? 'bg-primary' : 'bg-secondary'; ?>">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <span>Dashboard</span>
                    </a>
                </li>
                <li class="<?php echo ($active_menu == 'loket') ? 'active' : ''; ?>">
                    <a href="loket.php">
                        <div class="icon-circle <?php echo ($active_menu == 'loket') ? 'bg-warning' : 'bg-secondary'; ?>">
                            <i class="fas fa-door-open"></i>
                        </div>
                        <span>Manajemen Loket</span>
                    </a>
                </li>
                <li class="<?php echo ($active_menu == 'petugas') ? 'active' : ''; ?>">
                    <a href="petugas.php">
                        <div class="icon-circle <?php echo ($active_menu == 'petugas') ? 'bg-info' : 'bg-secondary'; ?>">
                            <i class="fas fa-user-tie"></i>
                        </div>
                        <span>Manajemen Petugas</span>
                    </a>
                </li>
                <li class="<?php echo ($active_menu == 'nasabah') ? 'active' : ''; ?>">
                    <a href="nasabah.php">
                        <div class="icon-circle <?php echo ($active_menu == 'nasabah') ? 'bg-success' : 'bg-secondary'; ?>">
                            <i class="fas fa-users"></i>
                        </div>
                        <span>Manajemen Nasabah</span>
                    </a>
                </li>
                <li class="<?php echo ($active_menu == 'laporan') ? 'active' : ''; ?>">
                    <a href="laporan.php">
                        <div class="icon-circle <?php echo ($active_menu == 'laporan') ? 'bg-primary' : 'bg-secondary'; ?>">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <span>Laporan</span>
                    </a>
                </li>
                <li class="<?php echo ($active_menu == 'feedback') ? 'active' : ''; ?>">
                    <a href="feedback.php">
                        <div class="icon-circle <?php echo ($active_menu == 'feedback') ? 'bg-warning' : 'bg-secondary'; ?>">
                            <i class="fas fa-star"></i>
                        </div>
                        <span>Feedback & Rating</span>
                    </a>
                </li>
                <li class="<?php echo ($active_menu == 'pengaturan') ? 'active' : ''; ?>">
                    <a href="pengaturan.php">
                        <div class="icon-circle <?php echo ($active_menu == 'pengaturan') ? 'bg-secondary' : 'bg-secondary'; ?>">
                            <i class="fas fa-cog"></i>
                        </div>
                        <span>Pengaturan</span>
                    </a>
                </li>
                <li>
                    <a href="../logout.php">
                        <div class="icon-circle bg-danger">
                            <i class="fas fa-sign-out-alt"></i>
                        </div>
                        <span>Logout</span>
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Page Content -->
        <div id="content">
            <!-- Navbar -->
            <nav class="navbar navbar-expand-lg navbar-light bg-light">
                <div class="container-fluid">
                    <button type="button" id="sidebarCollapse" class="btn btn-primary">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="ml-auto d-flex align-items-center">
                        <div class="mr-3">
                            <i class="fas fa-calendar-alt mr-1"></i> <?php echo date('d M Y'); ?>
                        </div>
                        <div>
                            <i class="fas fa-clock mr-1"></i> <span id="navbar-time"><?php echo date('H:i:s'); ?></span>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Success and Error Messages -->
            <?php if (isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle mr-2"></i> <?php echo $_SESSION['success_message']; ?>
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <?php unset($_SESSION['success_message']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['error_message'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle mr-2"></i> <?php echo $_SESSION['error_message']; ?>
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <?php unset($_SESSION['error_message']); ?>
            <?php endif; ?>

            <!-- Content -->
            <div class="container-fluid">

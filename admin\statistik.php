<?php
// Statistics and Analytics page
$page_title = 'Statistik & Analitik';
$active_menu = 'statistik';
$is_admin = true;
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!is_logged_in() || !is_admin()) {
    redirect('../login.php');
}

// Get date range
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-7 days'));
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');

// Get service statistics
$sql = "SELECT p.id, p.nama_poli, 
        COUNT(a.id) as total_antrian,
        SUM(CASE WHEN a.status = 'selesai' THEN 1 ELSE 0 END) as selesai,
        SUM(CASE WHEN a.booking_dari = 'online' THEN 1 ELSE 0 END) as online,
        SUM(CASE WHEN a.booking_dari = 'loket' THEN 1 ELSE 0 END) as loket
        FROM poli p
        LEFT JOIN antrian a ON p.id = a.poli_id AND a.tanggal BETWEEN '$start_date' AND '$end_date'
        GROUP BY p.id
        ORDER BY total_antrian DESC";
$result = query($sql);
$service_stats = fetch_all($result);

// Get daily statistics
$sql = "SELECT a.tanggal, 
        COUNT(a.id) as total_antrian,
        SUM(CASE WHEN a.status = 'selesai' THEN 1 ELSE 0 END) as selesai,
        SUM(CASE WHEN a.booking_dari = 'online' THEN 1 ELSE 0 END) as online,
        SUM(CASE WHEN a.booking_dari = 'loket' THEN 1 ELSE 0 END) as loket
        FROM antrian a
        WHERE a.tanggal BETWEEN '$start_date' AND '$end_date'
        GROUP BY a.tanggal
        ORDER BY a.tanggal";
$result = query($sql);
$daily_stats = fetch_all($result);

// Get hourly statistics
$sql = "SELECT HOUR(a.created_at) as jam, 
        COUNT(a.id) as total_antrian
        FROM antrian a
        WHERE a.tanggal BETWEEN '$start_date' AND '$end_date'
        GROUP BY HOUR(a.created_at)
        ORDER BY jam";
$result = query($sql);
$hourly_stats = fetch_all($result);

// Get counter performance
$sql = "SELECT l.nama_loket, u.nama_lengkap,
        COUNT(a.id) as total_antrian,
        SUM(CASE WHEN a.status = 'selesai' THEN 1 ELSE 0 END) as selesai,
        AVG(TIMESTAMPDIFF(MINUTE, a.waktu_dipanggil, a.waktu_selesai)) as avg_time
        FROM loket l
        LEFT JOIN users u ON l.user_id = u.id
        LEFT JOIN antrian a ON l.id = a.loket_id AND a.tanggal BETWEEN '$start_date' AND '$end_date'
        GROUP BY l.id
        ORDER BY selesai DESC";
$result = query($sql);
$counter_stats = fetch_all($result);

// Include header
include_once '../includes/header.php';
?>

<div class="row">
    <div class="col-md-12">
        <h1 class="mb-4">Statistik & Analitik</h1>
        
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-calendar"></i> Pilih Rentang Tanggal
            </div>
            <div class="card-body">
                <form method="get" action="statistik.php" class="form-inline">
                    <div class="form-group mr-3">
                        <label for="start_date" class="mr-2">Tanggal Mulai:</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $start_date; ?>">
                    </div>
                    <div class="form-group mr-3">
                        <label for="end_date" class="mr-2">Tanggal Akhir:</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $end_date; ?>">
                    </div>
                    <button type="submit" class="btn btn-primary">Tampilkan</button>
                </form>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-chart-pie"></i> Statistik Layanan
                    </div>
                    <div class="card-body">
                        <canvas id="serviceChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-chart-line"></i> Statistik Harian
                    </div>
                    <div class="card-body">
                        <canvas id="dailyChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-chart-bar"></i> Statistik Per Jam
                    </div>
                    <div class="card-body">
                        <canvas id="hourlyChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-chart-bar"></i> Performa Loket
                    </div>
                    <div class="card-body">
                        <canvas id="counterChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-12">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-table"></i> Tabel Statistik Layanan
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Layanan</th>
                                        <th>Total Antrian</th>
                                        <th>Selesai</th>
                                        <th>Booking Online</th>
                                        <th>Ambil di Loket</th>
                                        <th>Persentase Selesai</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach($service_stats as $stat): ?>
                                    <tr>
                                        <td><?php echo $stat['nama_poli']; ?></td>
                                        <td><?php echo $stat['total_antrian']; ?></td>
                                        <td><?php echo $stat['selesai']; ?></td>
                                        <td><?php echo $stat['online']; ?></td>
                                        <td><?php echo $stat['loket']; ?></td>
                                        <td>
                                            <?php
                                            $percentage = $stat['total_antrian'] > 0 ? round(($stat['selesai'] / $stat['total_antrian']) * 100) : 0;
                                            ?>
                                            <div class="progress">
                                                <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $percentage; ?>%" aria-valuenow="<?php echo $percentage; ?>" aria-valuemin="0" aria-valuemax="100"><?php echo $percentage; ?>%</div>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-12">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-table"></i> Tabel Performa Loket
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Loket</th>
                                        <th>Petugas</th>
                                        <th>Total Antrian</th>
                                        <th>Selesai</th>
                                        <th>Rata-rata Waktu Layanan</th>
                                        <th>Persentase Selesai</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach($counter_stats as $stat): ?>
                                    <tr>
                                        <td><?php echo $stat['nama_loket']; ?></td>
                                        <td><?php echo $stat['nama_lengkap'] ?: 'Belum ada petugas'; ?></td>
                                        <td><?php echo $stat['total_antrian']; ?></td>
                                        <td><?php echo $stat['selesai']; ?></td>
                                        <td>
                                            <?php
                                            $avg_time = $stat['avg_time'] ? round($stat['avg_time']) : 0;
                                            echo $avg_time . ' menit';
                                            ?>
                                        </td>
                                        <td>
                                            <?php
                                            $percentage = $stat['total_antrian'] > 0 ? round(($stat['selesai'] / $stat['total_antrian']) * 100) : 0;
                                            ?>
                                            <div class="progress">
                                                <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $percentage; ?>%" aria-valuenow="<?php echo $percentage; ?>" aria-valuemin="0" aria-valuemax="100"><?php echo $percentage; ?>%</div>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
$(document).ready(function() {
    // Service Chart
    var serviceCtx = document.getElementById('serviceChart').getContext('2d');
    var serviceChart = new Chart(serviceCtx, {
        type: 'pie',
        data: {
            labels: [
                <?php foreach($service_stats as $stat): ?>
                '<?php echo $stat['nama_poli']; ?>',
                <?php endforeach; ?>
            ],
            datasets: [{
                label: 'Total Antrian',
                data: [
                    <?php foreach($service_stats as $stat): ?>
                    <?php echo $stat['total_antrian']; ?>,
                    <?php endforeach; ?>
                ],
                backgroundColor: [
                    'rgba(255, 99, 132, 0.7)',
                    'rgba(54, 162, 235, 0.7)',
                    'rgba(255, 206, 86, 0.7)',
                    'rgba(75, 192, 192, 0.7)',
                    'rgba(153, 102, 255, 0.7)',
                    'rgba(255, 159, 64, 0.7)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'right',
                },
                title: {
                    display: true,
                    text: 'Distribusi Antrian per Layanan'
                }
            }
        }
    });
    
    // Daily Chart
    var dailyCtx = document.getElementById('dailyChart').getContext('2d');
    var dailyChart = new Chart(dailyCtx, {
        type: 'line',
        data: {
            labels: [
                <?php foreach($daily_stats as $stat): ?>
                '<?php echo tanggal_indonesia_short($stat['tanggal']); ?>',
                <?php endforeach; ?>
            ],
            datasets: [{
                label: 'Total Antrian',
                data: [
                    <?php foreach($daily_stats as $stat): ?>
                    <?php echo $stat['total_antrian']; ?>,
                    <?php endforeach; ?>
                ],
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 2,
                tension: 0.1
            }, {
                label: 'Booking Online',
                data: [
                    <?php foreach($daily_stats as $stat): ?>
                    <?php echo $stat['online']; ?>,
                    <?php endforeach; ?>
                ],
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                borderColor: 'rgba(255, 99, 132, 1)',
                borderWidth: 2,
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Statistik Antrian Harian'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // Hourly Chart
    var hourlyCtx = document.getElementById('hourlyChart').getContext('2d');
    var hourlyChart = new Chart(hourlyCtx, {
        type: 'bar',
        data: {
            labels: [
                <?php foreach($hourly_stats as $stat): ?>
                '<?php echo $stat['jam'] . ':00'; ?>',
                <?php endforeach; ?>
            ],
            datasets: [{
                label: 'Jumlah Antrian',
                data: [
                    <?php foreach($hourly_stats as $stat): ?>
                    <?php echo $stat['total_antrian']; ?>,
                    <?php endforeach; ?>
                ],
                backgroundColor: 'rgba(75, 192, 192, 0.7)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Distribusi Antrian per Jam'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // Counter Chart
    var counterCtx = document.getElementById('counterChart').getContext('2d');
    var counterChart = new Chart(counterCtx, {
        type: 'bar',
        data: {
            labels: [
                <?php foreach($counter_stats as $stat): ?>
                '<?php echo $stat['nama_loket']; ?>',
                <?php endforeach; ?>
            ],
            datasets: [{
                label: 'Antrian Selesai',
                data: [
                    <?php foreach($counter_stats as $stat): ?>
                    <?php echo $stat['selesai']; ?>,
                    <?php endforeach; ?>
                ],
                backgroundColor: 'rgba(153, 102, 255, 0.7)',
                borderColor: 'rgba(153, 102, 255, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Performa Loket'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
});
</script>

<?php
// Include footer
include_once '../includes/footer.php';
?>

<?php
// Login page
session_start();

// Include database configuration
require_once 'config/database.php';
require_once 'includes/functions_mock.php';

// Check if user is already logged in
if (is_logged_in()) {
    // Redirect to staff page
    if (is_staff()) {
        redirect('staff/index.php');
    } elseif (is_admin()) {
        redirect('admin/index.php');
    }
}

// Process login form
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = sanitize($_POST['username']);
    $password = $_POST['password'];

    // Validate input
    if (empty($username) || empty($password)) {
        $_SESSION['error_message'] = 'Username dan password harus diisi.';
    } else {
        // Query the database for the user
        $sql = "SELECT * FROM users WHERE username = '$username'";
        $result = query($sql);

        if (num_rows($result) > 0) {
            $user = fetch_assoc($result);

            // Verify password
            if (password_verify($password, $user['password'])) {
                // Set session variables
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['nama_lengkap'] = $user['nama_lengkap'];
                $_SESSION['role'] = $user['role'];

                // Check if user is assigned to a counter
                $counter_sql = "SELECT * FROM loket WHERE user_id = {$user['id']}";
                $counter_result = query($counter_sql);

                if (num_rows($counter_result) > 0) {
                    $counter = fetch_assoc($counter_result);
                    $_SESSION['counter_id'] = $counter['id'];
                    $_SESSION['counter_name'] = $counter['nama_loket'];
                    $_SESSION['counter_type'] = $counter['jenis_layanan'];
                }

                // Redirect to appropriate page
                if ($user['role'] == 'admin') {
                    redirect('admin/index.php');
                } else {
                    redirect('staff/index.php');
                }
            } else {
                $_SESSION['error_message'] = 'Username atau password salah. Silahkan coba lagi.';
            }
        } else {
            $_SESSION['error_message'] = 'Username atau password salah. Silahkan coba lagi.';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Petugas - Sistem Antrian Bank BJB</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">

    <style>
        .login-page {
            background: linear-gradient(135deg, #1a6a83 0%, #0e4b5e 100%);
        }
    </style>
</head>
<body class="login-page">
    <div class="login-box">
        <div class="login-logo">
            <img src="assets/img/logo.jpeg" alt="Bank BJB" style="max-width: 200px;">
            <h3>APLIKASI ANTRIAN PENGUNJUNG</h3>
            <p>BANK BJB</p>
            <p class="mt-3">Login Petugas</p>
        </div>

        <?php if(isset($_SESSION['error_message'])): ?>
        <div class="alert alert-dismissible fade show" role="alert" style="background-color: rgba(26, 106, 131, 0.1); color: #1a6a83; border-color: rgba(26, 106, 131, 0.2); border-radius: 8px;">
            <?php echo $_SESSION['error_message']; ?>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <?php unset($_SESSION['error_message']); ?>
        <?php endif; ?>

        <form action="login.php" method="post" class="login-form">
            <div class="form-group">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                    </div>
                    <input type="text" class="form-control" name="username" placeholder="Username" required>
                </div>
            </div>
            <div class="form-group">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                    </div>
                    <input type="password" class="form-control" name="password" placeholder="Password" required>
                </div>
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn-block" style="background: linear-gradient(135deg, #1a6a83 0%, #0e4b5e 100%); color: white; border: none; box-shadow: 0 4px 6px rgba(26, 106, 131, 0.2); padding: 10px 20px; border-radius: 4px; font-weight: 600;">
                    <i class="fas fa-sign-in-alt"></i> Login
                </button>
            </div>
        </form>

        <div class="text-center mt-3">
            <a href="index.php" class="btn btn-link" style="color: #1a6a83;">
                <i class="fas fa-arrow-left"></i> Kembali ke Halaman Utama
            </a>
        </div>

        <div class="text-center mt-3">
            <div class="alert" style="background-color: rgba(26, 106, 131, 0.1); color: #1a6a83; border-color: rgba(26, 106, 131, 0.2); border-radius: 8px;">
                <strong>Demo Credentials:</strong><br>
                Petugas: petugas / petugas123
            </div>
        </div>

        <div class="login-footer">
            &copy; <?php echo date('Y'); ?> - Bank BJB
        </div>
    </div>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

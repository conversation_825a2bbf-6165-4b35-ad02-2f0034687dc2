<?php
// Setup database tables for nasabah functionality
require_once '../includes/db.php';

// Create nasabah table if not exists
$sql_nasabah = "CREATE TABLE IF NOT EXISTS nasabah (
    id INT(11) NOT NULL AUTO_INCREMENT,
    nama VARCHAR(100) NOT NULL,
    no_identitas VARCHAR(20) NOT NULL,
    jenis_identitas ENUM('ktp', 'sim', 'passport') NOT NULL DEFAULT 'ktp',
    email VARCHAR(100) NOT NULL,
    no_hp VARCHAR(15) NOT NULL,
    password VARCHAR(255) NOT NULL,
    alamat TEXT NOT NULL,
    status_verifikasi ENUM('belum', 'sudah') NOT NULL DEFAULT 'belum',
    is_blocked TINYINT(1) DEFAULT 0,
    created_at DATETIME NOT NULL,
    updated_at DATETIME DEFAULT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY (email),
    UNIQUE KEY (no_identitas)
)";

if ($conn->query($sql_nasabah) === TRUE) {
    echo "Table 'nasabah' created successfully or already exists<br>";
} else {
    echo "Error creating table 'nasabah': " . $conn->error . "<br>";
}

// Create bookings table if not exists
$sql_bookings = "CREATE TABLE IF NOT EXISTS bookings (
    id INT(11) NOT NULL AUTO_INCREMENT,
    booking_id VARCHAR(20) NOT NULL,
    nasabah_id INT(11) NOT NULL,
    nama VARCHAR(100) NOT NULL,
    no_identitas VARCHAR(20) NOT NULL,
    email VARCHAR(100) NOT NULL,
    no_hp VARCHAR(15) NOT NULL,
    layanan VARCHAR(20) NOT NULL,
    tanggal DATE NOT NULL,
    waktu VARCHAR(20) NOT NULL,
    keterangan TEXT,
    file_identitas VARCHAR(255) NOT NULL,
    nomor_antrian VARCHAR(10) NOT NULL,
    status ENUM('pending', 'confirmed', 'completed', 'cancelled') NOT NULL DEFAULT 'pending',
    created_at DATETIME NOT NULL,
    updated_at DATETIME DEFAULT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY (booking_id),
    FOREIGN KEY (nasabah_id) REFERENCES nasabah(id) ON DELETE CASCADE
)";

if ($conn->query($sql_bookings) === TRUE) {
    echo "Table 'bookings' created successfully or already exists<br>";
} else {
    echo "Error creating table 'bookings': " . $conn->error . "<br>";
}

// Create uploads directory if not exists
$upload_dir = '../uploads/';
if (!file_exists($upload_dir)) {
    if (mkdir($upload_dir, 0777, true)) {
        echo "Directory 'uploads' created successfully<br>";
    } else {
        echo "Error creating directory 'uploads'<br>";
    }
} else {
    echo "Directory 'uploads' already exists<br>";
}

echo "<br>Database setup completed!";

<?php
// Test booking system integration
require_once 'includes/functions.php';

echo "<h1>Test Booking System Integration</h1>";

// Test 1: Create sample bookings for each service type
echo "<h2>1. Creating Sample Bookings</h2>";

$services = ['teller', 'cs', 'kredit'];
$customers = [
    ['nama' => '<PERSON> Fauzi', 'id' => 1],
    ['nama' => 'Siti Nurhaliza', 'id' => 2], 
    ['nama' => 'Budi Santoso', 'id' => 3],
    ['nama' => 'Dewi Lestari', 'id' => 4],
    ['nama' => '<PERSON><PERSON> Hartono', 'id' => 5]
];

foreach ($services as $index => $service) {
    $customer = $customers[$index];
    
    // Generate queue number
    $prefix = '';
    switch($service) {
        case 'teller': $prefix = 'T'; break;
        case 'cs': $prefix = 'C'; break;
        case 'kredit': $prefix = 'K'; break;
    }
    
    // Get next number for today
    $sql = "SELECT COUNT(*) as count FROM antrian WHERE jenis_layanan = '$service' AND tanggal = CURDATE()";
    $result = query($sql);
    $count = fetch_assoc($result);
    $next_number = $count['count'] + 1;
    $nomor_antrian = $prefix . str_pad($next_number, 3, '0', STR_PAD_LEFT);
    
    // Insert queue
    $sql = "INSERT INTO antrian (nomor_antrian, nama, jenis_layanan, tanggal, status, nasabah_id, waktu_booking, created_at) 
            VALUES ('$nomor_antrian', '{$customer['nama']}', '$service', CURDATE(), 'menunggu', {$customer['id']}, NOW(), NOW())";
    
    if (query($sql)) {
        echo "<p>✅ Created queue: $nomor_antrian - {$customer['nama']} ($service)</p>";
    } else {
        echo "<p>❌ Failed to create queue for {$customer['nama']} ($service)</p>";
    }
}

// Test 2: Check queues by service type
echo "<h2>2. Queues by Service Type</h2>";

foreach ($services as $service) {
    echo "<h3>" . ucfirst($service) . " Queues:</h3>";
    
    $sql = "SELECT * FROM antrian WHERE jenis_layanan = '$service' AND tanggal = CURDATE() AND status = 'menunggu' ORDER BY id";
    $result = query($sql);
    
    if ($result && num_rows($result) > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Nomor Antrian</th><th>Nama</th><th>Status</th><th>Waktu Booking</th></tr>";
        while ($row = fetch_assoc($result)) {
            echo "<tr>";
            echo "<td>" . $row['nomor_antrian'] . "</td>";
            echo "<td>" . $row['nama'] . "</td>";
            echo "<td>" . $row['status'] . "</td>";
            echo "<td>" . date('H:i', strtotime($row['waktu_booking'])) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No queues found for $service</p>";
    }
}

// Test 3: Check counter assignments
echo "<h2>3. Counter Assignments</h2>";

$sql = "SELECT * FROM loket WHERE status = 'aktif' ORDER BY id";
$result = query($sql);

if ($result && num_rows($result) > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Loket</th><th>Jenis Layanan</th><th>Status</th><th>User ID</th></tr>";
    while ($row = fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['nama_loket'] . "</td>";
        echo "<td>" . $row['jenis_layanan'] . "</td>";
        echo "<td>" . $row['status'] . "</td>";
        echo "<td>" . ($row['user_id'] ? $row['user_id'] : 'Not assigned') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No counters found</p>";
}

// Test 4: Test queue calling simulation
echo "<h2>4. Queue Calling Simulation</h2>";

// Simulate calling next queue for teller
$sql = "SELECT * FROM loket WHERE jenis_layanan = 'teller' AND status = 'aktif' LIMIT 1";
$result = query($sql);

if ($result && num_rows($result) > 0) {
    $counter = fetch_assoc($result);
    $counter_id = $counter['id'];
    
    echo "<h3>Testing Teller Counter (Loket {$counter['nama_loket']})</h3>";
    
    // Get next teller queue
    $sql = "SELECT * FROM antrian WHERE jenis_layanan = 'teller' AND status = 'menunggu' AND tanggal = CURDATE() ORDER BY id LIMIT 1";
    $result = query($sql);
    
    if ($result && num_rows($result) > 0) {
        $queue = fetch_assoc($result);
        
        // Simulate calling the queue
        $sql = "UPDATE antrian SET status = 'dipanggil', loket_id = $counter_id, waktu_dipanggil = NOW() WHERE id = {$queue['id']}";
        
        if (query($sql)) {
            echo "<p>✅ Called queue: {$queue['nomor_antrian']} - {$queue['nama']} to {$counter['nama_loket']}</p>";
        } else {
            echo "<p>❌ Failed to call queue</p>";
        }
    } else {
        echo "<p>No teller queues available to call</p>";
    }
}

// Test 5: Show current status
echo "<h2>5. Current System Status</h2>";

echo "<h3>All Queues Today:</h3>";
$sql = "SELECT a.*, l.nama_loket FROM antrian a LEFT JOIN loket l ON a.loket_id = l.id WHERE a.tanggal = CURDATE() ORDER BY a.id";
$result = query($sql);

if ($result && num_rows($result) > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Nomor Antrian</th><th>Nama</th><th>Layanan</th><th>Status</th><th>Loket</th><th>Waktu</th></tr>";
    while ($row = fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['nomor_antrian'] . "</td>";
        echo "<td>" . $row['nama'] . "</td>";
        echo "<td>" . $row['jenis_layanan'] . "</td>";
        echo "<td>" . $row['status'] . "</td>";
        echo "<td>" . ($row['nama_loket'] ? $row['nama_loket'] : '-') . "</td>";
        echo "<td>" . date('H:i', strtotime($row['waktu_booking'])) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No queues found for today</p>";
}

echo "<br><br>";
echo "<a href='nasabah/simple_booking.php' class='btn btn-primary'>Test Customer Booking</a> ";
echo "<a href='staff/index.php' class='btn btn-success'>Test Staff Interface</a> ";
echo "<a href='admin/index.php' class='btn btn-info'>Test Admin Dashboard</a>";
?>

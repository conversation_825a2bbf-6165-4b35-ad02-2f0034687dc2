<?php
// Admin dashboard page (Mock Version)
$page_title = 'Dashboard Admin';
$active_menu = 'dashboard';
$is_admin = true;
require_once '../includes/functions_mock.php';

// Check if user is logged in
if (!is_logged_in()) {
    redirect('../login_admin.php');
}

// Check if user is admin
if (!is_admin()) {
    redirect('../index.php');
}

// Mock data for dashboard
$total_antrian = 25;
$antrian_menunggu = 8;
$antrian_selesai = 15;
$antrian_batal = 2;
$verifikasi_pending = 5;

// Mock data for latest queues
$latest_queues = [
    [
        'id' => 1,
        'nomor_antrian' => 'T001',
        'nama' => 'Ahmad <PERSON>zi',
        'nama_poli' => 'Teller',
        'nama_loket' => 'Loket 1',
        'status' => 'selesai',
        'waktu_booking' => date('Y-m-d H:i:s', strtotime('-2 hours')),
        'waktu_dipanggil' => date('Y-m-d H:i:s', strtotime('-1 hour 45 minutes')),
        'waktu_selesai' => date('Y-m-d H:i:s', strtotime('-1 hour 30 minutes'))
    ],
    [
        'id' => 2,
        'nomor_antrian' => 'C001',
        'nama' => 'Siti Nurhaliza',
        'nama_poli' => 'Customer Service',
        'nama_loket' => 'Loket 4',
        'status' => 'selesai',
        'waktu_booking' => date('Y-m-d H:i:s', strtotime('-1 hour 30 minutes')),
        'waktu_dipanggil' => date('Y-m-d H:i:s', strtotime('-1 hour 15 minutes')),
        'waktu_selesai' => date('Y-m-d H:i:s', strtotime('-1 hour'))
    ],
    [
        'id' => 3,
        'nomor_antrian' => 'K001',
        'nama' => 'Budi Santoso',
        'nama_poli' => 'Kredit',
        'nama_loket' => 'Loket 6',
        'status' => 'selesai',
        'waktu_booking' => date('Y-m-d H:i:s', strtotime('-1 hour')),
        'waktu_dipanggil' => date('Y-m-d H:i:s', strtotime('-45 minutes')),
        'waktu_selesai' => date('Y-m-d H:i:s', strtotime('-30 minutes'))
    ],
    [
        'id' => 4,
        'nomor_antrian' => 'T002',
        'nama' => 'Dewi Lestari',
        'nama_poli' => 'Teller',
        'nama_loket' => 'Loket 1',
        'status' => 'dipanggil',
        'waktu_booking' => date('Y-m-d H:i:s', strtotime('-30 minutes')),
        'waktu_dipanggil' => date('Y-m-d H:i:s', strtotime('-15 minutes')),
        'waktu_selesai' => null
    ],
    [
        'id' => 5,
        'nomor_antrian' => 'C002',
        'nama' => 'Rudi Hartono',
        'nama_poli' => 'Customer Service',
        'nama_loket' => null,
        'status' => 'menunggu',
        'waktu_booking' => date('Y-m-d H:i:s', strtotime('-15 minutes')),
        'waktu_dipanggil' => null,
        'waktu_selesai' => null
    ]
];
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Bank BJB</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .dashboard-card {
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .dashboard-card .icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .dashboard-card .count {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .dashboard-card .title {
            font-size: 16px;
            opacity: 0.8;
        }

        .latest-queues {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .latest-queues h5 {
            margin-bottom: 20px;
            color: #1a6a83;
            font-weight: 600;
        }

        .latest-queues .table th {
            border-top: none;
            color: #6c757d;
            font-weight: 600;
        }

        .badge-menunggu {
            background-color: #ffc107;
            color: #212529;
        }

        .badge-dipanggil {
            background-color: #17a2b8;
            color: #fff;
        }

        .badge-selesai {
            background-color: #28a745;
            color: #fff;
        }

        .badge-batal {
            background-color: #dc3545;
            color: #fff;
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <!-- Sidebar -->
        <nav id="sidebar">
            <div class="sidebar-header">
                <div class="logo-container">
                    <img src="../assets/img/logo.jpeg" alt="Bank BJB" class="logo-bjb">
                </div>
                <h4>Bank BJB Kantor<br>Cabang Khusus<br>Banten</h4>
                <p><?php echo $_SESSION['nama_lengkap']; ?></p>
            </div>

            <ul class="list-unstyled components">
                <li class="<?php echo ($active_menu == 'dashboard') ? 'active' : ''; ?>">
                    <a href="index_mock.php">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                </li>
                <li class="<?php echo ($active_menu == 'loket') ? 'active' : ''; ?>">
                    <a href="loket.php">
                        <i class="fas fa-door-open"></i> Manajemen Loket
                    </a>
                </li>
                <li class="<?php echo ($active_menu == 'petugas') ? 'active' : ''; ?>">
                    <a href="petugas.php">
                        <i class="fas fa-user-tie"></i> Manajemen Petugas
                    </a>
                </li>
                <li class="<?php echo ($active_menu == 'nasabah') ? 'active' : ''; ?>">
                    <a href="nasabah.php">
                        <i class="fas fa-users"></i> Manajemen Nasabah
                    </a>
                </li>
                <li class="<?php echo ($active_menu == 'pengaturan') ? 'active' : ''; ?>">
                    <a href="pengaturan.php">
                        <i class="fas fa-cog"></i> Pengaturan
                    </a>
                </li>
                <li>
                    <a href="../logout.php">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Page Content -->
        <div id="content">
            <!-- Navbar -->
            <nav class="navbar navbar-expand-lg navbar-light bg-light">
                <div class="container-fluid">
                    <button type="button" id="sidebarCollapse" class="btn btn-info">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="ml-auto">
                        <span id="navbar-time"><?php echo date('H:i:s'); ?></span>
                    </div>
                </div>
            </nav>

            <!-- Content -->
            <div class="container-fluid">
                <div class="row">
                    <div class="col-md-12">
                        <h2 class="mb-4">Dashboard Admin</h2>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3">
                        <div class="card dashboard-card bg-primary text-white">
                            <div class="card-body text-center">
                                <div class="icon">
                                    <i class="fas fa-ticket-alt"></i>
                                </div>
                                <div class="count"><?php echo $total_antrian; ?></div>
                                <div class="title">Total Antrian Hari Ini</div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card dashboard-card bg-warning text-dark">
                            <div class="card-body text-center">
                                <div class="icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="count"><?php echo $antrian_menunggu; ?></div>
                                <div class="title">Antrian Menunggu</div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card dashboard-card bg-success text-white">
                            <div class="card-body text-center">
                                <div class="icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="count"><?php echo $antrian_selesai; ?></div>
                                <div class="title">Antrian Selesai</div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card dashboard-card bg-danger text-white">
                            <div class="card-body text-center">
                                <div class="icon">
                                    <i class="fas fa-user-check"></i>
                                </div>
                                <div class="count"><?php echo $verifikasi_pending; ?></div>
                                <div class="title">Verifikasi Pending</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="latest-queues">
                            <h5><i class="fas fa-list-alt mr-2"></i> Antrian Terbaru</h5>

                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>No. Antrian</th>
                                            <th>Nama</th>
                                            <th>Layanan</th>
                                            <th>Loket</th>
                                            <th>Status</th>
                                            <th>Waktu Booking</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($latest_queues as $queue): ?>
                                        <tr>
                                            <td><?php echo $queue['nomor_antrian']; ?></td>
                                            <td><?php echo $queue['nama']; ?></td>
                                            <td><?php echo $queue['nama_poli']; ?></td>
                                            <td><?php echo $queue['nama_loket'] ?? '-'; ?></td>
                                            <td>
                                                <?php
                                                $status_class = '';
                                                switch ($queue['status']) {
                                                    case 'menunggu':
                                                        $status_class = 'badge-menunggu';
                                                        break;
                                                    case 'dipanggil':
                                                        $status_class = 'badge-dipanggil';
                                                        break;
                                                    case 'selesai':
                                                        $status_class = 'badge-selesai';
                                                        break;
                                                    case 'batal':
                                                        $status_class = 'badge-batal';
                                                        break;
                                                }
                                                ?>
                                                <span class="badge <?php echo $status_class; ?>"><?php echo ucfirst($queue['status']); ?></span>
                                            </td>
                                            <td><?php echo date('H:i', strtotime($queue['waktu_booking'])); ?></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        $(document).ready(function() {
            // Sidebar toggle
            $('#sidebarCollapse').on('click', function() {
                $('#sidebar').toggleClass('active');
                $('#content').toggleClass('active');
            });

            // Update time every second
            setInterval(function() {
                var now = new Date();
                var hours = now.getHours().toString().padStart(2, '0');
                var minutes = now.getMinutes().toString().padStart(2, '0');
                var seconds = now.getSeconds().toString().padStart(2, '0');
                $('#navbar-time').text(hours + ':' + minutes + ':' + seconds);
            }, 1000);
        });
    </script>
</body>
</html>

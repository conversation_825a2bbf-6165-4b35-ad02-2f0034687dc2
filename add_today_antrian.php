<?php
// Add antrian data for today
require_once 'includes/functions.php';

echo "<h1>Add Today's Antrian Data</h1>";

// Check if there's data for today
$sql = "SELECT COUNT(*) as count FROM antrian WHERE tanggal = CURDATE()";
$result = query($sql);
$row = fetch_assoc($result);

echo "<p>Current antrian count for today: " . $row['count'] . "</p>";

if ($row['count'] == 0) {
    echo "<p>No data for today. Adding sample data...</p>";
    
    // Add sample antrian data for today
    $antrian_data = [
        [
            'nomor' => 'T001',
            'nama' => '<PERSON>',
            'layanan' => 'teller',
            'status' => 'selesai',
            'loket_id' => 1,
            'waktu' => date('Y-m-d H:i:s', strtotime('-2 hours'))
        ],
        [
            'nomor' => 'C001',
            'nama' => 'Siti <PERSON>',
            'layanan' => 'cs',
            'status' => 'selesai',
            'loket_id' => 4,
            'waktu' => date('Y-m-d H:i:s', strtotime('-1 hour 30 minutes'))
        ],
        [
            'nomor' => 'K001',
            'nama' => 'Budi Santoso',
            'layanan' => 'kredit',
            'status' => 'selesai',
            'loket_id' => 6,
            'waktu' => date('Y-m-d H:i:s', strtotime('-1 hour'))
        ],
        [
            'nomor' => 'T002',
            'nama' => 'Dewi Lestari',
            'layanan' => 'teller',
            'status' => 'dipanggil',
            'loket_id' => 1,
            'waktu' => date('Y-m-d H:i:s', strtotime('-30 minutes'))
        ],
        [
            'nomor' => 'C002',
            'nama' => 'Rudi Hartono',
            'layanan' => 'cs',
            'status' => 'menunggu',
            'loket_id' => null,
            'waktu' => date('Y-m-d H:i:s', strtotime('-15 minutes'))
        ],
        [
            'nomor' => 'T003',
            'nama' => 'Andi Wijaya',
            'layanan' => 'teller',
            'status' => 'menunggu',
            'loket_id' => null,
            'waktu' => date('Y-m-d H:i:s', strtotime('-10 minutes'))
        ],
        [
            'nomor' => 'K002',
            'nama' => 'Maya Sari',
            'layanan' => 'kredit',
            'status' => 'menunggu',
            'loket_id' => null,
            'waktu' => date('Y-m-d H:i:s', strtotime('-5 minutes'))
        ]
    ];
    
    foreach ($antrian_data as $antrian) {
        $loket_part = $antrian['loket_id'] ? $antrian['loket_id'] : 'NULL';
        $sql = "INSERT INTO antrian (nomor_antrian, nama, layanan, jenis_layanan, tanggal, status, loket_id, waktu_booking, created_at) 
                VALUES ('{$antrian['nomor']}', '{$antrian['nama']}', '{$antrian['layanan']}', '{$antrian['layanan']}', 
                        CURDATE(), '{$antrian['status']}', $loket_part, '{$antrian['waktu']}', '{$antrian['waktu']}')";
        
        if (query($sql)) {
            echo "<p>✅ Added antrian: {$antrian['nomor']} - {$antrian['nama']} ({$antrian['layanan']}) - {$antrian['status']}</p>";
        } else {
            echo "<p>❌ Failed to add antrian: {$antrian['nomor']}</p>";
        }
    }
    
    echo "<h2>✅ Sample data added successfully!</h2>";
} else {
    echo "<p>Data already exists for today.</p>";
    
    // Show existing data
    echo "<h2>Existing Data for Today:</h2>";
    $sql = "SELECT * FROM antrian WHERE tanggal = CURDATE() ORDER BY id DESC";
    $result = query($sql);
    
    if ($result && num_rows($result) > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Nomor Antrian</th><th>Nama</th><th>Layanan</th><th>Status</th><th>Loket</th><th>Waktu</th></tr>";
        while ($row = fetch_assoc($result)) {
            echo "<tr>";
            echo "<td>" . $row['nomor_antrian'] . "</td>";
            echo "<td>" . $row['nama'] . "</td>";
            echo "<td>" . $row['jenis_layanan'] . "</td>";
            echo "<td>" . $row['status'] . "</td>";
            echo "<td>" . ($row['loket_id'] ? 'Loket ' . $row['loket_id'] : '-') . "</td>";
            echo "<td>" . date('H:i', strtotime($row['waktu_booking'])) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
}

// Test the query that will be used in latest_queue.php
echo "<h2>Test Latest Queue Query</h2>";
$sql = "SELECT a.id, a.nomor_antrian, a.nama,
        CASE
            WHEN a.jenis_layanan = 'teller' THEN 'Teller'
            WHEN a.jenis_layanan = 'cs' THEN 'Customer Service'
            WHEN a.jenis_layanan = 'kredit' THEN 'Kredit'
            ELSE 'Layanan Lain'
        END as layanan,
        CASE
            WHEN a.loket_id IS NOT NULL THEN CONCAT('Loket ', a.loket_id)
            ELSE '-'
        END as loket,
        a.status,
        TIME_FORMAT(a.waktu_booking, '%H:%i') as waktu_booking
        FROM antrian a
        WHERE a.tanggal = CURDATE()
        ORDER BY a.id DESC
        LIMIT 10";

$result = query($sql);
if ($result && num_rows($result) > 0) {
    echo "<p>✅ Query will return " . num_rows($result) . " results</p>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Nomor Antrian</th><th>Nama</th><th>Layanan</th><th>Loket</th><th>Status</th><th>Waktu</th></tr>";
    while ($row = fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['nomor_antrian'] . "</td>";
        echo "<td>" . $row['nama'] . "</td>";
        echo "<td>" . $row['layanan'] . "</td>";
        echo "<td>" . $row['loket'] . "</td>";
        echo "<td>" . $row['status'] . "</td>";
        echo "<td>" . $row['waktu_booking'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>❌ Query returned no results</p>";
}

echo "<br><br><a href='admin/index.php'>Go to Admin Dashboard</a>";
?>

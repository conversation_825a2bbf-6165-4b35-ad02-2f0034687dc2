/* Latest Queue Component Styling */
.latest-queue-card {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    border: none;
}

.latest-queue-card .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 15px 20px;
}

.latest-queue-card .card-header h5 {
    margin: 0;
    font-weight: 600;
    color: #1a6a83;
    display: flex;
    align-items: center;
}

.latest-queue-card .card-header h5 i {
    margin-right: 10px;
    color: #1a6a83;
}

.latest-queue-card .table {
    margin-bottom: 0;
}

.latest-queue-card .table th {
    background-color: #f8f9fa;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.8rem;
    color: #495057;
    border-top: none;
    padding: 12px 15px;
}

.latest-queue-card .table td {
    padding: 12px 15px;
    vertical-align: middle;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.latest-queue-card .table tr:hover {
    background-color: rgba(26, 106, 131, 0.03);
}

.latest-queue-card .badge {
    padding: 6px 10px;
    font-weight: 500;
    border-radius: 30px;
}

.latest-queue-card .badge-success {
    background-color: #28a745;
    color: white;
}

.latest-queue-card .badge-info {
    background-color: #17a2b8;
    color: white;
}

.latest-queue-card .badge-warning {
    background-color: #ffc107;
    color: #212529;
}

.latest-queue-card .badge-danger {
    background-color: #dc3545;
    color: white;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .latest-queue-card .table {
        min-width: 650px;
    }
    
    .latest-queue-card .card-body {
        padding: 0;
    }
    
    .latest-queue-card .table-responsive {
        border-radius: 0 0 10px 10px;
        overflow: hidden;
    }
}

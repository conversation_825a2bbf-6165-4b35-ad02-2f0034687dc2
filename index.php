<?php
/**
 * Main Index File
 *
 * This is the main entry point for the Bank Queue Management System
 */

// Include database configuration
require_once 'config/database.php';

// Include functions
require_once 'includes/functions.php';

// Check if user is already logged in
if (is_logged_in()) {
    // Redirect based on role
    if (is_admin()) {
        redirect('admin/index.php');
    } elseif (is_staff()) {
        redirect('staff/index.php');
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistem Antrian Bank BJB</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">

    <style>
        body {
            background: linear-gradient(135deg, #9c27b0 0%, #673ab7 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px 0;
        }
        .notification-bar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 10px 20px;
            text-align: center;
            z-index: 1000;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .notification-bar a {
            background-color: #4caf50;
            color: white;
            border: none;
            padding: 5px 15px;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s;
            text-decoration: none;
            font-size: 14px;
        }
        .notification-bar a:hover {
            background-color: #388e3c;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            text-decoration: none;
            color: white;
        }
        .main-menu {
            display: flex;
            justify-content: center;
            width: 100%;
            max-width: 1000px;
            margin: 0 auto;
            gap: 20px;
        }
        .menu-card {
            flex: 1;
            border-radius: 10px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
            transition: all 0.3s;
            color: white;
            text-decoration: none;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 150px;
            position: relative;
            overflow: hidden;
        }
        .menu-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
            text-decoration: none;
            color: white;
        }
        .menu-card h3 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 0;
            position: relative;
            z-index: 2;
        }
        .menu-card i {
            font-size: 48px;
            margin-bottom: 15px;
            position: relative;
            z-index: 2;
        }
        .menu-card::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 80px;
            height: 80px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 0 0 0 100%;
            z-index: 1;
        }
        .menu-card.red {
            background: linear-gradient(135deg, #e91e63 0%, #d81b60 100%);
        }
        .menu-card.blue {
            background: linear-gradient(135deg, #03a9f4 0%, #0288d1 100%);
        }
        .menu-card.yellow {
            background: linear-gradient(135deg, #ffc107 0%, #ffa000 100%);
        }
        .menu-card.green {
            background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
        }
        .menu-icon {
            position: absolute;
            top: 10px;
            right: 10px;
            font-size: 24px;
            opacity: 0.5;
            z-index: 2;
        }
        .modal-content {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        .modal-header {
            border-bottom: none;
            padding: 20px 30px 0;
        }
        .modal-body {
            padding: 20px 30px;
        }
        .modal-footer {
            border-top: none;
            padding: 0 30px 20px;
        }
        .btn-role {
            border-radius: 50px;
            padding: 10px 20px;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s;
        }
        .btn-role:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        .btn-nasabah {
            background-color: #e91e63;
            border-color: #e91e63;
            color: white;
        }
        .btn-petugas {
            background-color: #03a9f4;
            border-color: #03a9f4;
            color: white;
        }
        .btn-admin {
            background-color: #424242;
            border-color: #424242;
            color: white;
        }
        .role-icon {
            font-size: 24px;
            margin-bottom: 10px;
        }
        .welcome-alert {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            border-radius: 10px;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
        }

    </style>
</head>
<body>
    <!-- Notification Bar -->
    <div class="notification-bar">
        <i class="fas fa-info-circle"></i> Selamat Datang di Aplikasi Antrian Perbankan. Silahkan pilih halaman yang ingin ditampilkan.
        <a href="nasabah/index.php" class="btn btn-sm btn-success ml-3">
            <i class="fas fa-users"></i> Area Nasabah
        </a>
    </div>

    <!-- Main Content -->
    <div class="container">
        <?php if(isset($_SESSION['success_message'])): ?>
        <div class="alert welcome-alert alert-dismissible fade show mb-4" role="alert">
            <?php echo $_SESSION['success_message']; ?>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <?php unset($_SESSION['success_message']); ?>
        <?php endif; ?>

        <?php if(isset($_SESSION['error_message'])): ?>
        <div class="alert welcome-alert alert-dismissible fade show mb-4" role="alert">
            <?php echo $_SESSION['error_message']; ?>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <?php unset($_SESSION['error_message']); ?>
        <?php endif; ?>



        <div class="main-menu">
            <!-- Admin Card -->
            <a href="login_admin.php" class="menu-card red">
                <i class="fas fa-user-shield"></i>
                <h3>Admin</h3>
                <div class="menu-icon">
                    <i class="fas fa-cog"></i>
                </div>
            </a>

            <!-- Dashboard Antrian Card -->
            <a href="display/dashboard.php" class="menu-card blue">
                <i class="fas fa-desktop"></i>
                <h3>Dashboard Antrian</h3>
                <div class="menu-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
            </a>

            <!-- Petugas Card -->
            <a href="login.php" class="menu-card yellow">
                <i class="fas fa-user-tie"></i>
                <h3>Petugas</h3>
                <div class="menu-icon">
                    <i class="fas fa-cog"></i>
                </div>
            </a>

            <!-- Feedback Card -->
            <a href="nasabah_feedback.php" class="menu-card green">
                <i class="fas fa-star"></i>
                <h3>Feedback & Rating</h3>
                <div class="menu-icon">
                    <i class="fas fa-comments"></i>
                </div>
            </a>
        </div>
    </div>



    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
/**
 * Add User Script
 * 
 * This script allows you to add a new user (admin or staff) to the system.
 * For security reasons, this file should be deleted after use.
 */

// Include database configuration
require_once 'config/database.php';

// Process form submission
$success = false;
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = sanitize($_POST['username']);
    $password = $_POST['password'];
    $nama_lengkap = sanitize($_POST['nama_lengkap']);
    $role = sanitize($_POST['role']);
    
    // Validate input
    if (empty($username) || empty($password) || empty($nama_lengkap) || empty($role)) {
        $error = 'Semua field harus diisi.';
    } else {
        // Check if username already exists
        $sql = "SELECT * FROM users WHERE username = '$username'";
        $result = query($sql);
        
        if (num_rows($result) > 0) {
            $error = 'Username sudah digunakan.';
        } else {
            // Hash password
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            
            // Insert user
            $sql = "INSERT INTO users (username, password, nama_lengkap, role) 
                    VALUES ('$username', '$hashed_password', '$nama_lengkap', '$role')";
            
            if (query($sql)) {
                $success = true;
            } else {
                $error = 'Gagal menambahkan user.';
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tambah User - Sistem Antrian Bank</title>
    
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    
    <style>
        body {
            background-color: #f8f9fa;
            padding: 50px 0;
        }
        .container {
            max-width: 500px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Tambah User</h1>
            <p class="text-muted">Tambahkan user baru ke sistem.</p>
        </div>
        
        <?php if($success): ?>
        <div class="alert alert-success">
            <h4 class="alert-heading">User Berhasil Ditambahkan!</h4>
            <p>User baru telah berhasil ditambahkan ke sistem.</p>
            <hr>
            <p class="mb-0">
                <a href="login.php" class="btn btn-success">Login ke Sistem</a>
            </p>
        </div>
        <?php else: ?>
        <?php if($error): ?>
        <div class="alert alert-danger">
            <?php echo $error; ?>
        </div>
        <?php endif; ?>
        
        <form action="add_user.php" method="post">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" class="form-control" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" class="form-control" id="password" name="password" required>
            </div>
            
            <div class="form-group">
                <label for="nama_lengkap">Nama Lengkap</label>
                <input type="text" class="form-control" id="nama_lengkap" name="nama_lengkap" required>
            </div>
            
            <div class="form-group">
                <label for="role">Role</label>
                <select class="form-control" id="role" name="role" required>
                    <option value="">-- Pilih Role --</option>
                    <option value="admin">Admin</option>
                    <option value="staff">Staff</option>
                </select>
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn btn-primary btn-block">
                    <i class="fas fa-user-plus"></i> Tambah User
                </button>
            </div>
        </form>
        
        <div class="text-center mt-3">
            <a href="login.php" class="btn btn-link">
                <i class="fas fa-arrow-left"></i> Kembali ke Halaman Login
            </a>
        </div>
        <?php endif; ?>
        
        <div class="alert alert-warning mt-4">
            <i class="fas fa-exclamation-triangle"></i> <strong>Peringatan:</strong> Untuk alasan keamanan, hapus file ini setelah digunakan.
        </div>
    </div>
    
    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

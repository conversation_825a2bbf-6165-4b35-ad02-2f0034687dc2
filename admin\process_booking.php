<?php
// Process online bookings
error_reporting(E_ALL);
ini_set('display_errors', 1);

$page_title = 'Proses Booking Online';
$active_menu = 'bookings';
$is_admin = true;
require_once '../includes/functions.php';
require_once '../includes/db.php';
require_once '../includes/queue_connector.php';

// Check if user is logged in and is admin
if (!is_logged_in() || !is_admin()) {
    redirect('../login.php');
}

// Process booking confirmation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_booking'])) {
    $booking_id = $_POST['booking_id'];

    // Get booking details
    $sql = "SELECT * FROM bookings WHERE booking_id = '$booking_id'";
    $result = query($sql);

    if (num_rows($result) > 0) {
        $booking = fetch_assoc($result);

        // Check if booking is already processed
        if ($booking['status'] !== 'pending') {
            $_SESSION['error_message'] = "Booking ini sudah diproses sebelumnya.";
            redirect('bookings.php');
            exit;
        }

        // Map layanan to poli_id
        $poli_id = 1; // Default poli_id
        switch ($booking['layanan']) {
            case 'teller':
                $poli_id = 1; // Assuming 1 is Teller
                break;
            case 'cs':
                $poli_id = 2; // Assuming 2 is Customer Service
                break;
            case 'kredit':
                $poli_id = 3; // Assuming 3 is Kredit
                break;
        }

        // Get available loket based on jenis_layanan
        $jenis_layanan = $booking['layanan'];

        // Make sure jenis_layanan is one of the valid values
        if (!in_array($jenis_layanan, ['teller', 'cs', 'kredit'])) {
            // Default to teller if invalid
            $jenis_layanan = 'teller';
        }

        $sql_loket = "SELECT id FROM loket WHERE jenis_layanan = '$jenis_layanan' AND status = 'aktif' ORDER BY id ASC LIMIT 1";
        $result_loket = query($sql_loket);
        $loket_id = null;

        if (num_rows($result_loket) > 0) {
            $loket = fetch_assoc($result_loket);
            $loket_id = $loket['id'];
        }

        // Buat antrian dari booking menggunakan fungsi dari queue_connector
        $antrian_id = create_queue_from_booking(
            $booking_id,
            $booking['nomor_antrian'],
            $booking['tanggal'],
            $booking['nasabah_id'],
            $jenis_layanan
        );

        if ($antrian_id) {
            $_SESSION['success_message'] = "Booking berhasil dikonfirmasi dan dimasukkan ke antrian.";
        } else {
            $_SESSION['error_message'] = "Gagal memproses booking. Silakan coba lagi.";
        }
    } else {
        $_SESSION['error_message'] = "Booking tidak ditemukan.";
    }

    redirect('bookings.php');
    exit;
}

// Process booking rejection
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['reject_booking'])) {
    $booking_id = $_POST['booking_id'];
    $reason = $_POST['reason'];

    // Update booking status
    $sql = "UPDATE bookings SET status = 'cancelled', alasan_penolakan = '$reason', updated_at = NOW() WHERE booking_id = '$booking_id'";

    if (query($sql)) {
        $_SESSION['success_message'] = "Booking berhasil ditolak.";
    } else {
        $_SESSION['error_message'] = "Gagal menolak booking. Silakan coba lagi.";
    }

    redirect('bookings.php');
    exit;
}

// If no action specified, redirect to bookings page
redirect('bookings.php');

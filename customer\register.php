<?php
// Register page for customers
$page_title = 'Registrasi Akun Nasabah';
require_once '../includes/functions.php';

// Initialize variables
$name = '';
$phone = '';
$id_number = '';
$errors = [];

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate input
    $name = sanitize($_POST['name'] ?? '');
    $phone = sanitize($_POST['phone'] ?? '');
    $id_number = sanitize($_POST['id_number'] ?? '');

    // Validation
    if (empty($name)) {
        $errors[] = 'Nama lengkap harus diisi';
    }

    if (empty($phone)) {
        $errors[] = 'Nomor telepon harus diisi';
    } elseif (!preg_match('/^[0-9]{10,15}$/', $phone)) {
        $errors[] = 'Nomor telepon harus berisi 10-15 digit angka';
    }

    if (empty($id_number)) {
        $errors[] = 'Nomor identitas (KTP/SIM/Passport) harus diisi';
    } elseif (!preg_match('/^[0-9]{16}$/', $id_number)) {
        $errors[] = 'Nomor identitas harus berisi 16 digit angka';
    }

    // If no errors, proceed with registration
    if (empty($errors)) {
        // Check if ID number already exists
        $sql = "SELECT id FROM nasabah WHERE no_identitas = '$id_number'";
        $result = query($sql);

        if (num_rows($result) > 0) {
            $errors[] = 'Nomor identitas sudah terdaftar. Silakan gunakan nomor identitas lain.';
        } else {
            // Insert new customer
            $sql = "INSERT INTO nasabah (nama, no_identitas, jenis_identitas, no_telepon, status_verifikasi, created_at)
                    VALUES ('$name', '$id_number', 'ktp', '$phone', 'terverifikasi', NOW())";

            if (query($sql)) {
                // Registration successful
                $_SESSION['success_message'] = 'Registrasi berhasil! Silakan ambil nomor antrian.';
                redirect('index.php');
            } else {
                $errors[] = 'Terjadi kesalahan saat mendaftarkan akun. Silakan coba lagi.';
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registrasi Akun Nasabah - Bank BJB</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">

    <style>
        body {
            background: linear-gradient(135deg, #9c27b0 0%, #673ab7 100%);
            min-height: 100vh;
            padding: 30px 0;
        }
        .register-box {
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        .register-header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .register-header img {
            height: 80px;
            margin-bottom: 20px;
        }
        .btn-primary {
            background: linear-gradient(135deg, #e91e63 0%, #d81b60 100%);
            border: none;
            box-shadow: 0 4px 6px rgba(233, 30, 99, 0.2);
            padding: 10px 20px;
            border-radius: 50px;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #d81b60 0%, #c2185b 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(233, 30, 99, 0.3);
        }
        .btn-outline-secondary {
            color: #673ab7;
            border-color: #673ab7;
            border-radius: 50px;
        }
        .btn-outline-secondary:hover {
            background-color: #673ab7;
            color: white;
        }
        .form-control {
            border-radius: 8px;
            padding: 12px 15px;
            height: auto;
        }
        .form-control:focus {
            border-color: #9c27b0;
            box-shadow: 0 0 0 0.2rem rgba(156, 39, 176, 0.25);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="register-box">
                    <div class="register-header">
                        <img src="../assets/img/logo.jpeg" alt="Bank BJB" style="max-width: 200px;">
                        <h2>Registrasi Akun Nasabah</h2>
                        <p class="text-muted">Silakan isi formulir di bawah ini untuk membuat akun nasabah baru</p>
                    </div>

                    <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                            <li><?php echo $error; ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <?php endif; ?>

                    <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>">
                        <div class="form-group">
                            <label for="name"><i class="fas fa-user mr-2"></i>Nama Lengkap</label>
                            <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($name); ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="phone"><i class="fas fa-phone mr-2"></i>Nomor Telepon</label>
                            <input type="tel" class="form-control" id="phone" name="phone" value="<?php echo htmlspecialchars($phone); ?>" required>
                            <small class="form-text text-muted">Contoh: 08123456789</small>
                        </div>

                        <div class="form-group">
                            <label for="id_number"><i class="fas fa-id-card mr-2"></i>Nomor Identitas (KTP/SIM/Passport)</label>
                            <input type="text" class="form-control" id="id_number" name="id_number" value="<?php echo htmlspecialchars($id_number); ?>" required>
                            <small class="form-text text-muted">Masukkan 16 digit nomor KTP</small>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary btn-block"><i class="fas fa-user-plus mr-2"></i>Daftar</button>
                        </div>

                        <div class="text-center mt-3">
                            <a href="../index.php" class="btn btn-outline-secondary mt-2"><i class="fas fa-home mr-2"></i>Kembali ke Beranda</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

/* Admin Dashboard Styles */
:root {
    --primary-color: #1a6a83;
    --secondary-color: #0e4b5e;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
}

body {
    overflow-x: hidden;
    background-color: #f8f9fa;
}

#wrapper {
    display: flex;
}

#sidebar-wrapper {
    min-height: 100vh;
    width: 250px;
    margin-left: -250px;
    background: linear-gradient(180deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    transition: margin 0.25s ease-out;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    z-index: 1;
}

#sidebar-wrapper .sidebar-heading {
    padding: 1.2rem 1.25rem;
    font-size: 1.2rem;
    color: white;
    background-color: rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

#sidebar-wrapper .sidebar-heading img {
    height: 40px;
    margin-right: 10px;
}

#sidebar-wrapper .list-group {
    width: 250px;
}

#sidebar-wrapper .list-group-item {
    border: none;
    padding: 0.75rem 1.25rem;
    background-color: transparent;
    color: rgba(255, 255, 255, 0.8);
    border-left: 3px solid transparent;
    transition: all 0.3s;
}

#sidebar-wrapper .list-group-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    border-left: 3px solid var(--warning-color);
}

#sidebar-wrapper .list-group-item.active {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border-left: 3px solid var(--warning-color);
}

#sidebar-wrapper .list-group-item i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

#page-content-wrapper {
    min-width: 100vw;
    transition: margin-left 0.25s ease-out;
}

#wrapper.toggled #sidebar-wrapper {
    margin-left: 0;
}

.navbar {
    background-color: white;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    padding: 0.5rem 1rem;
}

.navbar-brand {
    font-weight: 600;
    color: var(--primary-color);
}

.navbar .navbar-toggler {
    border: none;
    padding: 0.25rem 0.5rem;
}

.navbar .navbar-toggler:focus {
    outline: none;
    box-shadow: none;
}

.navbar .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3e%3cpath stroke='rgba(26, 106, 131, 1)' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

.navbar .dropdown-menu {
    right: 0;
    left: auto;
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.navbar .dropdown-item i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.navbar .dropdown-item:active {
    background-color: var(--primary-color);
}

.navbar .user-dropdown .dropdown-toggle::after {
    display: none;
}

.navbar .user-dropdown .dropdown-toggle {
    display: flex;
    align-items: center;
}

.navbar .user-dropdown .user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 10px;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.navbar .user-dropdown .user-name {
    font-weight: 600;
    color: var(--dark-color);
}

.container-fluid {
    padding: 1.5rem;
}

.card {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: white;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    padding: 1rem 1.25rem;
}

.card-title {
    margin-bottom: 0;
    color: var(--dark-color);
    font-weight: 600;
}

.card-body {
    padding: 1.25rem;
}

.dashboard-card {
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    color: white;
    position: relative;
    overflow: hidden;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.dashboard-card.primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.dashboard-card.success {
    background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
}

.dashboard-card.warning {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.dashboard-card.danger {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.dashboard-card .card-icon {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 4rem;
    opacity: 0.3;
}

.dashboard-card .card-value {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.dashboard-card .card-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0;
    color: rgba(255, 255, 255, 0.8);
}

.table {
    margin-bottom: 0;
}

.table thead th {
    border-top: none;
    background-color: #f8f9fa;
    color: var(--dark-color);
    font-weight: 600;
}

.table-hover tbody tr:hover {
    background-color: rgba(26, 106, 131, 0.05);
}

.badge-status {
    padding: 0.4rem 0.6rem;
    border-radius: 50rem;
    font-weight: 500;
}

.badge-waiting {
    background-color: rgba(255, 193, 7, 0.2);
    color: #856404;
}

.badge-serving {
    background-color: rgba(40, 167, 69, 0.2);
    color: #155724;
}

.badge-completed {
    background-color: rgba(23, 162, 184, 0.2);
    color: #0c5460;
}

.badge-skipped {
    background-color: rgba(220, 53, 69, 0.2);
    color: #721c24;
}

.btn-icon {
    width: 36px;
    height: 36px;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.25rem;
    margin-right: 0.25rem;
}

.pagination {
    margin-bottom: 0;
}

.pagination .page-link {
    color: var(--primary-color);
    border-color: #dee2e6;
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.chart-container {
    position: relative;
    margin: auto;
    height: 300px;
}

@media (min-width: 768px) {
    #sidebar-wrapper {
        margin-left: 0;
    }

    #page-content-wrapper {
        min-width: 0;
        width: 100%;
    }

    #wrapper.toggled #sidebar-wrapper {
        margin-left: -250px;
    }
}

<?php
/**
 * Installation Script
 * 
 * This script will help you set up the Bank Queue Management System.
 */

// Check PHP version
$php_version = phpversion();
$php_version_ok = version_compare($php_version, '7.2.0', '>=');

// Check MySQL extension
$mysql_ok = extension_loaded('mysqli');

// Check GD extension (for image processing)
$gd_ok = extension_loaded('gd');

// Check if config file exists
$config_file = __DIR__ . '/config/database.php';
$config_exists = file_exists($config_file);

// Check if uploads directory is writable
$uploads_dir = __DIR__ . '/uploads';
$uploads_writable = is_writable($uploads_dir);

// Check if assets directory is writable
$assets_dir = __DIR__ . '/assets';
$assets_writable = is_writable($assets_dir);

// Process form submission
$db_error = '';
$install_success = false;

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $db_host = $_POST['db_host'];
    $db_name = $_POST['db_name'];
    $db_user = $_POST['db_user'];
    $db_pass = $_POST['db_pass'];
    
    // Test database connection
    $conn = @mysqli_connect($db_host, $db_user, $db_pass);
    
    if (!$conn) {
        $db_error = 'Tidak dapat terhubung ke database server. Error: ' . mysqli_connect_error();
    } else {
        // Check if database exists
        $db_exists = mysqli_select_db($conn, $db_name);
        
        if (!$db_exists) {
            // Create database
            $sql = "CREATE DATABASE IF NOT EXISTS `$db_name` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci";
            if (!mysqli_query($conn, $sql)) {
                $db_error = 'Tidak dapat membuat database. Error: ' . mysqli_error($conn);
            } else {
                $db_exists = true;
            }
        }
        
        if ($db_exists) {
            // Select database
            mysqli_select_db($conn, $db_name);
            
            // Import SQL file
            $sql_file = file_get_contents(__DIR__ . '/database.sql');
            $sql_file = str_replace('CREATE DATABASE IF NOT EXISTS antrian_bank;', '', $sql_file);
            $sql_file = str_replace('USE antrian_bank;', '', $sql_file);
            
            // Split SQL file into individual queries
            $queries = explode(';', $sql_file);
            
            // Execute each query
            $error = false;
            foreach ($queries as $query) {
                $query = trim($query);
                if (!empty($query)) {
                    if (!mysqli_query($conn, $query)) {
                        $db_error = 'Error importing database: ' . mysqli_error($conn);
                        $error = true;
                        break;
                    }
                }
            }
            
            if (!$error) {
                // Update config file
                $config_content = file_get_contents($config_file);
                $config_content = str_replace("define('DB_SERVER', 'localhost');", "define('DB_SERVER', '$db_host');", $config_content);
                $config_content = str_replace("define('DB_USERNAME', 'root');", "define('DB_USERNAME', '$db_user');", $config_content);
                $config_content = str_replace("define('DB_PASSWORD', '');", "define('DB_PASSWORD', '$db_pass');", $config_content);
                $config_content = str_replace("define('DB_NAME', 'antrian_bank');", "define('DB_NAME', '$db_name');", $config_content);
                
                file_put_contents($config_file, $config_content);
                
                $install_success = true;
            }
        }
        
        mysqli_close($conn);
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instalasi - Sistem Antrian Bank</title>
    
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    
    <style>
        body {
            background-color: #f8f9fa;
            padding: 50px 0;
        }
        .install-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            margin-bottom: 10px;
        }
        .requirement {
            margin-bottom: 5px;
        }
        .requirement i.fa-check {
            color: #28a745;
        }
        .requirement i.fa-times {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="install-container">
            <div class="header">
                <h1>Instalasi Sistem Antrian Bank</h1>
                <p class="text-muted">Ikuti langkah-langkah berikut untuk menginstal sistem.</p>
            </div>
            
            <?php if($install_success): ?>
            <div class="alert alert-success">
                <h4 class="alert-heading">Instalasi Berhasil!</h4>
                <p>Sistem Antrian Bank telah berhasil diinstal. Silahkan login dengan kredensial berikut:</p>
                <ul>
                    <li>Username: <strong>admin</strong></li>
                    <li>Password: <strong>admin123</strong></li>
                </ul>
                <hr>
                <p class="mb-0">
                    <a href="index.php" class="btn btn-success">Mulai Menggunakan Sistem</a>
                </p>
            </div>
            <?php else: ?>
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <i class="fas fa-server"></i> Persyaratan Sistem
                </div>
                <div class="card-body">
                    <div class="requirement">
                        <i class="fas <?php echo $php_version_ok ? 'fa-check' : 'fa-times'; ?>"></i>
                        PHP versi <?php echo $php_version; ?> 
                        <?php if(!$php_version_ok): ?>
                        <span class="text-danger">(Dibutuhkan PHP 7.2 atau lebih tinggi)</span>
                        <?php endif; ?>
                    </div>
                    
                    <div class="requirement">
                        <i class="fas <?php echo $mysql_ok ? 'fa-check' : 'fa-times'; ?>"></i>
                        Ekstensi MySQL 
                        <?php if(!$mysql_ok): ?>
                        <span class="text-danger">(Ekstensi mysqli tidak tersedia)</span>
                        <?php endif; ?>
                    </div>
                    
                    <div class="requirement">
                        <i class="fas <?php echo $gd_ok ? 'fa-check' : 'fa-times'; ?>"></i>
                        Ekstensi GD 
                        <?php if(!$gd_ok): ?>
                        <span class="text-danger">(Ekstensi GD tidak tersedia)</span>
                        <?php endif; ?>
                    </div>
                    
                    <div class="requirement">
                        <i class="fas <?php echo $config_exists ? 'fa-check' : 'fa-times'; ?>"></i>
                        File konfigurasi 
                        <?php if(!$config_exists): ?>
                        <span class="text-danger">(File config/database.php tidak ditemukan)</span>
                        <?php endif; ?>
                    </div>
                    
                    <div class="requirement">
                        <i class="fas <?php echo $uploads_writable ? 'fa-check' : 'fa-times'; ?>"></i>
                        Folder uploads 
                        <?php if(!$uploads_writable): ?>
                        <span class="text-danger">(Folder uploads tidak dapat ditulis)</span>
                        <?php endif; ?>
                    </div>
                    
                    <div class="requirement">
                        <i class="fas <?php echo $assets_writable ? 'fa-check' : 'fa-times'; ?>"></i>
                        Folder assets 
                        <?php if(!$assets_writable): ?>
                        <span class="text-danger">(Folder assets tidak dapat ditulis)</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <?php if($php_version_ok && $mysql_ok && $config_exists && $uploads_writable && $assets_writable): ?>
            <div class="card">
                <div class="card-header bg-success text-white">
                    <i class="fas fa-database"></i> Konfigurasi Database
                </div>
                <div class="card-body">
                    <?php if($db_error): ?>
                    <div class="alert alert-danger">
                        <?php echo $db_error; ?>
                    </div>
                    <?php endif; ?>
                    
                    <form action="install.php" method="post">
                        <div class="form-group">
                            <label for="db_host">Host Database</label>
                            <input type="text" class="form-control" id="db_host" name="db_host" value="localhost" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="db_name">Nama Database</label>
                            <input type="text" class="form-control" id="db_name" name="db_name" value="antrian_bank" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="db_user">Username Database</label>
                            <input type="text" class="form-control" id="db_user" name="db_user" value="root" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="db_pass">Password Database</label>
                            <input type="password" class="form-control" id="db_pass" name="db_pass" value="">
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary btn-block">
                                <i class="fas fa-cog"></i> Instal Sistem
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            <?php else: ?>
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i> Silahkan perbaiki persyaratan sistem yang belum terpenuhi sebelum melanjutkan instalasi.
            </div>
            <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
// Staff Laporan page
$page_title = '<PERSON>poran An<PERSON>';
$active_menu = 'laporan';
require_once '../includes/functions.php';

// Check if user is logged in and is staff
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'staff') {
    redirect('../login.php');
}

// Get staff info
$staff_id = $_SESSION['user_id'];
$sql = "SELECT * FROM users WHERE id = $staff_id";
$result = query($sql);
$staff_info = fetch_assoc($result);

// Get filter parameters
$filter_type = $_GET['filter'] ?? 'hari';
$start_date = $_GET['start_date'] ?? date('Y-m-d');
$end_date = $_GET['end_date'] ?? date('Y-m-d');
$layanan = $_GET['layanan'] ?? '';

// Set date range based on filter type
switch ($filter_type) {
    case 'bulan':
        $start_date = date('Y-m-01');
        $end_date = date('Y-m-t');
        break;
    case 'tahun':
        $start_date = date('Y-01-01');
        $end_date = date('Y-12-31');
        break;
    default:
        $start_date = $_GET['start_date'] ?? date('Y-m-d');
        $end_date = $_GET['end_date'] ?? date('Y-m-d');
}

// Build WHERE clause - staff only sees their own counter data
$where_conditions = ["a.tanggal BETWEEN '$start_date' AND '$end_date'"];

// Get staff's assigned loket
$sql = "SELECT id FROM loket WHERE user_id = $staff_id AND status = 'aktif'";
$result = query($sql);
$staff_loket = fetch_assoc($result);

if ($staff_loket) {
    $where_conditions[] = "a.loket_id = " . $staff_loket['id'];
} else {
    // If staff has no assigned loket, show no data
    $where_conditions[] = "a.loket_id = 0";
}

if (!empty($layanan)) {
    $where_conditions[] = "a.jenis_layanan = '$layanan'";
}
$where_clause = implode(' AND ', $where_conditions);

// Get booking data for staff's counter only
$sql = "SELECT a.*, 
               COALESCE(n.nama, b.nama, 'Walk-in Customer') as nama_customer,
               COALESCE(n.email, b.email, '-') as email_customer,
               COALESCE(n.no_hp, b.no_hp, '-') as no_hp_customer,
               l.nama_loket,
               p.nama_poli,
               CASE 
                   WHEN a.booking_dari = 'online' THEN 'Online Booking'
                   ELSE 'Walk-in'
               END as sumber_booking
        FROM antrian a
        LEFT JOIN nasabah n ON a.nasabah_id = n.id
        LEFT JOIN bookings b ON a.booking_id = b.booking_id
        LEFT JOIN loket l ON a.loket_id = l.id
        LEFT JOIN poli p ON a.poli_id = p.id
        WHERE $where_clause
        ORDER BY a.tanggal DESC, a.created_at DESC";

$result = query($sql);
$data_antrian = fetch_all($result);

// Get statistics for staff's counter
$sql_stats = "SELECT 
                COUNT(*) as total_antrian,
                COUNT(CASE WHEN status = 'selesai' THEN 1 END) as selesai,
                COUNT(CASE WHEN status = 'batal' THEN 1 END) as batal,
                COUNT(CASE WHEN status = 'dipanggil' THEN 1 END) as dipanggil,
                COUNT(CASE WHEN booking_dari = 'online' THEN 1 END) as online_booking,
                AVG(CASE WHEN status = 'selesai' AND waktu_dipanggil IS NOT NULL AND waktu_selesai IS NOT NULL 
                    THEN TIMESTAMPDIFF(MINUTE, waktu_dipanggil, waktu_selesai) END) as avg_service_time
              FROM antrian a
              WHERE $where_clause";
$result_stats = query($sql_stats);
$stats = fetch_assoc($result_stats);

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-chart-bar me-2"></i>Laporan Antrian - <?= $staff_info['nama_lengkap'] ?></h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-success" onclick="exportToExcel()">
                        <i class="fas fa-file-excel me-1"></i>Export Excel
                    </button>
                </div>
            </div>

            <!-- Filter Form -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filter Laporan</h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="filter" class="form-label">Periode</label>
                            <select class="form-select" id="filter" name="filter" onchange="toggleDateInputs()">
                                <option value="hari" <?= $filter_type == 'hari' ? 'selected' : '' ?>>Harian</option>
                                <option value="bulan" <?= $filter_type == 'bulan' ? 'selected' : '' ?>>Bulanan</option>
                                <option value="tahun" <?= $filter_type == 'tahun' ? 'selected' : '' ?>>Tahunan</option>
                                <option value="custom" <?= $filter_type == 'custom' ? 'selected' : '' ?>>Custom</option>
                            </select>
                        </div>
                        <div class="col-md-3" id="start-date-group">
                            <label for="start_date" class="form-label">Tanggal Mulai</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="<?= $start_date ?>">
                        </div>
                        <div class="col-md-3" id="end-date-group">
                            <label for="end_date" class="form-label">Tanggal Akhir</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="<?= $end_date ?>">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>Filter
                                </button>
                                <a href="laporan.php" class="btn btn-secondary">
                                    <i class="fas fa-refresh me-1"></i>Reset
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?= $stats['total_antrian'] ?></h4>
                                    <p class="mb-0">Total Antrian</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?= $stats['selesai'] ?></h4>
                                    <p class="mb-0">Selesai Dilayani</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?= $stats['online_booking'] ?></h4>
                                    <p class="mb-0">Online Booking</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-globe fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?= number_format($stats['avg_service_time'] ?? 0, 1) ?> min</h4>
                                    <p class="mb-0">Rata-rata Layanan</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance Chart -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Status Antrian</h5>
                        </div>
                        <div class="card-body text-center">
                            <div class="row">
                                <div class="col-6">
                                    <h3 class="text-success"><?= $stats['selesai'] ?></h3>
                                    <p>Selesai</p>
                                </div>
                                <div class="col-6">
                                    <h3 class="text-info"><?= $stats['dipanggil'] ?></h3>
                                    <p>Sedang Dilayani</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Sumber Booking</h5>
                        </div>
                        <div class="card-body text-center">
                            <div class="row">
                                <div class="col-6">
                                    <h3 class="text-primary"><?= $stats['online_booking'] ?></h3>
                                    <p>Online</p>
                                </div>
                                <div class="col-6">
                                    <h3 class="text-secondary"><?= $stats['total_antrian'] - $stats['online_booking'] ?></h3>
                                    <p>Walk-in</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Data Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-table me-2"></i>Data Antrian (<?= date('d/m/Y', strtotime($start_date)) ?> - <?= date('d/m/Y', strtotime($end_date)) ?>)</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="dataTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>No</th>
                                    <th>Tanggal</th>
                                    <th>Nomor Antrian</th>
                                    <th>Nama Customer</th>
                                    <th>Email</th>
                                    <th>No HP</th>
                                    <th>Status</th>
                                    <th>Sumber</th>
                                    <th>Waktu Booking</th>
                                    <th>Waktu Selesai</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (count($data_antrian) > 0): ?>
                                    <?php foreach ($data_antrian as $index => $antrian): ?>
                                    <tr>
                                        <td><?= $index + 1 ?></td>
                                        <td><?= date('d/m/Y', strtotime($antrian['tanggal'])) ?></td>
                                        <td><span class="badge bg-primary"><?= $antrian['nomor_antrian'] ?></span></td>
                                        <td><?= $antrian['nama_customer'] ?></td>
                                        <td><?= $antrian['email_customer'] ?></td>
                                        <td><?= $antrian['no_hp_customer'] ?></td>
                                        <td>
                                            <?php
                                            $status_badges = [
                                                'menunggu' => 'bg-warning',
                                                'dipanggil' => 'bg-info',
                                                'selesai' => 'bg-success',
                                                'batal' => 'bg-danger'
                                            ];
                                            $status_class = $status_badges[$antrian['status']] ?? 'bg-secondary';
                                            ?>
                                            <span class="badge <?= $status_class ?>"><?= ucfirst($antrian['status']) ?></span>
                                        </td>
                                        <td>
                                            <span class="badge <?= $antrian['booking_dari'] == 'online' ? 'bg-primary' : 'bg-secondary' ?>">
                                                <?= $antrian['sumber_booking'] ?>
                                            </span>
                                        </td>
                                        <td><?= date('H:i', strtotime($antrian['waktu_booking'])) ?></td>
                                        <td><?= $antrian['waktu_selesai'] ? date('H:i', strtotime($antrian['waktu_selesai'])) : '-' ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="10" class="text-center">Tidak ada data antrian untuk periode yang dipilih</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleDateInputs() {
    const filter = document.getElementById('filter').value;
    const startDateGroup = document.getElementById('start-date-group');
    const endDateGroup = document.getElementById('end-date-group');

    if (filter === 'custom') {
        startDateGroup.style.display = 'block';
        endDateGroup.style.display = 'block';
    } else if (filter === 'hari') {
        startDateGroup.style.display = 'block';
        endDateGroup.style.display = 'block';
    } else {
        startDateGroup.style.display = 'none';
        endDateGroup.style.display = 'none';
    }
}

function exportToExcel() {
    const table = document.getElementById('dataTable');
    const wb = XLSX.utils.table_to_book(table, {sheet: "Laporan Antrian Staff"});
    const filename = `Laporan_Staff_${new Date().toISOString().slice(0,10)}.xlsx`;
    XLSX.writeFile(wb, filename);
}

// Initialize date inputs visibility
document.addEventListener('DOMContentLoaded', function() {
    toggleDateInputs();
});
</script>

<!-- Include SheetJS for Excel export -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

<?php include '../includes/footer.php'; ?>

<?php
// Nasabah booking page
$page_title = 'Booking Online Nasabah';
require_once 'includes/functions.php';
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Bank BJB</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">

    <style>
        body {
            background: linear-gradient(135deg, #9c27b0 0%, #673ab7 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px 0;
            color: #333;
        }
        .notification-bar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 10px 20px;
            text-align: center;
            z-index: 1000;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
        }
        .booking-container {
            background-color: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
        }
        .booking-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 1px solid #eee;
            padding-bottom: 20px;
        }
        .booking-header img {
            height: 80px;
            margin-bottom: 15px;
        }
        .booking-options {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: center;
            margin-bottom: 30px;
        }
        .booking-option {
            flex: 1;
            min-width: 200px;
            background: linear-gradient(135deg, #9c27b0 0%, #673ab7 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
            cursor: pointer;
            text-decoration: none;
        }
        .booking-option:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            text-decoration: none;
            color: white;
        }
        .booking-option i {
            font-size: 36px;
            margin-bottom: 10px;
            display: block;
        }
        .booking-option h4 {
            font-size: 18px;
            margin-bottom: 10px;
        }
        .booking-option p {
            font-size: 14px;
            opacity: 0.8;
        }
        .booking-form {
            background-color: #f9f9f9;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        .form-group label {
            font-weight: 600;
            color: #555;
        }
        .btn-primary {
            background: linear-gradient(135deg, #9c27b0 0%, #673ab7 100%);
            border: none;
            border-radius: 50px;
            padding: 10px 30px;
            font-weight: 600;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
        }
        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            background: linear-gradient(135deg, #8e24aa 0%, #5e35b1 100%);
        }
        .back-button {
            display: inline-block;
            color: #9c27b0;
            margin-top: 20px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s;
        }
        .back-button:hover {
            transform: translateX(-5px);
            text-decoration: none;
            color: #7b1fa2;
        }
        .welcome-alert {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            border-radius: 10px;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
        }
        .steps-container {
            margin: 30px 0;
        }
        .step {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .step-number {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #9c27b0 0%, #673ab7 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .step-content {
            flex: 1;
        }
        .step-content h5 {
            margin-bottom: 5px;
            color: #333;
        }
        .step-content p {
            color: #666;
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <!-- Notification Bar -->
    <div class="notification-bar">
        <i class="fas fa-info-circle"></i> Selamat Datang di Layanan Booking Online Bank BJB
    </div>

    <!-- Main Content -->
    <div class="container">
        <?php if(isset($_SESSION['success_message'])): ?>
        <div class="alert welcome-alert alert-dismissible fade show mb-4" role="alert">
            <?php echo $_SESSION['success_message']; ?>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <?php unset($_SESSION['success_message']); ?>
        <?php endif; ?>

        <?php if(isset($_SESSION['error_message'])): ?>
        <div class="alert welcome-alert alert-dismissible fade show mb-4" role="alert">
            <?php echo $_SESSION['error_message']; ?>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <?php unset($_SESSION['error_message']); ?>
        <?php endif; ?>

        <div class="booking-container">
            <div class="booking-header">
                <img src="assets/img/logo.jpeg" alt="Bank BJB">
                <h2>Booking Online Nasabah</h2>
                <p class="text-muted">Booking antrian dari rumah untuk kenyamanan Anda</p>
            </div>

            <div class="steps-container">
                <h4 class="mb-4">Cara Booking Antrian Online:</h4>
                
                <div class="step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h5>Registrasi Akun</h5>
                        <p>Daftar akun dengan data diri yang valid untuk verifikasi identitas</p>
                    </div>
                </div>
                
                <div class="step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h5>Pilih Layanan</h5>
                        <p>Pilih jenis layanan yang Anda butuhkan (Teller, Customer Service, atau Kredit)</p>
                    </div>
                </div>
                
                <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h5>Pilih Tanggal & Waktu</h5>
                        <p>Tentukan tanggal dan estimasi waktu kedatangan Anda</p>
                    </div>
                </div>
                
                <div class="step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h5>Verifikasi KTP/Buku Tabungan</h5>
                        <p>Unggah foto KTP atau buku tabungan untuk verifikasi identitas</p>
                    </div>
                </div>
                
                <div class="step">
                    <div class="step-number">5</div>
                    <div class="step-content">
                        <h5>Dapatkan Nomor Antrian</h5>
                        <p>Nomor antrian akan dikirim melalui email dan dapat dilihat di akun Anda</p>
                    </div>
                </div>
            </div>

            <div class="booking-options">
                <a href="customer/register.php" class="booking-option">
                    <i class="fas fa-user-plus"></i>
                    <h4>Registrasi Akun</h4>
                    <p>Daftar akun baru untuk menggunakan layanan booking</p>
                </a>
                
                <a href="customer/booking.php" class="booking-option">
                    <i class="fas fa-calendar-check"></i>
                    <h4>Booking Antrian</h4>
                    <p>Booking antrian untuk kunjungan ke bank</p>
                </a>
                
                <a href="customer/check_status.php" class="booking-option">
                    <i class="fas fa-search"></i>
                    <h4>Cek Status</h4>
                    <p>Lihat status booking dan nomor antrian Anda</p>
                </a>
            </div>

            <div class="text-center mt-4">
                <a href="index.php" class="back-button">
                    <i class="fas fa-arrow-left"></i> Kembali ke Halaman Utama
                </a>
            </div>
        </div>
    </div>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

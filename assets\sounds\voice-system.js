/**
 * Voice Announcement System for Queue Management
 *
 * This script provides functionality to play voice announcements for queue numbers
 * similar to the system shown in https://www.youtube.com/watch?v=Mxl6zzRYcF8
 */

// Create AudioContext
let audioContext;
let audioBuffers = {};
let isLoaded = false;
let isPlaying = false;
let audioQueue = [];

// Audio files to preload
const audioFiles = [
    { name: 'bell', path: '../assets/sounds/bell.mp3' },
    { name: 'nomor-antrian', path: '../assets/sounds/nomor-antrian.mp3' },
    { name: 'silahkan-ke', path: '../assets/sounds/silahkan-ke.mp3' },
    { name: '0', path: '../assets/sounds/0.mp3' },
    { name: '1', path: '../assets/sounds/1.mp3' },
    { name: '2', path: '../assets/sounds/2.mp3' },
    { name: '3', path: '../assets/sounds/3.mp3' },
    { name: '4', path: '../assets/sounds/4.mp3' },
    { name: '5', path: '../assets/sounds/5.mp3' },
    { name: '6', path: '../assets/sounds/6.mp3' },
    { name: '7', path: '../assets/sounds/7.mp3' },
    { name: '8', path: '../assets/sounds/8.mp3' },
    { name: '9', path: '../assets/sounds/9.mp3' },
    { name: 'belas', path: '../assets/sounds/belas.mp3' },
    { name: 'puluh', path: '../assets/sounds/puluh.mp3' },
    { name: 'ratus', path: '../assets/sounds/ratus.mp3' },
    { name: 'ribu', path: '../assets/sounds/ribu.mp3' },
    { name: 'sebelas', path: '../assets/sounds/sebelas.mp3' },
    { name: 'sepuluh', path: '../assets/sounds/sepuluh.mp3' },
    { name: 'seratus', path: '../assets/sounds/seratus.mp3' },
    { name: 'seribu', path: '../assets/sounds/seribu.mp3' },
    { name: 'loket', path: '../assets/sounds/loket.mp3' },
    { name: 'teller', path: '../assets/sounds/teller.mp3' },
    { name: 'customer-service', path: '../assets/sounds/customer-service.mp3' },
    { name: 'kredit', path: '../assets/sounds/kredit.mp3' }
];

// Function to initialize AudioContext (must be called from a user gesture)
function initAudio() {
    if (!audioContext) {
        audioContext = new (window.AudioContext || window.webkitAudioContext)();

        // Load all audio files
        if (!isLoaded) {
            loadAudioFiles();
        }
    }
    return audioContext;
}

// Function to load all audio files
function loadAudioFiles() {
    let loadedCount = 0;

    audioFiles.forEach(file => {
        // Create a dummy audio element to check if the file exists
        const audio = new Audio(file.path);
        audio.addEventListener('error', () => {
            console.warn(`Audio file ${file.path} not found. Using synthesized audio instead.`);
            // If file doesn't exist, we'll use synthesized audio
            audioBuffers[file.name] = null;
            loadedCount++;

            if (loadedCount === audioFiles.length) {
                isLoaded = true;
                console.log('All audio files processed');

                // Process any queued announcements
                processAudioQueue();
            }
        });

        audio.addEventListener('canplaythrough', () => {
            // File exists, load it into AudioContext
            fetch(file.path)
                .then(response => response.arrayBuffer())
                .then(arrayBuffer => audioContext.decodeAudioData(arrayBuffer))
                .then(audioBuffer => {
                    audioBuffers[file.name] = audioBuffer;
                    loadedCount++;

                    if (loadedCount === audioFiles.length) {
                        isLoaded = true;
                        console.log('All audio files loaded');

                        // Process any queued announcements
                        processAudioQueue();
                    }
                })
                .catch(error => {
                    console.error(`Error loading audio file ${file.path}:`, error);
                    audioBuffers[file.name] = null;
                    loadedCount++;

                    if (loadedCount === audioFiles.length) {
                        isLoaded = true;
                        console.log('All audio files processed with some errors');

                        // Process any queued announcements
                        processAudioQueue();
                    }
                });
        });

        // Start loading
        audio.load();
    });
}

// Function to play a sequence of audio files
function playAudioSequence(sequence) {
    if (isPlaying) {
        // Queue this announcement for later
        audioQueue.push(sequence);
        return;
    }

    isPlaying = true;
    let currentIndex = 0;

    function playNext() {
        if (currentIndex >= sequence.length) {
            isPlaying = false;

            // Check if there are more announcements in the queue
            processAudioQueue();
            return;
        }

        const item = sequence[currentIndex];
        currentIndex++;

        // Special handling for bell sound
        if (item === 'bell') {
            console.log('Playing bank bell sound...');

            if (audioBuffers[item]) {
                // Play the pre-recorded bell sound
                const bellSource = audioContext.createBufferSource();
                bellSource.buffer = audioBuffers[item];

                // Create gain node for volume control
                const gainNode = audioContext.createGain();
                gainNode.gain.value = 0.7; // Adjust volume to 70%

                // Connect nodes
                bellSource.connect(gainNode);
                gainNode.connect(audioContext.destination);

                bellSource.onended = function() {
                    // Add a small delay after the bell before continuing
                    setTimeout(playNext, 500);
                };

                bellSource.start();
            } else {
                // If bell sound not available, use a synthesized bell sound
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.type = 'sine';
                oscillator.frequency.setValueAtTime(1318.51, audioContext.currentTime); // E6

                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 1);

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.start();
                oscillator.stop(audioContext.currentTime + 1);

                setTimeout(playNext, 1500);
            }
        } else if (audioBuffers[item]) {
            // Play the pre-recorded audio
            const source = audioContext.createBufferSource();
            source.buffer = audioBuffers[item];
            source.connect(audioContext.destination);
            source.onended = playNext;
            source.start();
        } else {
            // Use speech synthesis as fallback
            const utterance = new SpeechSynthesisUtterance(item);
            utterance.lang = 'id-ID';
            utterance.onend = playNext;
            window.speechSynthesis.speak(utterance);
        }
    }

    playNext();
}

// Function to process the audio queue
function processAudioQueue() {
    if (audioQueue.length > 0 && !isPlaying) {
        const nextSequence = audioQueue.shift();
        playAudioSequence(nextSequence);
    }
}

// Function to convert number to Indonesian spoken form
function numberToIndonesianAudio(number) {
    const num = parseInt(number, 10);
    if (isNaN(num)) return [];

    if (num === 0) return ['0'];
    if (num === 10) return ['sepuluh'];
    if (num === 11) return ['sebelas'];
    if (num === 100) return ['seratus'];
    if (num === 1000) return ['seribu'];

    const digits = num.toString().split('').map(d => d);
    const result = [];

    if (digits.length === 1) {
        // 1-9
        result.push(digits[0]);
    } else if (digits.length === 2) {
        // 10-99
        if (num === 10) {
            result.push('sepuluh');
        } else if (num === 11) {
            result.push('sebelas');
        } else if (num >= 12 && num <= 19) {
            result.push(digits[1], 'belas');
        } else {
            result.push(digits[0], 'puluh');
            if (digits[1] !== '0') {
                result.push(digits[1]);
            }
        }
    } else if (digits.length === 3) {
        // 100-999
        if (digits[0] === '1') {
            result.push('seratus');
        } else {
            result.push(digits[0], 'ratus');
        }

        // Handle the last two digits
        const lastTwo = parseInt(digits[1] + digits[2], 10);
        if (lastTwo > 0) {
            if (lastTwo === 10) {
                result.push('sepuluh');
            } else if (lastTwo === 11) {
                result.push('sebelas');
            } else if (lastTwo >= 12 && lastTwo <= 19) {
                result.push(digits[2], 'belas');
            } else {
                if (digits[1] !== '0') {
                    result.push(digits[1], 'puluh');
                }
                if (digits[2] !== '0') {
                    result.push(digits[2]);
                }
            }
        }
    } else if (digits.length === 4) {
        // 1000-9999
        if (digits[0] === '1') {
            result.push('seribu');
        } else {
            result.push(digits[0], 'ribu');
        }

        // Handle hundreds
        if (digits[1] !== '0') {
            if (digits[1] === '1') {
                result.push('seratus');
            } else {
                result.push(digits[1], 'ratus');
            }
        }

        // Handle the last two digits
        const lastTwo = parseInt(digits[2] + digits[3], 10);
        if (lastTwo > 0) {
            if (lastTwo === 10) {
                result.push('sepuluh');
            } else if (lastTwo === 11) {
                result.push('sebelas');
            } else if (lastTwo >= 12 && lastTwo <= 19) {
                result.push(digits[3], 'belas');
            } else {
                if (digits[2] !== '0') {
                    result.push(digits[2], 'puluh');
                }
                if (digits[3] !== '0') {
                    result.push(digits[3]);
                }
            }
        }
    }

    return result;
}

// Function to announce a queue number
function announceQueueNumber(queueNumber, counterType, counterNumber) {
    // Initialize audio
    initAudio();

    // Parse queue number (remove any non-numeric prefix)
    const numericPart = queueNumber.replace(/[^0-9]/g, '');

    // Create the announcement sequence
    const sequence = ['bell', 'nomor-antrian'];

    // Add the queue number
    const numberParts = numberToIndonesianAudio(numericPart);
    sequence.push(...numberParts);

    // Add the counter information
    sequence.push('silahkan-ke');

    // Add counter type (teller, cs, kredit)
    if (counterType === 'teller') {
        sequence.push('teller');
    } else if (counterType === 'cs') {
        sequence.push('customer-service');
    } else if (counterType === 'kredit') {
        sequence.push('kredit');
    } else {
        sequence.push('loket');
    }

    // Add counter number
    const counterParts = numberToIndonesianAudio(counterNumber);
    sequence.push(...counterParts);

    // Play the announcement
    if (isLoaded) {
        playAudioSequence(sequence);
    } else {
        // Queue the announcement for when loading is complete
        audioQueue.push(sequence);
    }
}

// Export functions
window.voiceSystem = {
    initAudio: initAudio,
    announceQueueNumber: announceQueueNumber
};

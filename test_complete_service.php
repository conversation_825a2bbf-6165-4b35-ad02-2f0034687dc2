<?php
// Test complete service functionality
require_once 'includes/functions.php';

echo "<h1>Test Complete Service</h1>";

if (isset($_GET['id'])) {
    $queue_id = $_GET['id'];
    
    echo "<h2>Completing Service for Queue ID: $queue_id</h2>";
    
    // Get queue details
    $sql = "SELECT a.*, l.nama_loket FROM antrian a LEFT JOIN loket l ON a.loket_id = l.id WHERE a.id = $queue_id";
    $result = query($sql);
    
    if ($result && num_rows($result) > 0) {
        $queue = fetch_assoc($result);
        
        echo "<p><strong>Queue Details:</strong></p>";
        echo "<ul>";
        echo "<li>Nomor Antrian: {$queue['nomor_antrian']}</li>";
        echo "<li>Nama: {$queue['nama']}</li>";
        echo "<li>Layanan: {$queue['jenis_layanan']}</li>";
        echo "<li>Status: {$queue['status']}</li>";
        echo "<li>Loket: {$queue['nama_loket']}</li>";
        echo "</ul>";
        
        if ($queue['status'] == 'dipanggil') {
            // Update queue status to 'selesai'
            $sql = "UPDATE antrian SET status = 'selesai', waktu_selesai = NOW() WHERE id = $queue_id";
            
            if (query($sql)) {
                echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
                echo "<h3>✅ Service Completed Successfully!</h3>";
                echo "<p><strong>Nomor Antrian {$queue['nomor_antrian']}</strong> telah selesai dilayani di <strong>{$queue['nama_loket']}</strong></p>";
                echo "<p>Customer: {$queue['nama']}</p>";
                echo "<p>Service: " . ucfirst($queue['jenis_layanan']) . "</p>";
                echo "<p>Completed at: " . date('H:i:s') . "</p>";
                echo "</div>";
                
                // Show next queue for this service
                $service = $queue['jenis_layanan'];
                $sql = "SELECT * FROM antrian WHERE jenis_layanan = '$service' AND status = 'menunggu' AND tanggal = CURDATE() ORDER BY id LIMIT 1";
                $result = query($sql);
                
                if ($result && num_rows($result) > 0) {
                    $next_queue = fetch_assoc($result);
                    echo "<div style='background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
                    echo "<h4>📋 Next Queue Available:</h4>";
                    echo "<p><strong>{$next_queue['nomor_antrian']}</strong> - {$next_queue['nama']} (Service: " . ucfirst($service) . ")</p>";
                    echo "<a href='test_call_queue.php?id={$next_queue['id']}&service=$service' style='display: inline-block; padding: 8px 15px; background-color: #007bff; color: white; text-decoration: none; border-radius: 3px;'>Call Next Queue</a>";
                    echo "</div>";
                } else {
                    echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
                    echo "<h4>ℹ️ No More Queues</h4>";
                    echo "<p>No more waiting queues for " . ucfirst($service) . " service</p>";
                    echo "</div>";
                }
            } else {
                echo "<p style='color: red;'>❌ Failed to complete service</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ Queue is not in called status. Current status: {$queue['status']}</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Queue not found</p>";
    }
} else {
    echo "<h2>Complete Service for Called Queues</h2>";
    
    // Show all currently called queues
    $sql = "SELECT a.*, l.nama_loket FROM antrian a LEFT JOIN loket l ON a.loket_id = l.id WHERE a.status = 'dipanggil' AND a.tanggal = CURDATE() ORDER BY a.jenis_layanan, a.waktu_dipanggil";
    $result = query($sql);
    
    if ($result && num_rows($result) > 0) {
        echo "<p>The following queues are currently being served:</p>";
        
        while ($queue = fetch_assoc($result)) {
            echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; background-color: #f8f9fa;'>";
            echo "<h4>{$queue['nomor_antrian']} - {$queue['nama']}</h4>";
            echo "<p><strong>Service:</strong> " . ucfirst($queue['jenis_layanan']) . "</p>";
            echo "<p><strong>Counter:</strong> {$queue['nama_loket']}</p>";
            echo "<p><strong>Called at:</strong> " . date('H:i', strtotime($queue['waktu_dipanggil'])) . "</p>";
            echo "<a href='test_complete_service.php?id={$queue['id']}' style='display: inline-block; padding: 8px 15px; background-color: #28a745; color: white; text-decoration: none; border-radius: 3px;'>Complete Service</a>";
            echo "</div>";
        }
    } else {
        echo "<p>No queues are currently being served</p>";
    }
    
    // Show completed queues today
    echo "<h3>Completed Services Today</h3>";
    $sql = "SELECT a.*, l.nama_loket FROM antrian a LEFT JOIN loket l ON a.loket_id = l.id WHERE a.status = 'selesai' AND a.tanggal = CURDATE() ORDER BY a.waktu_selesai DESC LIMIT 10";
    $result = query($sql);
    
    if ($result && num_rows($result) > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin-bottom: 20px;'>";
        echo "<tr style='background-color: #f0f0f0;'><th>Nomor Antrian</th><th>Nama</th><th>Layanan</th><th>Loket</th><th>Completed At</th></tr>";
        while ($row = fetch_assoc($result)) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . $row['nomor_antrian'] . "</td>";
            echo "<td style='padding: 8px;'>" . $row['nama'] . "</td>";
            echo "<td style='padding: 8px;'>" . ucfirst($row['jenis_layanan']) . "</td>";
            echo "<td style='padding: 8px;'>" . $row['nama_loket'] . "</td>";
            echo "<td style='padding: 8px;'>" . ($row['waktu_selesai'] ? date('H:i', strtotime($row['waktu_selesai'])) : '-') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No completed services today</p>";
    }
}

echo "<br><br>";
echo "<a href='test_complete_system.php' style='display: inline-block; padding: 10px 15px; background-color: #6c757d; color: white; text-decoration: none; border-radius: 3px;'>Back to System Test</a> ";
echo "<a href='test_call_queue.php' style='display: inline-block; padding: 10px 15px; background-color: #007bff; color: white; text-decoration: none; border-radius: 3px;'>Test Call Queue</a>";
?>

<?php
// Reports page for administrators
$page_title = 'Laporan Sistem An<PERSON>an';
require_once '../includes/functions.php';

// Check if user is logged in and is an admin
if (!is_logged_in() || !is_admin()) {
    redirect('../login.php');
}

// Initialize variables
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-7 days'));
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');
$report_type = isset($_GET['report_type']) ? $_GET['report_type'] : 'daily';

// Connect to database
$conn = db_connect();

// Function to get daily queue statistics
function get_daily_stats($conn, $start_date, $end_date) {
    $stats = [];
    
    $sql = "SELECT 
                DATE(q.date) as date,
                COUNT(*) as total_queues,
                SUM(CASE WHEN q.status = 'completed' THEN 1 ELSE 0 END) as completed,
                SUM(CASE WHEN q.status = 'skipped' THEN 1 ELSE 0 END) as skipped,
                SUM(CASE WHEN q.status = 'waiting' THEN 1 ELSE 0 END) as waiting,
                AVG(TIMESTAMPDIFF(MINUTE, q.created_at, q.called_at)) as avg_wait_time,
                AVG(TIMESTAMPDIFF(MINUTE, q.called_at, q.completed_at)) as avg_service_time
            FROM queue q
            WHERE q.date BETWEEN ? AND ?
            GROUP BY DATE(q.date)
            ORDER BY DATE(q.date)";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ss", $start_date, $end_date);
    $stmt->execute();
    $result = $stmt->get_result();
    
    while ($row = $result->fetch_assoc()) {
        $stats[] = $row;
    }
    
    $stmt->close();
    return $stats;
}

// Function to get service statistics
function get_service_stats($conn, $start_date, $end_date) {
    $stats = [];
    
    $sql = "SELECT 
                s.name as service_name,
                COUNT(*) as total_queues,
                SUM(CASE WHEN q.status = 'completed' THEN 1 ELSE 0 END) as completed,
                SUM(CASE WHEN q.status = 'skipped' THEN 1 ELSE 0 END) as skipped,
                AVG(TIMESTAMPDIFF(MINUTE, q.created_at, q.called_at)) as avg_wait_time,
                AVG(TIMESTAMPDIFF(MINUTE, q.called_at, q.completed_at)) as avg_service_time
            FROM queue q
            JOIN services s ON q.service_id = s.id
            WHERE q.date BETWEEN ? AND ?
            GROUP BY q.service_id
            ORDER BY total_queues DESC";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ss", $start_date, $end_date);
    $stmt->execute();
    $result = $stmt->get_result();
    
    while ($row = $result->fetch_assoc()) {
        $stats[] = $row;
    }
    
    $stmt->close();
    return $stats;
}

// Function to get counter statistics
function get_counter_stats($conn, $start_date, $end_date) {
    $stats = [];
    
    $sql = "SELECT 
                c.name as counter_name,
                COUNT(*) as total_served,
                AVG(TIMESTAMPDIFF(MINUTE, q.called_at, q.completed_at)) as avg_service_time,
                MAX(TIMESTAMPDIFF(MINUTE, q.called_at, q.completed_at)) as max_service_time
            FROM queue q
            JOIN counters c ON q.counter_id = c.id
            WHERE q.date BETWEEN ? AND ? AND q.status = 'completed'
            GROUP BY q.counter_id
            ORDER BY total_served DESC";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ss", $start_date, $end_date);
    $stmt->execute();
    $result = $stmt->get_result();
    
    while ($row = $result->fetch_assoc()) {
        $stats[] = $row;
    }
    
    $stmt->close();
    return $stats;
}

// Function to get staff statistics
function get_staff_stats($conn, $start_date, $end_date) {
    $stats = [];
    
    $sql = "SELECT 
                u.name as staff_name,
                COUNT(*) as total_served,
                AVG(TIMESTAMPDIFF(MINUTE, q.called_at, q.completed_at)) as avg_service_time
            FROM queue q
            JOIN users u ON q.staff_id = u.id
            WHERE q.date BETWEEN ? AND ? AND q.status = 'completed'
            GROUP BY q.staff_id
            ORDER BY total_served DESC";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ss", $start_date, $end_date);
    $stmt->execute();
    $result = $stmt->get_result();
    
    while ($row = $result->fetch_assoc()) {
        $stats[] = $row;
    }
    
    $stmt->close();
    return $stats;
}

// Get statistics based on report type
$stats = [];
switch ($report_type) {
    case 'daily':
        $stats = get_daily_stats($conn, $start_date, $end_date);
        break;
    case 'service':
        $stats = get_service_stats($conn, $start_date, $end_date);
        break;
    case 'counter':
        $stats = get_counter_stats($conn, $start_date, $end_date);
        break;
    case 'staff':
        $stats = get_staff_stats($conn, $start_date, $end_date);
        break;
}

$conn->close();
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Bank BJB</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
</head>
<body>
    <div class="d-flex" id="wrapper">
        <!-- Sidebar -->
        <?php include '../includes/admin_sidebar.php'; ?>

        <!-- Page Content -->
        <div id="page-content-wrapper">
            <!-- Navbar -->
            <?php include '../includes/admin_navbar.php'; ?>

            <!-- Content -->
            <div class="container-fluid px-4">
                <div class="row">
                    <div class="col-12">
                        <div class="card shadow-sm mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0"><i class="fas fa-chart-bar mr-2"></i>Laporan Sistem Antrian</h5>
                            </div>
                            <div class="card-body">
                                <form method="GET" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" class="mb-4">
                                    <div class="form-row">
                                        <div class="col-md-3 mb-3">
                                            <label for="start_date">Tanggal Mulai</label>
                                            <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $start_date; ?>">
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <label for="end_date">Tanggal Akhir</label>
                                            <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $end_date; ?>">
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <label for="report_type">Jenis Laporan</label>
                                            <select class="form-control" id="report_type" name="report_type">
                                                <option value="daily" <?php if ($report_type === 'daily') echo 'selected'; ?>>Harian</option>
                                                <option value="service" <?php if ($report_type === 'service') echo 'selected'; ?>>Layanan</option>
                                                <option value="counter" <?php if ($report_type === 'counter') echo 'selected'; ?>>Counter</option>
                                                <option value="staff" <?php if ($report_type === 'staff') echo 'selected'; ?>>Petugas</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3 mb-3 d-flex align-items-end">
                                            <button type="submit" class="btn btn-primary"><i class="fas fa-filter mr-2"></i>Filter</button>
                                            <a href="export_report.php?start_date=<?php echo $start_date; ?>&end_date=<?php echo $end_date; ?>&report_type=<?php echo $report_type; ?>" class="btn btn-success ml-2"><i class="fas fa-file-excel mr-2"></i>Export</a>
                                        </div>
                                    </div>
                                </form>

                                <div class="row mb-4">
                                    <div class="col-md-12">
                                        <div class="chart-container" style="position: relative; height:300px;">
                                            <canvas id="reportChart"></canvas>
                                        </div>
                                    </div>
                                </div>

                                <div class="table-responsive">
                                    <?php if ($report_type === 'daily'): ?>
                                    <table class="table table-bordered table-hover" id="reportTable">
                                        <thead class="thead-light">
                                            <tr>
                                                <th>Tanggal</th>
                                                <th>Total Antrian</th>
                                                <th>Selesai</th>
                                                <th>Terlewat</th>
                                                <th>Menunggu</th>
                                                <th>Rata-rata Waktu Tunggu (menit)</th>
                                                <th>Rata-rata Waktu Layanan (menit)</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($stats as $stat): ?>
                                            <tr>
                                                <td><?php echo date('d M Y', strtotime($stat['date'])); ?></td>
                                                <td><?php echo $stat['total_queues']; ?></td>
                                                <td><?php echo $stat['completed']; ?></td>
                                                <td><?php echo $stat['skipped']; ?></td>
                                                <td><?php echo $stat['waiting']; ?></td>
                                                <td><?php echo round($stat['avg_wait_time'], 1); ?></td>
                                                <td><?php echo round($stat['avg_service_time'], 1); ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                    <?php elseif ($report_type === 'service'): ?>
                                    <table class="table table-bordered table-hover" id="reportTable">
                                        <thead class="thead-light">
                                            <tr>
                                                <th>Layanan</th>
                                                <th>Total Antrian</th>
                                                <th>Selesai</th>
                                                <th>Terlewat</th>
                                                <th>Rata-rata Waktu Tunggu (menit)</th>
                                                <th>Rata-rata Waktu Layanan (menit)</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($stats as $stat): ?>
                                            <tr>
                                                <td><?php echo $stat['service_name']; ?></td>
                                                <td><?php echo $stat['total_queues']; ?></td>
                                                <td><?php echo $stat['completed']; ?></td>
                                                <td><?php echo $stat['skipped']; ?></td>
                                                <td><?php echo round($stat['avg_wait_time'], 1); ?></td>
                                                <td><?php echo round($stat['avg_service_time'], 1); ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                    <?php elseif ($report_type === 'counter'): ?>
                                    <table class="table table-bordered table-hover" id="reportTable">
                                        <thead class="thead-light">
                                            <tr>
                                                <th>Counter</th>
                                                <th>Total Dilayani</th>
                                                <th>Rata-rata Waktu Layanan (menit)</th>
                                                <th>Waktu Layanan Terlama (menit)</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($stats as $stat): ?>
                                            <tr>
                                                <td><?php echo $stat['counter_name']; ?></td>
                                                <td><?php echo $stat['total_served']; ?></td>
                                                <td><?php echo round($stat['avg_service_time'], 1); ?></td>
                                                <td><?php echo round($stat['max_service_time'], 1); ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                    <?php elseif ($report_type === 'staff'): ?>
                                    <table class="table table-bordered table-hover" id="reportTable">
                                        <thead class="thead-light">
                                            <tr>
                                                <th>Petugas</th>
                                                <th>Total Dilayani</th>
                                                <th>Rata-rata Waktu Layanan (menit)</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($stats as $stat): ?>
                                            <tr>
                                                <td><?php echo $stat['staff_name']; ?></td>
                                                <td><?php echo $stat['total_served']; ?></td>
                                                <td><?php echo round($stat['avg_service_time'], 1); ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap4.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Initialize DataTable
            $('#reportTable').DataTable({
                "responsive": true,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "language": {
                    "search": "Cari:",
                    "lengthMenu": "Tampilkan _MENU_ data per halaman",
                    "zeroRecords": "Tidak ada data yang ditemukan",
                    "info": "Menampilkan halaman _PAGE_ dari _PAGES_",
                    "infoEmpty": "Tidak ada data yang tersedia",
                    "infoFiltered": "(difilter dari _MAX_ total data)",
                    "paginate": {
                        "first": "Pertama",
                        "last": "Terakhir",
                        "next": "Selanjutnya",
                        "previous": "Sebelumnya"
                    }
                }
            });
            
            // Initialize Chart
            var ctx = document.getElementById('reportChart').getContext('2d');
            
            <?php if ($report_type === 'daily'): ?>
            var labels = <?php echo json_encode(array_map(function($stat) { return date('d M', strtotime($stat['date'])); }, $stats)); ?>;
            var completed = <?php echo json_encode(array_map(function($stat) { return $stat['completed']; }, $stats)); ?>;
            var skipped = <?php echo json_encode(array_map(function($stat) { return $stat['skipped']; }, $stats)); ?>;
            var waiting = <?php echo json_encode(array_map(function($stat) { return $stat['waiting']; }, $stats)); ?>;
            
            var myChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: 'Selesai',
                            data: completed,
                            backgroundColor: 'rgba(40, 167, 69, 0.7)',
                            borderColor: 'rgba(40, 167, 69, 1)',
                            borderWidth: 1
                        },
                        {
                            label: 'Terlewat',
                            data: skipped,
                            backgroundColor: 'rgba(220, 53, 69, 0.7)',
                            borderColor: 'rgba(220, 53, 69, 1)',
                            borderWidth: 1
                        },
                        {
                            label: 'Menunggu',
                            data: waiting,
                            backgroundColor: 'rgba(255, 193, 7, 0.7)',
                            borderColor: 'rgba(255, 193, 7, 1)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Statistik Antrian Harian'
                        }
                    }
                }
            });
            <?php elseif ($report_type === 'service'): ?>
            var labels = <?php echo json_encode(array_map(function($stat) { return $stat['service_name']; }, $stats)); ?>;
            var total = <?php echo json_encode(array_map(function($stat) { return $stat['total_queues']; }, $stats)); ?>;
            
            var myChart = new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: labels,
                    datasets: [{
                        data: total,
                        backgroundColor: [
                            'rgba(40, 167, 69, 0.7)',
                            'rgba(0, 123, 255, 0.7)',
                            'rgba(255, 193, 7, 0.7)',
                            'rgba(23, 162, 184, 0.7)',
                            'rgba(111, 66, 193, 0.7)',
                            'rgba(220, 53, 69, 0.7)'
                        ],
                        borderColor: [
                            'rgba(40, 167, 69, 1)',
                            'rgba(0, 123, 255, 1)',
                            'rgba(255, 193, 7, 1)',
                            'rgba(23, 162, 184, 1)',
                            'rgba(111, 66, 193, 1)',
                            'rgba(220, 53, 69, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Distribusi Antrian per Layanan'
                        }
                    }
                }
            });
            <?php elseif ($report_type === 'counter' || $report_type === 'staff'): ?>
            var labels = <?php echo json_encode(array_map(function($stat) { return $report_type === 'counter' ? $stat['counter_name'] : $stat['staff_name']; }, $stats)); ?>;
            var total = <?php echo json_encode(array_map(function($stat) { return $stat['total_served']; }, $stats)); ?>;
            
            var myChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Total Dilayani',
                        data: total,
                        backgroundColor: 'rgba(0, 123, 255, 0.7)',
                        borderColor: 'rgba(0, 123, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: <?php echo json_encode('Kinerja ' . ($report_type === 'counter' ? 'Counter' : 'Petugas')); ?>
                        }
                    }
                }
            });
            <?php endif; ?>
        });
    </script>
</body>
</html>

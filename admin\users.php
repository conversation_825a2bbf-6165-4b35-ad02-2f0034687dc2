<?php
// User management page
$page_title = 'Manajemen Pengguna';
$active_menu = 'users';
$is_admin = true;
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!is_logged_in() || !is_admin()) {
    redirect('../login.php');
}

// Process add user
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_user'])) {
    $nama_lengkap = sanitize($_POST['nama_lengkap']);
    $username = sanitize($_POST['username']);
    $password = sanitize($_POST['password']);
    $role = sanitize($_POST['role']);
    $email = sanitize($_POST['email']);
    $no_hp = sanitize($_POST['no_hp']);
    
    // Validate input
    $errors = [];
    
    if (empty($nama_lengkap)) {
        $errors[] = 'Nama lengkap tidak boleh kosong.';
    }
    
    if (empty($username)) {
        $errors[] = 'Username tidak boleh kosong.';
    } else {
        // Check if username already exists
        $sql = "SELECT id FROM users WHERE username = '$username'";
        $result = query($sql);
        if (num_rows($result) > 0) {
            $errors[] = 'Username sudah digunakan.';
        }
    }
    
    if (empty($password)) {
        $errors[] = 'Password tidak boleh kosong.';
    } elseif (strlen($password) < 6) {
        $errors[] = 'Password minimal 6 karakter.';
    }
    
    if (empty($role)) {
        $errors[] = 'Role tidak boleh kosong.';
    }
    
    // If no errors, add user
    if (empty($errors)) {
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
        
        $sql = "INSERT INTO users (nama_lengkap, username, password, role, email, no_hp, created_at) 
                VALUES ('$nama_lengkap', '$username', '$hashed_password', '$role', '$email', '$no_hp', NOW())";
        
        if (query($sql)) {
            $_SESSION['success_message'] = 'Pengguna berhasil ditambahkan.';
            redirect('users.php');
        } else {
            $_SESSION['error_message'] = 'Gagal menambahkan pengguna.';
        }
    } else {
        $_SESSION['error_message'] = implode('<br>', $errors);
    }
}

// Process edit user
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['edit_user'])) {
    $user_id = (int)$_POST['user_id'];
    $nama_lengkap = sanitize($_POST['nama_lengkap']);
    $username = sanitize($_POST['username']);
    $password = sanitize($_POST['password']);
    $role = sanitize($_POST['role']);
    $email = sanitize($_POST['email']);
    $no_hp = sanitize($_POST['no_hp']);
    
    // Validate input
    $errors = [];
    
    if (empty($nama_lengkap)) {
        $errors[] = 'Nama lengkap tidak boleh kosong.';
    }
    
    if (empty($username)) {
        $errors[] = 'Username tidak boleh kosong.';
    } else {
        // Check if username already exists (except for this user)
        $sql = "SELECT id FROM users WHERE username = '$username' AND id != $user_id";
        $result = query($sql);
        if (num_rows($result) > 0) {
            $errors[] = 'Username sudah digunakan.';
        }
    }
    
    if (!empty($password) && strlen($password) < 6) {
        $errors[] = 'Password minimal 6 karakter.';
    }
    
    if (empty($role)) {
        $errors[] = 'Role tidak boleh kosong.';
    }
    
    // If no errors, update user
    if (empty($errors)) {
        if (!empty($password)) {
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            $sql = "UPDATE users SET nama_lengkap = '$nama_lengkap', username = '$username', password = '$hashed_password', 
                    role = '$role', email = '$email', no_hp = '$no_hp' WHERE id = $user_id";
        } else {
            $sql = "UPDATE users SET nama_lengkap = '$nama_lengkap', username = '$username', 
                    role = '$role', email = '$email', no_hp = '$no_hp' WHERE id = $user_id";
        }
        
        if (query($sql)) {
            $_SESSION['success_message'] = 'Pengguna berhasil diperbarui.';
            redirect('users.php');
        } else {
            $_SESSION['error_message'] = 'Gagal memperbarui pengguna.';
        }
    } else {
        $_SESSION['error_message'] = implode('<br>', $errors);
    }
}

// Process delete user
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['delete_user'])) {
    $user_id = (int)$_POST['user_id'];
    
    // Check if user is not the current user
    if ($user_id == $_SESSION['user_id']) {
        $_SESSION['error_message'] = 'Anda tidak dapat menghapus akun Anda sendiri.';
        redirect('users.php');
    }
    
    // Delete user
    $sql = "DELETE FROM users WHERE id = $user_id";
    
    if (query($sql)) {
        $_SESSION['success_message'] = 'Pengguna berhasil dihapus.';
        redirect('users.php');
    } else {
        $_SESSION['error_message'] = 'Gagal menghapus pengguna.';
    }
}

// Get all users
$sql = "SELECT * FROM users ORDER BY id";
$result = query($sql);
$users = fetch_all($result);

// Include header
include_once '../includes/header.php';
?>

<div class="row">
    <div class="col-md-12">
        <h1 class="mb-4">Manajemen Pengguna</h1>
        
        <?php include_once '../includes/alerts.php'; ?>
        
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-user-plus"></i> Tambah Pengguna Baru
            </div>
            <div class="card-body">
                <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addUserModal">
                    <i class="fas fa-plus"></i> Tambah Pengguna
                </button>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-users"></i> Daftar Pengguna
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover datatable">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Nama Lengkap</th>
                                <th>Username</th>
                                <th>Role</th>
                                <th>Email</th>
                                <th>No. HP</th>
                                <th>Tanggal Dibuat</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if(count($users) > 0): ?>
                            <?php foreach($users as $user): ?>
                            <tr>
                                <td><?php echo $user['id']; ?></td>
                                <td><?php echo $user['nama_lengkap']; ?></td>
                                <td><?php echo $user['username']; ?></td>
                                <td>
                                    <?php if($user['role'] == 'admin'): ?>
                                    <span class="badge badge-danger">Admin</span>
                                    <?php elseif($user['role'] == 'staff'): ?>
                                    <span class="badge badge-primary">Staff</span>
                                    <?php else: ?>
                                    <span class="badge badge-secondary"><?php echo ucfirst($user['role']); ?></span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo $user['email']; ?></td>
                                <td><?php echo $user['no_hp']; ?></td>
                                <td><?php echo date('d/m/Y H:i', strtotime($user['created_at'])); ?></td>
                                <td>
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-sm btn-info dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            Aksi
                                        </button>
                                        <div class="dropdown-menu">
                                            <a class="dropdown-item" href="#" data-toggle="modal" data-target="#editUserModal<?php echo $user['id']; ?>">
                                                <i class="fas fa-edit"></i> Edit
                                            </a>
                                            <?php if($user['id'] != $_SESSION['user_id']): ?>
                                            <a class="dropdown-item text-danger" href="#" data-toggle="modal" data-target="#deleteUserModal<?php echo $user['id']; ?>">
                                                <i class="fas fa-trash"></i> Hapus
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- Edit User Modal -->
                            <div class="modal fade" id="editUserModal<?php echo $user['id']; ?>" tabindex="-1" role="dialog" aria-labelledby="editUserModalLabel<?php echo $user['id']; ?>" aria-hidden="true">
                                <div class="modal-dialog" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="editUserModalLabel<?php echo $user['id']; ?>">Edit Pengguna</h5>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <form action="users.php" method="post">
                                            <div class="modal-body">
                                                <div class="form-group">
                                                    <label for="nama_lengkap">Nama Lengkap</label>
                                                    <input type="text" class="form-control" id="nama_lengkap" name="nama_lengkap" value="<?php echo $user['nama_lengkap']; ?>" required>
                                                </div>
                                                <div class="form-group">
                                                    <label for="username">Username</label>
                                                    <input type="text" class="form-control" id="username" name="username" value="<?php echo $user['username']; ?>" required>
                                                </div>
                                                <div class="form-group">
                                                    <label for="password">Password (Kosongkan jika tidak ingin mengubah)</label>
                                                    <input type="password" class="form-control" id="password" name="password">
                                                    <small class="form-text text-muted">Minimal 6 karakter.</small>
                                                </div>
                                                <div class="form-group">
                                                    <label for="role">Role</label>
                                                    <select class="form-control" id="role" name="role" required>
                                                        <option value="admin" <?php echo $user['role'] == 'admin' ? 'selected' : ''; ?>>Admin</option>
                                                        <option value="staff" <?php echo $user['role'] == 'staff' ? 'selected' : ''; ?>>Staff</option>
                                                    </select>
                                                </div>
                                                <div class="form-group">
                                                    <label for="email">Email</label>
                                                    <input type="email" class="form-control" id="email" name="email" value="<?php echo $user['email']; ?>">
                                                </div>
                                                <div class="form-group">
                                                    <label for="no_hp">No. HP</label>
                                                    <input type="text" class="form-control" id="no_hp" name="no_hp" value="<?php echo $user['no_hp']; ?>">
                                                </div>
                                                <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                                                <button type="submit" name="edit_user" class="btn btn-primary">Simpan</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Delete User Modal -->
                            <?php if($user['id'] != $_SESSION['user_id']): ?>
                            <div class="modal fade" id="deleteUserModal<?php echo $user['id']; ?>" tabindex="-1" role="dialog" aria-labelledby="deleteUserModalLabel<?php echo $user['id']; ?>" aria-hidden="true">
                                <div class="modal-dialog" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="deleteUserModalLabel<?php echo $user['id']; ?>">Hapus Pengguna</h5>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <form action="users.php" method="post">
                                            <div class="modal-body">
                                                <p>Apakah Anda yakin ingin menghapus pengguna <strong><?php echo $user['nama_lengkap']; ?></strong>?</p>
                                                <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                                                <button type="submit" name="delete_user" class="btn btn-danger">Hapus</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                            <?php endforeach; ?>
                            <?php else: ?>
                            <tr>
                                <td colspan="8" class="text-center">Tidak ada data pengguna.</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1" role="dialog" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addUserModalLabel">Tambah Pengguna Baru</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="users.php" method="post">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="nama_lengkap">Nama Lengkap</label>
                        <input type="text" class="form-control" id="nama_lengkap" name="nama_lengkap" required>
                    </div>
                    <div class="form-group">
                        <label for="username">Username</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                        <small class="form-text text-muted">Minimal 6 karakter.</small>
                    </div>
                    <div class="form-group">
                        <label for="role">Role</label>
                        <select class="form-control" id="role" name="role" required>
                            <option value="">Pilih Role</option>
                            <option value="admin">Admin</option>
                            <option value="staff">Staff</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="email">Email</label>
                        <input type="email" class="form-control" id="email" name="email">
                    </div>
                    <div class="form-group">
                        <label for="no_hp">No. HP</label>
                        <input type="text" class="form-control" id="no_hp" name="no_hp">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" name="add_user" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>

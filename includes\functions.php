<?php
/**
 * Core Functions
 *
 * Fungsi-fungsi utama untuk Sistem Manajemen Antrian Bank BJB
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include database configuration
require_once __DIR__ . '/../config/database.php';

/**
 * Function to check if user is logged in
 *
 * @return bool
 */
function is_logged_in() {
    return isset($_SESSION['user_id']);
}

/**
 * Function to check if user is admin
 *
 * @return bool
 */
function is_admin() {
    return isset($_SESSION['role']) && $_SESSION['role'] == 'admin';
}

/**
 * Function to check if user is staff
 *
 * @return bool
 */
function is_staff() {
    return isset($_SESSION['role']) && $_SESSION['role'] == 'staff';
}

/**
 * Function to redirect to a specific page
 *
 * @param string $page
 * @return void
 */
function redirect($page) {
    header("Location: $page");
    exit;
}

/**
 * Function to display error message
 *
 * @param string $message
 * @return string
 */
function error_message($message) {
    return '<div class="alert alert-danger">' . $message . '</div>';
}

/**
 * Function to display success message
 *
 * @param string $message
 * @return string
 */
function success_message($message) {
    return '<div class="alert alert-success">' . $message . '</div>';
}

/**
 * Function to get setting value
 *
 * @param string $name
 * @return string
 */
function get_setting($name) {
    $sql = "SELECT nilai FROM pengaturan WHERE nama_pengaturan = '$name'";
    $result = query($sql);

    if (num_rows($result) > 0) {
        $row = fetch_assoc($result);
        return $row['nilai'];
    }

    return '';
}

/**
 * Function to generate queue number
 *
 * @param int $poli_id
 * @param string $date
 * @return string
 */
function generate_queue_number($poli_id, $date = null) {
    if ($date === null) {
        $date = date('Y-m-d');
    }

    // Get poli code
    $sql = "SELECT id FROM poli WHERE id = $poli_id";
    $result = query($sql);
    $poli = fetch_assoc($result);

    if (!$poli) {
        return false;
    }

    // Get last queue number for this poli and date
    $sql = "SELECT MAX(CAST(SUBSTRING(nomor_antrian, 4) AS UNSIGNED)) as last_number
            FROM antrian
            WHERE poli_id = $poli_id AND tanggal = '$date'";
    $result = query($sql);
    $row = fetch_assoc($result);

    $last_number = $row['last_number'] ? $row['last_number'] : 0;
    $next_number = $last_number + 1;

    // Format: XXX-NNN (XXX = poli code, NNN = sequence number)
    return sprintf("%03d", $poli_id) . '-' . sprintf("%03d", $next_number);
}

/**
 * Function to calculate estimated waiting time
 *
 * @param int $poli_id
 * @param string $date
 * @return int
 */
function calculate_estimated_time($poli_id, $date = null) {
    if ($date === null) {
        $date = date('Y-m-d');
    }

    // Get poli average service time
    $sql = "SELECT estimasi_waktu FROM poli WHERE id = $poli_id";
    $result = query($sql);
    $poli = fetch_assoc($result);

    if (!$poli) {
        return 0;
    }

    $avg_service_time = $poli['estimasi_waktu'];

    // Count waiting queue
    $sql = "SELECT COUNT(*) as waiting_count
            FROM antrian
            WHERE poli_id = $poli_id
            AND tanggal = '$date'
            AND status = 'menunggu'";
    $result = query($sql);
    $row = fetch_assoc($result);

    $waiting_count = $row['waiting_count'];

    // Calculate estimated time
    return $waiting_count * $avg_service_time;
}

/**
 * Function to check if IP is blocked
 *
 * @param string $ip
 * @return bool
 */
function is_ip_blocked($ip) {
    $sql = "SELECT id FROM blocked_ips WHERE ip_address = '$ip'";
    $result = query($sql);

    return num_rows($result) > 0;
}

/**
 * Function to block an IP
 *
 * @param string $ip
 * @param string $reason
 * @return bool
 */
function block_ip($ip, $reason = '') {
    $sql = "INSERT INTO blocked_ips (ip_address, alasan) VALUES ('$ip', '$reason')";
    return query($sql);
}

/**
 * Function to verify customer ID
 *
 * @param int $nasabah_id
 * @param string $status
 * @param string $reason
 * @return bool
 */
function verify_customer($nasabah_id, $status = 'terverifikasi', $reason = '') {
    $sql = "UPDATE nasabah SET
            status_verifikasi = '$status',
            alasan_penolakan = '$reason',
            updated_at = NOW()
            WHERE id = $nasabah_id";
    return query($sql);
}

/**
 * Function to get current date in Indonesian format
 *
 * @return string
 */
function tanggal_indonesia() {
    $hari = array('Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu');
    $bulan = array('Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni', 'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember');

    $day = $hari[date('w')];
    $date = date('j');
    $month = $bulan[date('n')-1];
    $year = date('Y');

    return "$day, $date $month $year";
}

/**
 * Function to format date in Indonesian format
 *
 * @param string $date
 * @return string
 */
function format_date($date) {
    if (empty($date)) {
        return '-';
    }

    $hari = array('Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu');
    $bulan = array('Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni', 'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember');

    $timestamp = strtotime($date);
    $day = $hari[date('w', $timestamp)];
    $date_num = date('j', $timestamp);
    $month = $bulan[date('n', $timestamp)-1];
    $year = date('Y', $timestamp);

    return "$day, $date_num $month $year";
}

/**
 * Function to format datetime in Indonesian format
 *
 * @param string $datetime
 * @return string
 */
function format_datetime($datetime) {
    if (empty($datetime)) {
        return '-';
    }

    $hari = array('Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu');
    $bulan = array('Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni', 'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember');

    $timestamp = strtotime($datetime);
    $day = $hari[date('w', $timestamp)];
    $date_num = date('j', $timestamp);
    $month = $bulan[date('n', $timestamp)-1];
    $year = date('Y', $timestamp);
    $time = date('H:i:s', $timestamp);

    return "$day, $date_num $month $year $time";
}

/**
 * Function to check if current time is within operational hours for booking
 * Modified to allow booking anytime, but restrict to future dates
 *
 * @return bool
 */
function is_operational_hours() {
    // Untuk booking, izinkan kapan saja
    return true;
}
// Function last_id() is defined in config/database.php

/**
 * Function to get nasabah by ID
 *
 * @param int $id
 * @return array|bool
 */
function get_nasabah_by_id($id) {
    global $conn;

    $stmt = $conn->prepare("SELECT * FROM nasabah WHERE id = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        return $result->fetch_assoc();
    }

    return false;
}

/**
 * Function to get nasabah by email
 *
 * @param string $email
 * @return array|bool
 */
function get_nasabah_by_email($email) {
    global $conn;

    $stmt = $conn->prepare("SELECT * FROM nasabah WHERE email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        return $result->fetch_assoc();
    }

    return false;
}

/**
 * Function to get booking by ID
 *
 * @param string $booking_id
 * @return array|bool
 */
function get_booking_by_id($booking_id) {
    global $conn;

    $stmt = $conn->prepare("SELECT * FROM bookings WHERE booking_id = ?");
    $stmt->bind_param("s", $booking_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        return $result->fetch_assoc();
    }

    return false;
}

/**
 * Function to get bookings by nasabah ID
 *
 * @param int $nasabah_id
 * @return array
 */
function get_bookings_by_nasabah_id($nasabah_id) {
    global $conn;

    $stmt = $conn->prepare("SELECT * FROM bookings WHERE nasabah_id = ? ORDER BY created_at DESC");
    $stmt->bind_param("i", $nasabah_id);
    $stmt->execute();
    $result = $stmt->get_result();

    $bookings = [];
    while ($row = $result->fetch_assoc()) {
        $bookings[] = $row;
    }

    return $bookings;
}

/**
 * Function to check if nasabah has pending booking
 *
 * @param int $nasabah_id
 * @return bool
 */
function has_pending_booking($nasabah_id) {
    global $conn;

    try {
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM bookings WHERE nasabah_id = ? AND (status = 'pending' OR status = 'confirmed') AND tanggal >= CURDATE()");

        if (!$stmt) {
            error_log("Error preparing statement in has_pending_booking: " . $conn->error);
            return false;
        }

    $stmt->bind_param("i", $nasabah_id);
        if (!$stmt->execute()) {
            error_log("Error executing query in has_pending_booking: " . $stmt->error);
            return false;
}

        $result = $stmt->get_result();
        $row = $result->fetch_assoc();

        return ($row['count'] > 0);
    } catch (Exception $e) {
        error_log("Error in has_pending_booking: " . $e->getMessage());
        return false;
    }
}
?>

<?php
// Staff queue history page
$page_title = 'Riwayat Antrian';
$active_menu = 'riwayat';
require_once '../includes/functions.php';

// Check if user is logged in and is staff
if (!is_logged_in() || !is_staff()) {
    redirect('../login.php');
}

// Get user ID
$user_id = $_SESSION['user_id'];

// Get date filter
$date_filter = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');

// Get status filter
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';

// Build query
$where_clause = "WHERE l.user_id = $user_id AND a.tanggal = '$date_filter'";

if ($status_filter) {
    $where_clause .= " AND a.status = '$status_filter'";
}

// Get queue history
$sql = "SELECT a.*, n.nama, p.nama_poli, l.nama_loket,
        TIMESTAMPDIFF(MINUTE, a.waktu_dipanggil, a.waktu_selesai) as durasi_layanan
        FROM antrian a
        LEFT JOIN nasabah n ON a.nasabah_id = n.id
        LEFT JOIN poli p ON a.poli_id = p.id
        LEFT JOIN loket l ON a.loket_id = l.id
        $where_clause
        ORDER BY a.waktu_dipanggil DESC";
$result = query($sql);
$queue_history = fetch_all($result);

// Get statistics
$sql = "SELECT COUNT(*) as total_antrian,
        SUM(CASE WHEN a.status = 'selesai' THEN 1 ELSE 0 END) as total_selesai,
        SUM(CASE WHEN a.status = 'dipanggil' THEN 1 ELSE 0 END) as total_dipanggil,
        SUM(CASE WHEN a.status = 'batal' THEN 1 ELSE 0 END) as total_batal,
        AVG(TIMESTAMPDIFF(MINUTE, a.waktu_dipanggil, a.waktu_selesai)) as rata_durasi
        FROM antrian a
        LEFT JOIN loket l ON a.loket_id = l.id
        WHERE l.user_id = $user_id AND a.tanggal = '$date_filter'";
$result = query($sql);
$stats = fetch_assoc($result);

// Include header
include_once 'includes/header.php';
echo '<style>
    /* Custom styles for Riwayat Antrian page */
    .icon-circle {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        margin-right: 15px;
        flex-shrink: 0;
    }

    .stats-card {
        border-radius: 8px;
        overflow: hidden;
        transition: all 0.3s;
        border: none;
        height: 100%;
    }

    .stats-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .stats-card .card-body {
        padding: 15px;
    }

    .stats-value {
        font-size: 2rem;
        font-weight: 700;
        margin: 0;
        line-height: 1.2;
    }

    .stats-label {
        text-transform: uppercase;
        font-size: 0.8rem;
        font-weight: 600;
        letter-spacing: 0.5px;
        margin-bottom: 3px;
        opacity: 0.9;
    }

    /* Card styles */

    .history-table th {
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.85rem;
        letter-spacing: 0.5px;
        padding: 15px 10px;
        background-color: #f8f9fa;
        border-top: none;
    }

    .history-table td {
        padding: 15px 10px;
        vertical-align: middle;
    }

    .badge {
        padding: 8px 12px;
        font-weight: 500;
        border-radius: 30px;
    }

    .page-title {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        color: #1a6a83;
        display: flex;
        align-items: center;
    }

    .page-title i {
        background-color: #1a6a83;
        color: white;
        width: 40px;
        height: 40px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        box-shadow: 0 4px 10px rgba(26, 106, 131, 0.2);
    }

    /* Color styles */

    .date-display {
        background-color: rgba(255, 255, 255, 0.2);
        padding: 5px 15px;
        border-radius: 30px;
        font-size: 0.9rem;
        font-weight: 500;
    }

    .btn {
        border-radius: 10px;
        padding: 8px 20px;
        font-weight: 500;
        transition: all 0.3s;
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .input-group {
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        border-radius: 10px;
        overflow: hidden;
    }

    .input-group-text {
        background-color: #f8f9fa;
        border: none;
        padding-left: 15px;
        padding-right: 15px;
    }

    .form-control {
        border: none;
        padding: 12px 15px;
        height: auto;
    }

    .form-control:focus {
        box-shadow: none;
        border-color: #1a6a83;
    }

    @media (max-width: 768px) {
        .stats-value {
            font-size: 1.8rem;
        }

        .icon-circle {
            width: 50px;
            height: 50px;
        }
    }
</style>';
?>

<div class="container-fluid px-4 py-3">
    <!-- Halaman ini tidak memerlukan header tambahan karena sudah ada di navbar -->

    <!-- Filter Card -->
    <div class="card shadow mb-4">
        <div class="card-header bg-primary text-white py-3">
            <div class="d-flex align-items-center">
                <i class="fas fa-filter mr-2"></i>
                <span class="font-weight-bold">Filter Data Riwayat</span>
            </div>
        </div>
        <div class="card-body p-3">
            <form method="get" action="" class="row">
                <div class="col-md-5 mb-2">
                    <label for="date" class="font-weight-bold mb-1">Tanggal:</label>
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text bg-light"><i class="fas fa-calendar-alt"></i></span>
                        </div>
                        <input type="date" class="form-control" id="date" name="date" value="<?php echo $date_filter; ?>">
                    </div>
                </div>
                <div class="col-md-5 mb-2">
                    <label for="status" class="font-weight-bold mb-1">Status:</label>
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text bg-light"><i class="fas fa-tag"></i></span>
                        </div>
                        <select class="form-control" id="status" name="status">
                            <option value="">Semua Status</option>
                            <option value="selesai" <?php echo $status_filter == 'selesai' ? 'selected' : ''; ?>>Selesai</option>
                            <option value="dipanggil" <?php echo $status_filter == 'dipanggil' ? 'selected' : ''; ?>>Sedang Dilayani</option>
                            <option value="batal" <?php echo $status_filter == 'batal' ? 'selected' : ''; ?>>Batal</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-2 mb-2 d-flex align-items-end">
                    <div class="d-flex">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search mr-1"></i> Filter
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stats-card bg-primary text-white shadow-sm">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center">
                        <div class="icon-circle bg-white text-primary mr-3">
                            <i class="fas fa-ticket-alt fa-2x"></i>
                        </div>
                        <div>
                            <div class="stats-label">TOTAL ANTRIAN</div>
                            <div class="stats-value"><?php echo $stats['total_antrian'] ?: 0; ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stats-card bg-success text-white shadow-sm">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center">
                        <div class="icon-circle bg-white text-success mr-3">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                        <div>
                            <div class="stats-label">SELESAI</div>
                            <div class="stats-value"><?php echo $stats['total_selesai'] ?: 0; ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stats-card bg-warning text-dark shadow-sm">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center">
                        <div class="icon-circle bg-white text-warning mr-3">
                            <i class="fas fa-user-clock fa-2x"></i>
                        </div>
                        <div>
                            <div class="stats-label">SEDANG DILAYANI</div>
                            <div class="stats-value"><?php echo $stats['total_dipanggil'] ?: 0; ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stats-card bg-danger text-white shadow-sm">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center">
                        <div class="icon-circle bg-white text-danger mr-3">
                            <i class="fas fa-times-circle fa-2x"></i>
                        </div>
                        <div>
                            <div class="stats-label">BATAL</div>
                            <div class="stats-value"><?php echo $stats['total_batal'] ?: 0; ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- History Table -->
    <div class="card shadow mb-4">
        <div class="card-header bg-dark text-white py-3">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <i class="fas fa-history mr-2"></i>
                    <span class="font-weight-bold">Daftar Riwayat Antrian</span>
                </div>
                <div>
                    <span class="date-display">
                        <i class="fas fa-calendar-day mr-1"></i> <?php echo tanggal_indonesia($date_filter); ?>
                    </span>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0 history-table">
                    <thead>
                        <tr>
                            <th>No. Antrian</th>
                            <th>Nama</th>
                            <th>Layanan</th>
                            <th>Loket</th>
                            <th>Waktu Panggil</th>
                            <th>Waktu Selesai</th>
                            <th>Durasi</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if(count($queue_history) > 0): ?>
                        <?php foreach($queue_history as $queue): ?>
                        <tr>
                            <td class="font-weight-bold"><?php echo $queue['nomor_antrian']; ?></td>
                            <td><?php echo $queue['nama']; ?></td>
                            <td><?php echo $queue['nama_poli']; ?></td>
                            <td><?php echo $queue['nama_loket']; ?></td>
                            <td>
                                <i class="fas fa-clock text-muted mr-1"></i>
                                <?php echo format_datetime($queue['waktu_dipanggil']); ?>
                            </td>
                            <td>
                                <?php if($queue['waktu_selesai']): ?>
                                <i class="fas fa-check-circle text-success mr-1"></i>
                                <?php echo format_datetime($queue['waktu_selesai']); ?>
                                <?php else: ?>
                                <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if($queue['durasi_layanan']): ?>
                                <span class="badge badge-info">
                                    <i class="fas fa-stopwatch mr-1"></i>
                                    <?php echo $queue['durasi_layanan']; ?> menit
                                </span>
                                <?php else: ?>
                                <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if($queue['status'] == 'selesai'): ?>
                                <span class="badge badge-success">
                                    <i class="fas fa-check mr-1"></i> Selesai
                                </span>
                                <?php elseif($queue['status'] == 'dipanggil'): ?>
                                <span class="badge badge-warning">
                                    <i class="fas fa-user-clock mr-1"></i> Sedang Dilayani
                                </span>
                                <?php elseif($queue['status'] == 'batal'): ?>
                                <span class="badge badge-danger">
                                    <i class="fas fa-times mr-1"></i> Batal
                                </span>
                                <?php else: ?>
                                <span class="badge badge-secondary">
                                    <i class="fas fa-hourglass-half mr-1"></i> Menunggu
                                </span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                        <?php else: ?>
                        <tr>
                            <td colspan="8" class="text-center py-5">
                                <div class="my-4 text-muted">
                                    <i class="fas fa-info-circle fa-3x mb-3"></i>
                                    <p class="font-weight-bold">Tidak ada data riwayat antrian untuk tanggal ini.</p>
                                    <p class="small">Silakan pilih tanggal lain atau reset filter untuk melihat semua data.</p>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div> <!-- Close container-fluid -->

<?php
// Include footer
include_once 'includes/footer.php';
?>

<!-- Add JavaScript for enhanced functionality -->
<script>
    $(document).ready(function() {
        // Initialize real-time clock
        function updateClock() {
            var now = new Date();
            var hours = now.getHours();
            var minutes = now.getMinutes();
            var seconds = now.getSeconds();

            // Add leading zeros
            hours = (hours < 10) ? "0" + hours : hours;
            minutes = (minutes < 10) ? "0" + minutes : minutes;
            seconds = (seconds < 10) ? "0" + seconds : seconds;

            // Display time
            $("#navbar-time").text(hours + ":" + minutes + ":" + seconds);

            // Update every second
            setTimeout(updateClock, 1000);
        }

        // Start clock
        updateClock();

        // Add hover effect to table rows
        $(".history-table tbody tr").hover(
            function() {
                $(this).addClass("bg-light");
            },
            function() {
                $(this).removeClass("bg-light");
            }
        );

        // Add animation to stats cards
        $(".stats-card").each(function(index) {
            $(this).css({
                "animation-delay": (index * 0.1) + "s",
                "animation": "fadeInUp 0.5s ease-out forwards"
            });
        });

        // Add animation keyframes
        $("<style>")
            .prop("type", "text/css")
            .html(`
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translate3d(0, 20px, 0);
                    }
                    to {
                        opacity: 1;
                        transform: translate3d(0, 0, 0);
                    }
                }
            `)
            .appendTo("head");


    });
</script>

<?php
// Get current page
$current_page = basename($_SERVER['PHP_SELF']);
?>

<div class="bg-dark" id="sidebar-wrapper">
    <div class="sidebar-heading d-flex align-items-center">
        <img src="../assets/img/logo.jpeg" alt="Bank BJB" style="max-width: 40px;">
        <span>Admin Panel</span>
    </div>
    <div class="list-group list-group-flush">
        <a href="index.php" class="list-group-item list-group-item-action <?php echo ($current_page === 'index.php') ? 'active' : ''; ?>">
            <i class="fas fa-tachometer-alt"></i> Dashboard
        </a>
        <a href="users.php" class="list-group-item list-group-item-action <?php echo ($current_page === 'users.php') ? 'active' : ''; ?>">
            <i class="fas fa-users"></i> Pengguna
        </a>
        <a href="services.php" class="list-group-item list-group-item-action <?php echo ($current_page === 'services.php') ? 'active' : ''; ?>">
            <i class="fas fa-cogs"></i> Layanan
        </a>
        <a href="counters.php" class="list-group-item list-group-item-action <?php echo ($current_page === 'counters.php') ? 'active' : ''; ?>">
            <i class="fas fa-desktop"></i> Counter
        </a>
        <a href="queue.php" class="list-group-item list-group-item-action <?php echo ($current_page === 'queue.php') ? 'active' : ''; ?>">
            <i class="fas fa-list-ol"></i> Antrian
        </a>
        <a href="reports.php" class="list-group-item list-group-item-action <?php echo ($current_page === 'reports.php') ? 'active' : ''; ?>">
            <i class="fas fa-chart-bar"></i> Laporan
        </a>
        <a href="settings.php" class="list-group-item list-group-item-action <?php echo ($current_page === 'settings.php') ? 'active' : ''; ?>">
            <i class="fas fa-cog"></i> Pengaturan
        </a>
        <a href="../logout.php" class="list-group-item list-group-item-action">
            <i class="fas fa-sign-out-alt"></i> Logout
        </a>
    </div>
</div>

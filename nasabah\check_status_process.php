<?php
// Process check status request
require_once '../includes/functions.php';
require_once '../includes/db.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if request is AJAX
if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
    // Get search parameter
    $search = isset($_POST['search']) ? trim($_POST['search']) : '';
    $response = ['success' => false, 'message' => '', 'data' => null];
    
    if (empty($search)) {
        $response['message'] = 'Silakan masukkan nomor booking atau nomor identitas.';
        echo json_encode($response);
        exit;
    }
    
    // Check if search is a booking ID
    $booking = null;
    if (preg_match('/^BK\d+$/', $search)) {
        // Search by booking ID
        $stmt = $conn->prepare("SELECT * FROM bookings WHERE booking_id = ?");
        $stmt->bind_param("s", $search);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $booking = $result->fetch_assoc();
        }
    } else {
        // Search by nomor identitas
        $stmt = $conn->prepare("SELECT * FROM bookings WHERE no_identitas = ? ORDER BY created_at DESC LIMIT 1");
        $stmt->bind_param("s", $search);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $booking = $result->fetch_assoc();
        }
    }
    
    if ($booking) {
        // Format data for response
        $status_text = '';
        $status_class = '';
        
        switch($booking['status']) {
            case 'pending':
                $status_text = 'Menunggu Konfirmasi';
                $status_class = 'pending';
                break;
            case 'confirmed':
                $status_text = 'Terkonfirmasi';
                $status_class = 'confirmed';
                break;
            case 'completed':
                $status_text = 'Selesai';
                $status_class = 'completed';
                break;
            case 'cancelled':
                $status_text = 'Dibatalkan';
                $status_class = 'cancelled';
                break;
        }
        
        $layanan_text = '';
        switch($booking['layanan']) {
            case 'teller':
                $layanan_text = 'Teller';
                break;
            case 'cs':
                $layanan_text = 'Customer Service';
                break;
            case 'kredit':
                $layanan_text = 'Kredit';
                break;
        }
        
        $response['success'] = true;
        $response['data'] = [
            'booking_id' => $booking['booking_id'],
            'created_at' => date('d M Y, H:i', strtotime($booking['created_at'])),
            'status' => $status_text,
            'status_class' => $status_class,
            'nama' => $booking['nama'],
            'layanan' => $layanan_text,
            'tanggal' => date('d F Y', strtotime($booking['tanggal'])),
            'waktu' => $booking['waktu'],
            'nomor_antrian' => $booking['nomor_antrian'],
            'can_cancel' => ($booking['status'] == 'pending' || $booking['status'] == 'confirmed') && strtotime($booking['tanggal']) > strtotime(date('Y-m-d'))
        ];
    } else {
        $response['message'] = 'Booking tidak ditemukan. Silakan periksa kembali nomor booking atau nomor identitas Anda.';
    }
    
    echo json_encode($response);
    exit;
} else {
    // If not AJAX request, redirect to check status page
    header("Location: check_status.php");
    exit;
}
?>

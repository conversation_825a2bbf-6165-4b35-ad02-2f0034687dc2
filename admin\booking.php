<?php
// Booking Online management page
$page_title = 'Manajemen Booking Online';
$active_menu = 'booking';
$is_admin = true;
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!is_logged_in() || !is_admin()) {
    redirect('../login.php');
}

// Get filter
$filter = isset($_GET['filter']) ? $_GET['filter'] : '';

// Build query based on filter
$where_clause = "WHERE a.booking_dari = 'online'";
if ($filter == 'pending') {
    $where_clause .= " AND a.status = 'menunggu'";
} elseif ($filter == 'processed') {
    $where_clause .= " AND a.status IN ('dipanggil', 'selesai')";
} elseif ($filter == 'cancelled') {
    $where_clause .= " AND a.status = 'batal'";
} elseif ($filter == 'today') {
    $where_clause .= " AND a.tanggal = CURDATE()";
}

// Process approve booking
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['approve_booking'])) {
    $booking_id = (int)$_POST['booking_id'];
    
    // Update booking status
    $sql = "UPDATE antrian SET status_verifikasi = 'terverifikasi' WHERE id = $booking_id";
    
    if (query($sql)) {
        $_SESSION['success_message'] = 'Booking berhasil disetujui.';
        redirect('booking.php');
    } else {
        $_SESSION['error_message'] = 'Gagal menyetujui booking.';
    }
}

// Process reject booking
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['reject_booking'])) {
    $booking_id = (int)$_POST['booking_id'];
    $alasan = sanitize($_POST['alasan']);
    
    // Update booking status
    $sql = "UPDATE antrian SET status = 'batal', catatan = '$alasan' WHERE id = $booking_id";
    
    if (query($sql)) {
        $_SESSION['success_message'] = 'Booking berhasil ditolak.';
        redirect('booking.php');
    } else {
        $_SESSION['error_message'] = 'Gagal menolak booking.';
    }
}

// Get all bookings
$sql = "SELECT a.*, n.nama, n.no_identitas, n.no_hp, p.nama_poli, l.nama_loket
        FROM antrian a
        LEFT JOIN nasabah n ON a.nasabah_id = n.id
        LEFT JOIN poli p ON a.poli_id = p.id
        LEFT JOIN loket l ON a.loket_id = l.id
        $where_clause
        ORDER BY a.tanggal DESC, a.id DESC";
$result = query($sql);
$bookings = fetch_all($result);

// Include header
include_once '../includes/header.php';
?>

<div class="row">
    <div class="col-md-12">
        <h1 class="mb-4">Manajemen Booking Online</h1>
        
        <?php include_once '../includes/alerts.php'; ?>
        
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-filter"></i> Filter Booking
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-12">
                        <a href="booking.php" class="btn <?php echo $filter == '' ? 'btn-primary' : 'btn-outline-primary'; ?> mb-2">
                            <i class="fas fa-list"></i> Semua
                        </a>
                        <a href="booking.php?filter=pending" class="btn <?php echo $filter == 'pending' ? 'btn-warning' : 'btn-outline-warning'; ?> mb-2">
                            <i class="fas fa-clock"></i> Menunggu
                        </a>
                        <a href="booking.php?filter=processed" class="btn <?php echo $filter == 'processed' ? 'btn-success' : 'btn-outline-success'; ?> mb-2">
                            <i class="fas fa-check"></i> Diproses
                        </a>
                        <a href="booking.php?filter=cancelled" class="btn <?php echo $filter == 'cancelled' ? 'btn-danger' : 'btn-outline-danger'; ?> mb-2">
                            <i class="fas fa-times"></i> Dibatalkan
                        </a>
                        <a href="booking.php?filter=today" class="btn <?php echo $filter == 'today' ? 'btn-info' : 'btn-outline-info'; ?> mb-2">
                            <i class="fas fa-calendar-day"></i> Hari Ini
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-calendar-check"></i> Daftar Booking Online
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover datatable">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Tanggal</th>
                                <th>Nomor Antrian</th>
                                <th>Nama</th>
                                <th>No. Identitas</th>
                                <th>No. HP</th>
                                <th>Layanan</th>
                                <th>Status</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if(count($bookings) > 0): ?>
                            <?php foreach($bookings as $booking): ?>
                            <tr>
                                <td><?php echo $booking['id']; ?></td>
                                <td><?php echo tanggal_indonesia($booking['tanggal']); ?></td>
                                <td><?php echo $booking['nomor_antrian']; ?></td>
                                <td><?php echo $booking['nama']; ?></td>
                                <td><?php echo $booking['no_identitas']; ?></td>
                                <td><?php echo $booking['no_hp']; ?></td>
                                <td><?php echo $booking['nama_poli']; ?></td>
                                <td>
                                    <?php if($booking['status'] == 'menunggu'): ?>
                                    <span class="badge badge-warning">Menunggu</span>
                                    <?php elseif($booking['status'] == 'dipanggil'): ?>
                                    <span class="badge badge-primary">Dipanggil</span>
                                    <?php elseif($booking['status'] == 'selesai'): ?>
                                    <span class="badge badge-success">Selesai</span>
                                    <?php else: ?>
                                    <span class="badge badge-danger">Batal</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-sm btn-info dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            Aksi
                                        </button>
                                        <div class="dropdown-menu">
                                            <a class="dropdown-item" href="#" data-toggle="modal" data-target="#detailModal<?php echo $booking['id']; ?>">
                                                <i class="fas fa-eye"></i> Detail
                                            </a>
                                            <?php if($booking['status'] == 'menunggu'): ?>
                                            <a class="dropdown-item" href="#" data-toggle="modal" data-target="#approveModal<?php echo $booking['id']; ?>">
                                                <i class="fas fa-check"></i> Setujui
                                            </a>
                                            <a class="dropdown-item text-danger" href="#" data-toggle="modal" data-target="#rejectModal<?php echo $booking['id']; ?>">
                                                <i class="fas fa-times"></i> Tolak
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            
                            <!-- Detail Modal -->
                            <div class="modal fade" id="detailModal<?php echo $booking['id']; ?>" tabindex="-1" role="dialog" aria-labelledby="detailModalLabel<?php echo $booking['id']; ?>" aria-hidden="true">
                                <div class="modal-dialog" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="detailModalLabel<?php echo $booking['id']; ?>">Detail Booking</h5>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <div class="modal-body">
                                            <table class="table table-bordered">
                                                <tr>
                                                    <th>ID Booking</th>
                                                    <td><?php echo $booking['id']; ?></td>
                                                </tr>
                                                <tr>
                                                    <th>Tanggal</th>
                                                    <td><?php echo tanggal_indonesia($booking['tanggal']); ?></td>
                                                </tr>
                                                <tr>
                                                    <th>Nomor Antrian</th>
                                                    <td><?php echo $booking['nomor_antrian']; ?></td>
                                                </tr>
                                                <tr>
                                                    <th>Nama</th>
                                                    <td><?php echo $booking['nama']; ?></td>
                                                </tr>
                                                <tr>
                                                    <th>No. Identitas</th>
                                                    <td><?php echo $booking['no_identitas']; ?></td>
                                                </tr>
                                                <tr>
                                                    <th>No. HP</th>
                                                    <td><?php echo $booking['no_hp']; ?></td>
                                                </tr>
                                                <tr>
                                                    <th>Layanan</th>
                                                    <td><?php echo $booking['nama_poli']; ?></td>
                                                </tr>
                                                <tr>
                                                    <th>Status</th>
                                                    <td>
                                                        <?php if($booking['status'] == 'menunggu'): ?>
                                                        <span class="badge badge-warning">Menunggu</span>
                                                        <?php elseif($booking['status'] == 'dipanggil'): ?>
                                                        <span class="badge badge-primary">Dipanggil</span>
                                                        <?php elseif($booking['status'] == 'selesai'): ?>
                                                        <span class="badge badge-success">Selesai</span>
                                                        <?php else: ?>
                                                        <span class="badge badge-danger">Batal</span>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                                <?php if($booking['status'] == 'batal' && !empty($booking['catatan'])): ?>
                                                <tr>
                                                    <th>Alasan Pembatalan</th>
                                                    <td><?php echo $booking['catatan']; ?></td>
                                                </tr>
                                                <?php endif; ?>
                                                <tr>
                                                    <th>Waktu Booking</th>
                                                    <td><?php echo date('d/m/Y H:i:s', strtotime($booking['created_at'])); ?></td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Approve Modal -->
                            <?php if($booking['status'] == 'menunggu'): ?>
                            <div class="modal fade" id="approveModal<?php echo $booking['id']; ?>" tabindex="-1" role="dialog" aria-labelledby="approveModalLabel<?php echo $booking['id']; ?>" aria-hidden="true">
                                <div class="modal-dialog" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="approveModalLabel<?php echo $booking['id']; ?>">Setujui Booking</h5>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <form action="booking.php" method="post">
                                            <div class="modal-body">
                                                <p>Apakah Anda yakin ingin menyetujui booking ini?</p>
                                                <input type="hidden" name="booking_id" value="<?php echo $booking['id']; ?>">
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                                                <button type="submit" name="approve_booking" class="btn btn-success">Setujui</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Reject Modal -->
                            <div class="modal fade" id="rejectModal<?php echo $booking['id']; ?>" tabindex="-1" role="dialog" aria-labelledby="rejectModalLabel<?php echo $booking['id']; ?>" aria-hidden="true">
                                <div class="modal-dialog" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="rejectModalLabel<?php echo $booking['id']; ?>">Tolak Booking</h5>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <form action="booking.php" method="post">
                                            <div class="modal-body">
                                                <div class="form-group">
                                                    <label for="alasan">Alasan Penolakan</label>
                                                    <textarea class="form-control" id="alasan" name="alasan" rows="3" required></textarea>
                                                </div>
                                                <input type="hidden" name="booking_id" value="<?php echo $booking['id']; ?>">
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                                                <button type="submit" name="reject_booking" class="btn btn-danger">Tolak</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                            <?php endforeach; ?>
                            <?php else: ?>
                            <tr>
                                <td colspan="9" class="text-center">Tidak ada data booking online.</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>

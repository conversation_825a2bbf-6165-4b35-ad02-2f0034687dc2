<?php
// Booking detail page
require_once '../includes/functions.php';
require_once '../includes/db.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'nasabah') {
    $_SESSION['error_messages'] = ["Anda harus login terlebih dahulu."];
    header("Location: login.php");
    exit;
}

// Check if booking ID is provided
if (!isset($_GET['id'])) {
    $_SESSION['error_messages'] = ["ID booking tidak valid."];
    header("Location: dashboard.php");
    exit;
}

$booking_id = $_GET['id'];
$user_id = $_SESSION['user_id'];

// Get booking details
$stmt = $conn->prepare("SELECT b.*, n.nama, n.no_identitas, n.email, n.no_hp 
                        FROM bookings b 
                        JOIN nasabah n ON b.nasabah_id = n.id 
                        WHERE b.booking_id = ? AND b.nasabah_id = ?");
$stmt->bind_param("si", $booking_id, $user_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    $_SESSION['error_messages'] = ["Booking tidak ditemukan atau Anda tidak memiliki akses."];
    header("Location: dashboard.php");
    exit;
}

$booking = $result->fetch_assoc();

// Format layanan name for display
$layanan_display = '';
switch ($booking['layanan']) {
    case 'teller':
        $layanan_display = 'Teller';
        break;
    case 'cs':
        $layanan_display = 'Customer Service';
        break;
    case 'kredit':
        $layanan_display = 'Kredit';
        break;
    default:
        $layanan_display = $booking['layanan'];
}

// Format date
$tanggal_display = date('d F Y', strtotime($booking['tanggal']));

// Format status badge
$status_badge_class = '';
$status_display = '';
switch ($booking['status']) {
    case 'pending':
        $status_badge_class = 'warning';
        $status_display = 'Menunggu Konfirmasi';
        break;
    case 'confirmed':
        $status_badge_class = 'success';
        $status_display = 'Terkonfirmasi';
        break;
    case 'completed':
        $status_badge_class = 'info';
        $status_display = 'Selesai';
        break;
    case 'cancelled':
        $status_badge_class = 'danger';
        $status_display = 'Dibatalkan';
        break;
    default:
        $status_badge_class = 'secondary';
        $status_display = $booking['status'];
}

// Page title
$page_title = "Detail Booking #" . $booking['booking_id'];
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Bank BJB</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-info-circle mr-2"></i> <?php echo $page_title; ?></h5>
                            <a href="dashboard.php" class="btn btn-sm btn-light">
                                <i class="fas fa-arrow-left mr-1"></i> Kembali
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h6 class="text-muted">Informasi Booking</h6>
                                <table class="table table-sm table-borderless">
                                    <tr>
                                        <th width="40%">ID Booking</th>
                                        <td><?php echo $booking['booking_id']; ?></td>
                                    </tr>
                                    <tr>
                                        <th>Status</th>
                                        <td><span class="badge badge-<?php echo $status_badge_class; ?>"><?php echo $status_display; ?></span></td>
                                    </tr>
                                    <tr>
                                        <th>Nomor Antrian</th>
                                        <td><strong class="text-primary"><?php echo $booking['nomor_antrian']; ?></strong></td>
                                    </tr>
                                    <tr>
                                        <th>Layanan</th>
                                        <td><?php echo $layanan_display; ?></td>
                                    </tr>
                                    <tr>
                                        <th>Tanggal Kunjungan</th>
                                        <td><?php echo $tanggal_display; ?></td>
                                    </tr>
                                    <tr>
                                        <th>Waktu</th>
                                        <td><?php echo $booking['waktu']; ?></td>
                                    </tr>
                                    <tr>
                                        <th>Tanggal Booking</th>
                                        <td><?php echo date('d/m/Y H:i', strtotime($booking['created_at'])); ?></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-muted">Informasi Nasabah</h6>
                                <table class="table table-sm table-borderless">
                                    <tr>
                                        <th width="40%">Nama</th>
                                        <td><?php echo $booking['nama']; ?></td>
                                    </tr>
                                    <tr>
                                        <th>No. Identitas</th>
                                        <td><?php echo $booking['no_identitas']; ?></td>
                                    </tr>
                                    <tr>
                                        <th>Email</th>
                                        <td><?php echo $booking['email']; ?></td>
                                    </tr>
                                    <tr>
                                        <th>No. HP</th>
                                        <td><?php echo $booking['no_hp']; ?></td>
                                    </tr>
                                </table>
                                
                                <?php if (!empty($booking['keterangan'])): ?>
                                <h6 class="text-muted mt-3">Keterangan</h6>
                                <p><?php echo $booking['keterangan']; ?></p>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <?php if (!empty($booking['file_identitas'])): ?>
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-muted">Dokumen Identitas</h6>
                                <div class="border p-3 text-center">
                                    <img src="../uploads/<?php echo $booking['file_identitas']; ?>" alt="Dokumen Identitas" class="img-fluid" style="max-height: 300px;">
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <div class="row">
                            <div class="col-12 text-right">
                                <a href="dashboard.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left mr-1"></i> Kembali
                                </a>
                                <a href="print_ticket.php?id=<?php echo $booking['booking_id']; ?>" class="btn btn-success" target="_blank">
                                    <i class="fas fa-print mr-1"></i> Cetak Tiket
                                </a>
                                <?php if ($booking['status'] == 'pending' || $booking['status'] == 'confirmed'): ?>
                                <a href="cancel_booking.php?id=<?php echo $booking['booking_id']; ?>" class="btn btn-danger" onclick="return confirm('Apakah Anda yakin ingin membatalkan booking ini?');">
                                    <i class="fas fa-times mr-1"></i> Batalkan Booking
                                </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

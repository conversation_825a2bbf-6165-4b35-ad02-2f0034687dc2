<?php
// Create test data for display
require_once 'includes/functions.php';

echo "<h1>Create Test Data for Display</h1>";

// Clear existing test data
$sql = "DELETE FROM antrian WHERE nomor_antrian LIKE 'TEST%'";
query($sql);

// Create test nasabah if not exists
$sql = "INSERT INTO nasabah (nama, no_identitas, email, password, no_hp, alamat, status_verifikasi) 
        VALUES 
        ('<PERSON>', '1234567890123456', '<EMAIL>', 'password', '081234567890', 'Jakarta', 'sudah'),
        ('Siti <PERSON>', '2345678901234567', '<EMAIL>', 'password', '081234567891', 'Bandung', 'sudah'),
        ('<PERSON><PERSON>', '3456789012345678', '<EMAIL>', 'password', '081234567892', 'Surabaya', 'sudah'),
        ('Dewi Lestari', '4567890123456789', '<EMAIL>', 'password', '081234567893', 'Medan', 'sudah')
        ON DUPLICATE KEY UPDATE nama = VALUES(nama)";
query($sql);

// Get nasabah IDs
$sql = "SELECT id, nama FROM nasabah WHERE email LIKE '%@test.com'";
$result = query($sql);
$nasabah_list = fetch_all($result);

// Create test antrian data
$test_data = [
    ['nomor' => 'TEST001', 'nasabah_id' => $nasabah_list[0]['id'], 'poli_id' => 1, 'jenis_layanan' => 'teller', 'loket_id' => 1, 'status' => 'dipanggil'],
    ['nomor' => 'TEST002', 'nasabah_id' => $nasabah_list[1]['id'], 'poli_id' => 2, 'jenis_layanan' => 'cs', 'loket_id' => 4, 'status' => 'dipanggil'],
    ['nomor' => 'TEST003', 'nasabah_id' => $nasabah_list[2]['id'], 'poli_id' => 3, 'jenis_layanan' => 'kredit', 'loket_id' => 6, 'status' => 'dipanggil'],
    ['nomor' => 'TEST004', 'nasabah_id' => $nasabah_list[3]['id'], 'poli_id' => 1, 'jenis_layanan' => 'teller', 'loket_id' => 2, 'status' => 'menunggu']
];

foreach ($test_data as $data) {
    $sql = "INSERT INTO antrian (nomor_antrian, tanggal, nasabah_id, poli_id, jenis_layanan, loket_id, status, waktu_dipanggil, booking_dari) 
            VALUES ('{$data['nomor']}', CURDATE(), {$data['nasabah_id']}, {$data['poli_id']}, '{$data['jenis_layanan']}', {$data['loket_id']}, '{$data['status']}', NOW(), 'lokal')";
    if (query($sql)) {
        echo "<p>Created test antrian: {$data['nomor']} for {$nasabah_list[array_search($data['nasabah_id'], array_column($nasabah_list, 'id'))]['nama']}</p>";
    } else {
        echo "<p>Error creating test antrian: {$data['nomor']}</p>";
    }
}

// Create test booking data
$sql = "INSERT INTO bookings (booking_id, nasabah_id, nama, no_identitas, email, no_hp, layanan, tanggal, waktu, nomor_antrian, status) 
        VALUES ('TESTBOOK001', {$nasabah_list[0]['id']}, 'Rudi Hartono', '5678901234567890', '<EMAIL>', '081234567894', 'teller', CURDATE(), '10:00', 'TEST005', 'confirmed')
        ON DUPLICATE KEY UPDATE nama = VALUES(nama)";
if (query($sql)) {
    echo "<p>Created test booking: TESTBOOK001 for Rudi Hartono</p>";
    
    // Create antrian for this booking
    $sql = "INSERT INTO antrian (nomor_antrian, tanggal, booking_id, poli_id, jenis_layanan, loket_id, status, waktu_dipanggil, booking_dari) 
            VALUES ('TEST005', CURDATE(), 'TESTBOOK001', 1, 'teller', 3, 'dipanggil', NOW(), 'online')";
    if (query($sql)) {
        echo "<p>Created test antrian: TEST005 for booking customer</p>";
    }
}

echo "<h2>Test Data Created Successfully!</h2>";
echo "<p><a href='display/index.php'>View Display</a></p>";
echo "<p><a href='debug_display.php'>Debug Display Data</a></p>";

// Show current test data
echo "<h2>Current Test Data</h2>";
$sql = "SELECT a.*, n.nama as nama_nasabah, b.nama as nama_booking, l.nama_loket
        FROM antrian a
        LEFT JOIN nasabah n ON a.nasabah_id = n.id
        LEFT JOIN bookings b ON a.booking_id = b.booking_id
        LEFT JOIN loket l ON a.loket_id = l.id
        WHERE a.nomor_antrian LIKE 'TEST%'
        ORDER BY a.id";
$result = query($sql);
if ($result && num_rows($result) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Nomor Antrian</th><th>Nama Customer</th><th>Loket</th><th>Status</th><th>Jenis Layanan</th></tr>";
    while ($row = fetch_assoc($result)) {
        $nama_customer = $row['nama_nasabah'] ?? $row['nama_booking'] ?? 'Unknown';
        echo "<tr>";
        echo "<td>" . $row['nomor_antrian'] . "</td>";
        echo "<td>" . $nama_customer . "</td>";
        echo "<td>" . ($row['nama_loket'] ?? 'N/A') . "</td>";
        echo "<td>" . $row['status'] . "</td>";
        echo "<td>" . $row['jenis_layanan'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}
?>

<?php
// Dashboard Antrian page
$page_title = 'Dashboard Antrian';
$no_header = true;
require_once '../includes/functions.php';

// Get active counters
$sql = "SELECT l.*, u.nama_lengkap
        FROM loket l
        LEFT JOIN users u ON l.user_id = u.id
        WHERE l.status = 'aktif'
        ORDER BY l.id";
$result = query($sql);
$counters = fetch_all($result);
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Antrian Bank BJB</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        body {
            background-color: #f0f8ff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .header {
            background-color: #0099cc;
            color: white;
            padding: 20px 0;
            text-align: center;
            margin-bottom: 30px;
        }
        .dashboard-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .dashboard-button {
            display: block;
            width: 100%;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
            border-radius: 10px;
            color: white;
            font-size: 18px;
            font-weight: bold;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .dashboard-button:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
            color: white;
            text-decoration: none;
        }
        .dashboard-button i {
            font-size: 24px;
            margin-right: 10px;
        }
        .btn-utama {
            background-color: #0099cc;
        }
        .btn-teller {
            background-color: #33cc33;
        }
        .btn-cs {
            background-color: #ff9900;
        }
        .btn-kredit {
            background-color: #cc3366;
        }
        .btn-back {
            background-color: #666666;
            margin-top: 40px;
        }
    </style>
</head>
<body>
    <div class="header">
        <img src="../assets/img/logo.jpeg" alt="Bank BJB" style="max-width: 100px; margin-bottom: 10px;">
        <h1>Dashboard Antrian</h1>
        <p>Silahkan pilih tampilan dashboard yang Anda butuhkan</p>
    </div>

    <div class="dashboard-container">
        <a href="index.php" class="dashboard-button btn-utama">
            <i class="fas fa-desktop"></i> Tampilan Utama
        </a>
        
        <a href="teller.php" class="dashboard-button btn-teller">
            <i class="fas fa-money-check-alt"></i> Tampilan Teller
        </a>
        
        <a href="customer_service.php" class="dashboard-button btn-cs">
            <i class="fas fa-headset"></i> Tampilan Customer Service
        </a>
        
        <a href="kredit.php" class="dashboard-button btn-kredit">
            <i class="fas fa-credit-card"></i> Tampilan Kredit
        </a>
        
        <a href="../index.php" class="dashboard-button btn-back">
            <i class="fas fa-arrow-left"></i> Kembali ke Halaman Utama
        </a>
    </div>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

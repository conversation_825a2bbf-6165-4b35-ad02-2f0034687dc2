<?php
// Add default staff data
require_once 'includes/functions.php';

echo "<h2>Adding Default Staff Data</h2>";

// Check if staff already exists
$sql = "SELECT COUNT(*) as count FROM users WHERE role = 'staff'";
$result = query($sql);
$row = fetch_assoc($result);
$staff_count = $row['count'];

echo "<p>Current staff count: " . $staff_count . "</p>";

if ($staff_count == 0) {
    echo "<p>No staff found. Adding default staff...</p>";
    
    // Add default staff members
    $default_staff = [
        [
            'username' => 'petugas1',
            'password' => password_hash('petugas123', PASSWORD_DEFAULT),
            'nama_lengkap' => 'Petugas Teller 1',
            'email' => '<EMAIL>',
            'role' => 'staff',
            'status' => 'aktif'
        ],
        [
            'username' => 'petugas2',
            'password' => password_hash('petugas123', PASSWORD_DEFAULT),
            'nama_lengkap' => 'Petugas Teller 2',
            'email' => '<EMAIL>',
            'role' => 'staff',
            'status' => 'aktif'
        ],
        [
            'username' => 'petugas3',
            'password' => password_hash('petugas123', PASSWORD_DEFAULT),
            'nama_lengkap' => 'Petugas Customer Service 1',
            'email' => '<EMAIL>',
            'role' => 'staff',
            'status' => 'aktif'
        ],
        [
            'username' => 'petugas4',
            'password' => password_hash('petugas123', PASSWORD_DEFAULT),
            'nama_lengkap' => 'Petugas Customer Service 2',
            'email' => '<EMAIL>',
            'role' => 'staff',
            'status' => 'aktif'
        ],
        [
            'username' => 'petugas5',
            'password' => password_hash('petugas123', PASSWORD_DEFAULT),
            'nama_lengkap' => 'Petugas Kredit 1',
            'email' => '<EMAIL>',
            'role' => 'staff',
            'status' => 'aktif'
        ]
    ];
    
    foreach ($default_staff as $staff) {
        $sql = "INSERT INTO users (username, password, nama_lengkap, email, role, status, created_at) 
                VALUES ('{$staff['username']}', '{$staff['password']}', '{$staff['nama_lengkap']}', 
                        '{$staff['email']}', '{$staff['role']}', '{$staff['status']}', NOW())";
        
        if (query($sql)) {
            echo "<p>✅ Added staff: " . $staff['nama_lengkap'] . " (username: " . $staff['username'] . ")</p>";
        } else {
            echo "<p>❌ Failed to add staff: " . $staff['nama_lengkap'] . "</p>";
        }
    }
    
    echo "<p><strong>Default staff added successfully!</strong></p>";
    echo "<p>Default login credentials for all staff: password = petugas123</p>";
    
} else {
    echo "<p>Staff already exists in database. No need to add default data.</p>";
}

// Check if loket table exists and has data
echo "<h3>Checking Loket Table:</h3>";
$sql = "SHOW TABLES LIKE 'loket'";
$result = query($sql);

if (num_rows($result) > 0) {
    $sql = "SELECT COUNT(*) as count FROM loket";
    $result = query($sql);
    $row = fetch_assoc($result);
    $loket_count = $row['count'];
    
    echo "<p>Current loket count: " . $loket_count . "</p>";
    
    if ($loket_count == 0) {
        echo "<p>No loket found. Adding default loket...</p>";
        
        // Add default loket
        $default_loket = [
            ['id' => 1, 'nama_loket' => 'Loket 1', 'jenis_layanan' => 'teller', 'status' => 'aktif'],
            ['id' => 2, 'nama_loket' => 'Loket 2', 'jenis_layanan' => 'teller', 'status' => 'aktif'],
            ['id' => 3, 'nama_loket' => 'Loket 3', 'jenis_layanan' => 'teller', 'status' => 'aktif'],
            ['id' => 4, 'nama_loket' => 'Loket 4', 'jenis_layanan' => 'cs', 'status' => 'aktif'],
            ['id' => 5, 'nama_loket' => 'Loket 5', 'jenis_layanan' => 'cs', 'status' => 'aktif'],
            ['id' => 6, 'nama_loket' => 'Loket 6', 'jenis_layanan' => 'kredit', 'status' => 'aktif'],
            ['id' => 7, 'nama_loket' => 'Loket 7', 'jenis_layanan' => 'kredit', 'status' => 'aktif']
        ];
        
        foreach ($default_loket as $loket) {
            $sql = "INSERT INTO loket (id, nama_loket, jenis_layanan, status, created_at) 
                    VALUES ({$loket['id']}, '{$loket['nama_loket']}', '{$loket['jenis_layanan']}', 
                            '{$loket['status']}', NOW())";
            
            if (query($sql)) {
                echo "<p>✅ Added loket: " . $loket['nama_loket'] . " (" . $loket['jenis_layanan'] . ")</p>";
            } else {
                echo "<p>❌ Failed to add loket: " . $loket['nama_loket'] . "</p>";
            }
        }
    }
} else {
    echo "<p>❌ Loket table does not exist. Please create it first.</p>";
}

echo "<br><br><a href='check_staff_data.php'>Check Staff Data</a> | <a href='admin/petugas.php'>Go to Petugas Management</a>";
?>

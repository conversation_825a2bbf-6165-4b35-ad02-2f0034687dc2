<?php
// Customer queue history page
$page_title = 'Riwayat Antrian Nasabah';
$active_menu = 'nasabah';
$is_admin = true;
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!is_logged_in() || !is_admin()) {
    redirect('../login.php');
}

// Get customer ID
$customer_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Validate customer
if ($customer_id <= 0) {
    $_SESSION['error_message'] = 'ID nasabah tidak valid.';
    redirect('nasabah.php');
}

// Get customer details
$sql = "SELECT * FROM nasabah WHERE id = $customer_id";
$result = query($sql);

if (num_rows($result) == 0) {
    $_SESSION['error_message'] = 'Nasabah tidak ditemukan.';
    redirect('nasabah.php');
}

$customer = fetch_assoc($result);

// Get customer queues
$sql = "SELECT a.*, p.nama_poli, l.nama_loket 
        FROM antrian a 
        LEFT JOIN poli p ON a.poli_id = p.id 
        LEFT JOIN loket l ON a.loket_id = l.id 
        WHERE a.nasabah_id = $customer_id 
        ORDER BY a.tanggal DESC, a.id DESC";
$result = query($sql);
$queues = fetch_all($result);

// Include header
include_once '../includes/header.php';
?>

<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Riwayat Antrian Nasabah</h1>
            
            <a href="nasabah.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
        </div>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <i class="fas fa-user"></i> Detail Nasabah
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <table class="table table-borderless">
                    <tr>
                        <th width="150">ID</th>
                        <td><?php echo $customer['id']; ?></td>
                    </tr>
                    <tr>
                        <th>Nama</th>
                        <td><?php echo $customer['nama']; ?></td>
                    </tr>
                    <tr>
                        <th>No. Identitas</th>
                        <td><?php echo $customer['no_identitas']; ?></td>
                    </tr>
                    <tr>
                        <th>Jenis Identitas</th>
                        <td>
                            <?php 
                            if ($customer['jenis_identitas'] == 'ktp') {
                                echo 'KTP';
                            } else {
                                echo 'Buku Tabungan';
                            }
                            ?>
                        </td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <table class="table table-borderless">
                    <tr>
                        <th width="150">No. HP</th>
                        <td><?php echo $customer['no_hp'] ?: '-'; ?></td>
                    </tr>
                    <tr>
                        <th>Email</th>
                        <td><?php echo $customer['email'] ?: '-'; ?></td>
                    </tr>
                    <tr>
                        <th>Status Verifikasi</th>
                        <td>
                            <?php if($customer['status_verifikasi'] == 'belum'): ?>
                            <span class="badge badge-warning">Menunggu Verifikasi</span>
                            <?php elseif($customer['status_verifikasi'] == 'terverifikasi'): ?>
                            <span class="badge badge-success">Terverifikasi</span>
                            <?php else: ?>
                            <span class="badge badge-danger">Ditolak</span>
                            <?php endif; ?>
                            
                            <?php if($customer['is_blocked']): ?>
                            <span class="badge badge-dark">Diblokir</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <th>Terdaftar Pada</th>
                        <td><?php echo date('d/m/Y H:i', strtotime($customer['created_at'])); ?></td>
                    </tr>
                </table>
            </div>
        </div>
        
        <?php if($customer['file_identitas']): ?>
        <div class="text-center mt-3">
            <button class="btn btn-info btn-sm" data-toggle="modal" data-target="#previewModal" data-image="../uploads/identitas/<?php echo $customer['file_identitas']; ?>">
                <i class="fas fa-eye"></i> Lihat Identitas
            </button>
        </div>
        <?php endif; ?>
    </div>
</div>

<div class="card">
    <div class="card-header bg-primary text-white">
        <i class="fas fa-list"></i> Riwayat Antrian
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nomor Antrian</th>
                        <th>Tanggal</th>
                        <th>Layanan</th>
                        <th>Loket</th>
                        <th>Status</th>
                        <th>Waktu Booking</th>
                        <th>Waktu Dipanggil</th>
                        <th>Waktu Selesai</th>
                        <th>Sumber</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if(count($queues) > 0): ?>
                    <?php foreach($queues as $queue): ?>
                    <tr>
                        <td><?php echo $queue['id']; ?></td>
                        <td><strong><?php echo $queue['nomor_antrian']; ?></strong></td>
                        <td><?php echo date('d/m/Y', strtotime($queue['tanggal'])); ?></td>
                        <td><?php echo $queue['nama_poli']; ?></td>
                        <td><?php echo $queue['nama_loket'] ?: '-'; ?></td>
                        <td>
                            <?php if($queue['status'] == 'menunggu'): ?>
                            <span class="badge badge-warning">Menunggu</span>
                            <?php elseif($queue['status'] == 'dipanggil'): ?>
                            <span class="badge badge-primary">Dipanggil</span>
                            <?php elseif($queue['status'] == 'selesai'): ?>
                            <span class="badge badge-success">Selesai</span>
                            <?php else: ?>
                            <span class="badge badge-danger">Batal</span>
                            <?php endif; ?>
                        </td>
                        <td><?php echo date('d/m/Y H:i', strtotime($queue['waktu_booking'])); ?></td>
                        <td><?php echo $queue['waktu_dipanggil'] ? date('d/m/Y H:i', strtotime($queue['waktu_dipanggil'])) : '-'; ?></td>
                        <td><?php echo $queue['waktu_selesai'] ? date('d/m/Y H:i', strtotime($queue['waktu_selesai'])) : '-'; ?></td>
                        <td>
                            <?php if($queue['booking_dari'] == 'online'): ?>
                            <span class="badge badge-info">Online</span>
                            <?php else: ?>
                            <span class="badge badge-secondary">Lokal</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                    <?php else: ?>
                    <tr>
                        <td colspan="10" class="text-center">Tidak ada data antrian.</td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1" role="dialog" aria-labelledby="previewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previewModalLabel">Preview Identitas</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <img src="" alt="Preview Identitas" class="img-fluid" id="previewImage">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>

<script>
$(document).ready(function() {
    // Preview modal
    $('#previewModal').on('show.bs.modal', function (event) {
        var button = $(event.relatedTarget);
        var image = button.data('image');
        var modal = $(this);
        
        modal.find('#previewImage').attr('src', image);
    });
});
</script>

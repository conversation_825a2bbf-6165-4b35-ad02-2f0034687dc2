<?php
// Feedback & Rating page
$page_title = 'Feedback & Rating';
$active_menu = 'feedback';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    redirect('../login.php');
}

// Handle status update
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_status'])) {
    $feedback_id = (int)$_POST['feedback_id'];
    $new_status = sanitize($_POST['status']);
    
    $sql = "UPDATE feedback SET status = '$new_status' WHERE id = $feedback_id";
    if (query($sql)) {
        $_SESSION['success_message'] = 'Status feedback berhasil diupdate!';
    } else {
        $_SESSION['error_message'] = 'Gagal mengupdate status feedback!';
    }
    redirect('feedback.php');
}

// Get filter parameters
$filter_rating = $_GET['rating'] ?? '';
$filter_kategori = $_GET['kategori'] ?? '';
$filter_status = $_GET['status'] ?? '';
$start_date = $_GET['start_date'] ?? date('Y-m-d', strtotime('-30 days'));
$end_date = $_GET['end_date'] ?? date('Y-m-d');

// Build WHERE clause
$where_conditions = ["f.tanggal_feedback BETWEEN '$start_date' AND '$end_date'"];
if (!empty($filter_rating)) {
    $where_conditions[] = "f.rating = '$filter_rating'";
}
if (!empty($filter_kategori)) {
    $where_conditions[] = "f.kategori_feedback = '$filter_kategori'";
}
if (!empty($filter_status)) {
    $where_conditions[] = "f.status = '$filter_status'";
}
$where_clause = implode(' AND ', $where_conditions);

// Get feedback data
$sql = "SELECT f.*, l.nama_loket
        FROM feedback f
        LEFT JOIN loket l ON f.loket_id = l.id
        WHERE $where_clause
        ORDER BY f.created_at DESC";
$result = query($sql);
$feedback_data = fetch_all($result);

// Get statistics
$sql_stats = "SELECT 
                COUNT(*) as total_feedback,
                AVG(rating) as avg_rating,
                COUNT(CASE WHEN rating = 5 THEN 1 END) as rating_5,
                COUNT(CASE WHEN rating = 4 THEN 1 END) as rating_4,
                COUNT(CASE WHEN rating = 3 THEN 1 END) as rating_3,
                COUNT(CASE WHEN rating = 2 THEN 1 END) as rating_2,
                COUNT(CASE WHEN rating = 1 THEN 1 END) as rating_1,
                COUNT(CASE WHEN status = 'baru' THEN 1 END) as feedback_baru
              FROM feedback f
              WHERE $where_clause";
$result_stats = query($sql_stats);
$stats = fetch_assoc($result_stats);

include 'includes/header.php';
?>

        <!-- Main Content -->
        <div class="main-content">
            <div class="container-fluid">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-star me-2"></i>Feedback & Rating</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-success" onclick="exportFeedbackToExcel()">
                        <i class="fas fa-file-excel me-1"></i>Export Excel
                    </button>
                </div>
            </div>

            <?php if (isset($_SESSION['success_message'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $_SESSION['success_message'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php unset($_SESSION['success_message']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['error_message'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $_SESSION['error_message'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php unset($_SESSION['error_message']); ?>
            <?php endif; ?>

            <!-- Filter Form -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filter Feedback</h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-2">
                            <label for="rating" class="form-label">Rating</label>
                            <select class="form-select" id="rating" name="rating">
                                <option value="">Semua Rating</option>
                                <option value="5" <?= $filter_rating == '5' ? 'selected' : '' ?>>⭐⭐⭐⭐⭐ (5)</option>
                                <option value="4" <?= $filter_rating == '4' ? 'selected' : '' ?>>⭐⭐⭐⭐ (4)</option>
                                <option value="3" <?= $filter_rating == '3' ? 'selected' : '' ?>>⭐⭐⭐ (3)</option>
                                <option value="2" <?= $filter_rating == '2' ? 'selected' : '' ?>>⭐⭐ (2)</option>
                                <option value="1" <?= $filter_rating == '1' ? 'selected' : '' ?>>⭐ (1)</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="kategori" class="form-label">Kategori</label>
                            <select class="form-select" id="kategori" name="kategori">
                                <option value="">Semua Kategori</option>
                                <option value="pelayanan" <?= $filter_kategori == 'pelayanan' ? 'selected' : '' ?>>Pelayanan</option>
                                <option value="fasilitas" <?= $filter_kategori == 'fasilitas' ? 'selected' : '' ?>>Fasilitas</option>
                                <option value="kecepatan" <?= $filter_kategori == 'kecepatan' ? 'selected' : '' ?>>Kecepatan</option>
                                <option value="keramahan" <?= $filter_kategori == 'keramahan' ? 'selected' : '' ?>>Keramahan</option>
                                <option value="lainnya" <?= $filter_kategori == 'lainnya' ? 'selected' : '' ?>>Lainnya</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">Semua Status</option>
                                <option value="baru" <?= $filter_status == 'baru' ? 'selected' : '' ?>>Baru</option>
                                <option value="dibaca" <?= $filter_status == 'dibaca' ? 'selected' : '' ?>>Dibaca</option>
                                <option value="ditindaklanjuti" <?= $filter_status == 'ditindaklanjuti' ? 'selected' : '' ?>>Ditindaklanjuti</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="start_date" class="form-label">Tanggal Mulai</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="<?= $start_date ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="end_date" class="form-label">Tanggal Akhir</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="<?= $end_date ?>">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>Filter
                                </button>
                                <a href="feedback.php" class="btn btn-secondary">
                                    <i class="fas fa-refresh me-1"></i>Reset
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?= $stats['total_feedback'] ?></h4>
                                    <p class="mb-0">Total Feedback</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-comments fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?= number_format($stats['avg_rating'], 1) ?> ⭐</h4>
                                    <p class="mb-0">Rating Rata-rata</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-star fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?= $stats['feedback_baru'] ?></h4>
                                    <p class="mb-0">Feedback Baru</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-bell fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?= $stats['rating_5'] ?></h4>
                                    <p class="mb-0">Rating 5 Bintang</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-thumbs-up fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Rating Distribution -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Distribusi Rating</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php for ($i = 5; $i >= 1; $i--): ?>
                        <div class="col-md-2">
                            <div class="text-center">
                                <h6><?= str_repeat('⭐', $i) ?></h6>
                                <h4 class="text-primary"><?= $stats["rating_$i"] ?></h4>
                                <small class="text-muted"><?= $stats['total_feedback'] > 0 ? round(($stats["rating_$i"] / $stats['total_feedback']) * 100, 1) : 0 ?>%</small>
                            </div>
                        </div>
                        <?php endfor; ?>
                    </div>
                </div>
            </div>

            <!-- Feedback Data Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-table me-2"></i>Data Feedback</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="feedbackTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>No</th>
                                    <th>Tanggal</th>
                                    <th>Nama Customer</th>
                                    <th>Rating</th>
                                    <th>Kategori</th>
                                    <th>Komentar</th>
                                    <th>Loket</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (count($feedback_data) > 0): ?>
                                    <?php foreach ($feedback_data as $index => $feedback): ?>
                                    <tr>
                                        <td><?= $index + 1 ?></td>
                                        <td><?= date('d/m/Y', strtotime($feedback['tanggal_feedback'])) ?></td>
                                        <td>
                                            <strong><?= $feedback['nama_customer'] ?></strong><br>
                                            <small class="text-muted"><?= $feedback['email_customer'] ?></small>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning text-dark">
                                                <?= str_repeat('⭐', $feedback['rating']) ?> (<?= $feedback['rating'] ?>)
                                            </span>
                                        </td>
                                        <td>
                                            <?php
                                            $kategori_badges = [
                                                'pelayanan' => 'bg-primary',
                                                'fasilitas' => 'bg-info',
                                                'kecepatan' => 'bg-warning',
                                                'keramahan' => 'bg-success',
                                                'lainnya' => 'bg-secondary'
                                            ];
                                            $badge_class = $kategori_badges[$feedback['kategori_feedback']] ?? 'bg-secondary';
                                            ?>
                                            <span class="badge <?= $badge_class ?>"><?= ucfirst($feedback['kategori_feedback']) ?></span>
                                        </td>
                                        <td>
                                            <div style="max-width: 200px;">
                                                <?= strlen($feedback['komentar']) > 100 ? substr($feedback['komentar'], 0, 100) . '...' : $feedback['komentar'] ?>
                                                <?php if (strlen($feedback['komentar']) > 100): ?>
                                                    <br><small><a href="#" onclick="showFullComment('<?= addslashes($feedback['komentar']) ?>')">Lihat selengkapnya</a></small>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td><?= $feedback['nama_loket'] ?? '-' ?></td>
                                        <td>
                                            <?php
                                            $status_badges = [
                                                'baru' => 'bg-danger',
                                                'dibaca' => 'bg-warning',
                                                'ditindaklanjuti' => 'bg-success'
                                            ];
                                            $status_class = $status_badges[$feedback['status']] ?? 'bg-secondary';
                                            ?>
                                            <span class="badge <?= $status_class ?>"><?= ucfirst($feedback['status']) ?></span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="updateStatus(<?= $feedback['id'] ?>, 'dibaca')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-success" onclick="updateStatus(<?= $feedback['id'] ?>, 'ditindaklanjuti')">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="9" class="text-center">Tidak ada data feedback untuk periode yang dipilih</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

<!-- Modal for full comment -->
<div class="modal fade" id="commentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Komentar Lengkap</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p id="fullComment"></p>
            </div>
        </div>
    </div>
</div>

<!-- Hidden form for status update -->
<form id="statusForm" method="POST" style="display: none;">
    <input type="hidden" name="update_status" value="1">
    <input type="hidden" name="feedback_id" id="feedbackId">
    <input type="hidden" name="status" id="newStatus">
</form>

<script>
function showFullComment(comment) {
    document.getElementById('fullComment').textContent = comment;
    new bootstrap.Modal(document.getElementById('commentModal')).show();
}

function updateStatus(feedbackId, status) {
    if (confirm('Apakah Anda yakin ingin mengubah status feedback ini?')) {
        document.getElementById('feedbackId').value = feedbackId;
        document.getElementById('newStatus').value = status;
        document.getElementById('statusForm').submit();
    }
}

function exportFeedbackToExcel() {
    const table = document.getElementById('feedbackTable');
    const wb = XLSX.utils.table_to_book(table, {sheet: "Feedback Data"});
    const filename = `Feedback_Data_${new Date().toISOString().slice(0,10)}.xlsx`;
    XLSX.writeFile(wb, filename);
}
</script>

<!-- Include SheetJS for Excel export -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

<?php include 'includes/footer.php'; ?>

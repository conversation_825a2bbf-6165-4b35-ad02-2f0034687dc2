/* Custom sidebar overrides for staff pages */
#sidebar {
    min-width: 250px !important;
    max-width: 250px !important;
    background: linear-gradient(180deg, #1a6a83 0%, #0d3c4e 100%) !important;
    color: #fff !important;
    transition: all 0.3s !important;
    height: 100vh !important;
    position: fixed !important;
    z-index: 999 !important;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.2) !important;
    overflow-y: auto !important;
    padding: 0 !important;
    font-family: Arial, sans-serif !important;
}

#sidebar .sidebar-header {
    padding: 30px 20px !important;
    text-align: center !important;
    background-color: transparent !important;
    border-bottom: none !important;
    margin-bottom: 0 !important;
}

#sidebar .sidebar-header .logo-container {
    width: 100% !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    margin-bottom: 20px !important;
    padding: 15px !important;
    background-color: #fff !important;
    border-radius: 10px !important;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2) !important;
    max-width: 200px !important;
    margin-left: auto !important;
    margin-right: auto !important;
    position: relative !important;
    z-index: 1 !important;
    border: 5px solid rgba(0, 0, 0, 0.1) !important;
}

#sidebar .sidebar-header .logo-bjb {
    width: 100% !important;
    max-width: 180px !important;
    height: auto !important;
    object-fit: contain !important;
    display: block !important;
    margin: 0 auto !important;
}

#sidebar .sidebar-header h4 {
    color: white !important;
    font-weight: bold !important;
    margin: 15px 0 !important;
    font-size: 1.2rem !important;
    text-align: center !important;
    line-height: 1.4 !important;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3) !important;
    letter-spacing: 0.5px !important;
}

#sidebar .sidebar-header p {
    color: white !important;
    margin: 15px auto !important;
    font-size: 0.9rem !important;
    background-color: rgba(0, 0, 0, 0.3) !important;
    padding: 8px 15px !important;
    border-radius: 30px !important;
    display: inline-block !important;
    font-weight: 500 !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

#sidebar ul.components {
    padding: 0 !important;
    margin: 0 !important;
    list-style: none !important;
}

#sidebar ul li {
    margin-bottom: 10px !important;
    padding: 0 15px !important;
    position: relative !important;
    list-style: none !important;
}

#sidebar ul li a {
    padding: 12px 15px !important;
    font-size: 1rem !important;
    display: flex !important;
    align-items: center !important;
    color: rgba(255, 255, 255, 0.9) !important;
    text-decoration: none !important;
    transition: all 0.3s !important;
    border-radius: 10px !important;
    position: relative !important;
    overflow: hidden !important;
    border-left: 4px solid transparent !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    font-weight: 500 !important;
    letter-spacing: 0.3px !important;
    background-color: rgba(255, 255, 255, 0.05) !important;
}

#sidebar ul li a i {
    margin-right: 15px !important;
    width: 40px !important;
    height: 40px !important;
    line-height: 40px !important;
    text-align: center !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
    border-radius: 50% !important;
    transition: all 0.3s !important;
    font-size: 1.1rem !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
    border: 1px solid rgba(255, 255, 255, 0.05) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Fix for Font Awesome icons */
#sidebar ul li a i:before {
    display: inline-block !important;
    text-align: center !important;
    width: 100% !important;
    margin: 0 !important;
}

#sidebar ul li a i.fa-door-open {
    background-color: #ffc107 !important;
    color: #000 !important;
}

#sidebar ul li a i.fa-history {
    background-color: #17a2b8 !important;
    color: #fff !important;
}

#sidebar ul li a i.fa-sign-out-alt {
    background-color: #dc3545 !important;
    color: #fff !important;
}

/* Hover effect for all menu items */
#sidebar ul li a:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    transform: translateX(5px) !important;
}

#sidebar ul li a:hover i {
    transform: scale(1.05) !important;
}

#sidebar ul li.active > a {
    color: #fff !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2) !important;
    border-left: 4px solid #ffc107 !important;
    transform: translateX(5px) !important;
}

#sidebar ul li.active > a i {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
    transform: scale(1.05) !important;
}

#sidebar ul li:last-child {
    margin-top: 20px !important;
    border-top: 1px solid rgba(255, 255, 255, 0.05) !important;
    padding-top: 20px !important;
}

/* Additional styling for better appearance */
#sidebar::-webkit-scrollbar {
    width: 5px !important;
}

#sidebar::-webkit-scrollbar-track {
    background: transparent !important;
}

#sidebar::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2) !important;
    border-radius: 10px !important;
}
